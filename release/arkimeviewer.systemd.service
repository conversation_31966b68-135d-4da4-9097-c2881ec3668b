[Unit]
Description=Arkime Viewer
After=network.target opensearch.service elasticsearch.service

[Service]
Type=simple
Restart=on-failure
StandardOutput=tty
EnvironmentFile=-BUILD_ARKIME_INSTALL_DIR/etc/viewer.env
# Modify BUILD_ARKIME_INSTALL_DIR/etc/viewer.env instead of ExecStart
ExecStart=/bin/sh -c 'BUILD_ARKIME_INSTALL_DIR/bin/node viewer.js -c BUILD_ARKIME_INSTALL_DIR/etc/config.ini ${OPTIONS} >> BUILD_ARKIME_INSTALL_DIR/logs/viewer.log 2>&1'
WorkingDirectory=BUILD_ARKIME_INSTALL_DIR/viewer

[Install]
WantedBy=multi-user.target
