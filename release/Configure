#!/bin/bash
# Simple capital C Configure script for rpm/deb, like the old days

if [ "$1" == "--help" ]; then
    echo "Configure (--wise|--parliament|--cont3xt|)  = Only 1 option can be used"
    echo "--cont3xt      = install and start Cont3xt"
    echo "--wise         = install and start Wise"
    echo "--parliament   = install and start Parliament"
    echo "[no option]    = install Arkime capture and viewer"
    echo "--help         = this help"
    exit 0
fi

if [ "$(id -u)" != "0" ]; then
    echo "This script must be run as root"
    exit 1
fi

ARKIME_NAME=BUILD_ARKIME_NAME
if [ "$ARKIME_NAME" == "BUILD_ARKIME_NAME" ]; then
    ARKIME_NAME=arkime
fi

ARKIME_INSTALL_DIR=BUILD_ARKIME_INSTALL_DIR
if [ "$ARKIME_INSTALL_DIR" == "BUILD_ARKIME_""INSTALL_DIR" ]; then
ARKIME_INSTALL_DIR=/opt/$ARKIME_NAME
fi

# Make sure logs directory exists
mkdir -p $ARKIME_INSTALL_DIR/logs

if [ "$1" == "--wise" ]; then
    if [ ! -f "$ARKIME_INSTALL_DIR/etc/wise.ini" ]; then
        echo "Installing sample $ARKIME_INSTALL_DIR/etc/wise.ini"
        sed -e "s,ARKIME_ELASTICSEARCH,${ARKIME_ELASTICSEARCH},g" -e "s,ARKIME_INSTALL_DIR,${ARKIME_INSTALL_DIR},g" < $ARKIME_INSTALL_DIR/etc/wise.ini.sample > $ARKIME_INSTALL_DIR/etc/wise.ini
    else
        echo "Not overwriting $ARKIME_INSTALL_DIR/etc/wise.ini, delete and run again if update required (usually not), or edit by hand"
        sleep 1
    fi

    if [ -d "/etc/systemd" ] && [ -x "/bin/systemctl" ]; then
        echo "Enabling & Starting wise systemd files"
        systemctl enable arkimewise
        systemctl start arkimewise
    else
        echo "No startup scripts created for wise"
    fi


    exit 0;
fi

if [ "$1" == "--parliament" ]; then
    if [ -d "/etc/systemd" ] && [ -x "/bin/systemctl" ]; then
        echo "Enabling & Starting parliament systemd files"
        systemctl enable arkimeparliament
        systemctl start arkimeparliament
    else
        echo "No startup scripts created for parliament"
    fi

    exit 0;
fi

if [ "$1" == "--cont3xt" ]; then

    if [ -z "$ARKIME_ELASTICSEARCH" ]; then
        echo -n "OpenSearch/Elasticsearch server URL [https://localhost:9200] "
        read -r ARKIME_ELASTICSEARCH
    fi
    if [ -z "$ARKIME_ELASTICSEARCH" ]; then ARKIME_ELASTICSEARCH="https://localhost:9200"; fi

    echo -n "OpenSearch/Elasticsearch user [empty is no user] "
    read -r ARKIME_ELASTICSEARCH_USER

    if [ ! -z "$ARKIME_ELASTICSEARCH_USER" ]; then
        echo -n "OpenSearch/Elasticsearch password [empty is no password] "
        read -r ARKIME_ELASTICSEARCH_PASSWORD
    fi

    while [ -z "$ARKIME_PASSWORD" ]; do
        echo -n "Password to encrypt users, keys, etc. Should be the same across tools [must create one] "
        read -r ARKIME_PASSWORD
    done
    ARKIME_PASSWORD=${ARKIME_PASSWORD//\//\\/}


    if [ ! -f "$ARKIME_INSTALL_DIR/etc/cont3xt.ini" ]; then
        echo "Installing sample $ARKIME_INSTALL_DIR/etc/cont3xt.ini"
        sed -e "s,ARKIME_ELASTICSEARCH,${ARKIME_ELASTICSEARCH},g" -e "s,ARKIME_INSTALL_DIR,${ARKIME_INSTALL_DIR},g" -e "s/ARKIME_PASSWORD/${ARKIME_PASSWORD}/g" < $ARKIME_INSTALL_DIR/etc/cont3xt.ini.sample > $ARKIME_INSTALL_DIR/etc/cont3xt.ini

        if [ ! -z "$ARKIME_ELASTICSEARCH_USER" ]; then
            sed -i "" -e "s/# elasticsearchBasicAuth=/elasticsearchBasicAuth=${ARKIME_ELASTICSEARCH_USER}:${ARKIME_ELASTICSEARCH_PASSWORD}/g" $ARKIME_INSTALL_DIR/etc/cont3xt.ini
        fi
    else
        echo "Not overwriting $ARKIME_INSTALL_DIR/etc/cont3xt.ini, delete and run again if update required (usually not), or edit by hand"
        sleep 1
    fi

    if [ -d "/etc/systemd" ] && [ -x "/bin/systemctl" ]; then
        echo "Enabling & Starting cont3xt systemd files"
        systemctl enable arkimecont3xt
        systemctl start arkimecont3xt
    else
        echo "No startup scripts created for cont3xt"
    fi


    exit 0;
fi

################################################################################
### Ask config questions
if [ -z "$ARKIME_INTERFACE" ]; then
    echo -n "Found interfaces: "
    if [ ! -f /sbin/ifconfig ]; then
        ip -o link | cut -f2 -d: | tr '\n' ' ' | sed 's/ \+/ /ig' | sed 's/ //1' | sed 's/[[:blank:]]*$//' | tr ' ' ';' | sed 's/$/\n/'
    else
        /sbin/ifconfig | grep "^[a-z]" | cut -d: -f1 | cut -d" " -f1 | paste -s -d";"
    fi
    echo -n "Semicolon ';' seperated list of interfaces to monitor [eth1] "
    read -r ARKIME_INTERFACE
fi
if [ -z "$ARKIME_INTERFACE" ]; then ARKIME_INTERFACE="eth1"; fi

# If Arch don't offer demo install, if variable not set then ask the question
if [ -f "/etc/arch-release" ]; then
    ARKIME_INSTALLELASTICSEARCH=no
elif [ -z "$ARKIME_INSTALLELASTICSEARCH" ]; then
    ARKIME_INSTALLELASTICSEARCH=not-set
fi
until [ "$ARKIME_INSTALLELASTICSEARCH" == "yes" ] || [ "$ARKIME_INSTALLELASTICSEARCH" == "no" ] || [ "$ARKIME_INSTALLELASTICSEARCH" == "" ]; do
    echo -n "Install Elasticsearch server locally for demo, must have at least 3G of memory, NOT recommended for production use (yes or no) [no] "
    read -r ARKIME_INSTALLELASTICSEARCH
done

if [ "$ARKIME_INSTALLELASTICSEARCH" == "yes" ]; then
    ARKIME_ELASTICSEARCH="http://localhost:9200"
    which java
    JAVA_VAL=$?

    if [ $JAVA_VAL -ne 0 ]; then
        echo "java command not found, make sure java is installed and in the path and run again"
    fi
else
    if [ -z "$ARKIME_ELASTICSEARCH" ]; then
        echo -n "OpenSearch/Elasticsearch server URL [https://localhost:9200] "
        read -r ARKIME_ELASTICSEARCH
    fi
    if [ -z "$ARKIME_ELASTICSEARCH" ]; then ARKIME_ELASTICSEARCH="https://localhost:9200"; fi
fi

echo -n "OpenSearch/Elasticsearch user [empty is no user] "
read -r ARKIME_ELASTICSEARCH_USER

if [ ! -z "$ARKIME_ELASTICSEARCH_USER" ]; then
    echo -n "OpenSearch/Elasticsearch password [empty is no password] "
    read -r ARKIME_ELASTICSEARCH_PASSWORD
fi

while [ -z "$ARKIME_PASSWORD" ]; do
    echo -n "Password to encrypt S2S and other things, don't use spaces [must create one] "
    read -r ARKIME_PASSWORD
done
ARKIME_PASSWORD=${ARKIME_PASSWORD//\//\\/}

################################################################################
echo "Arkime - Creating configuration files"
if [ ! -f "$ARKIME_INSTALL_DIR/etc/config.ini" ]; then
    echo "Installing sample $ARKIME_INSTALL_DIR/etc/config.ini"
    echo sed -e "s/ARKIME_INTERFACE/${ARKIME_INTERFACE}/g" -e "s,ARKIME_ELASTICSEARCH,${ARKIME_ELASTICSEARCH},g" -e "s/ARKIME_PASSWORD/${ARKIME_PASSWORD}/g" -e "s,ARKIME_INSTALL_DIR,${ARKIME_INSTALL_DIR},g" < $ARKIME_INSTALL_DIR/etc/config.ini.sample > $ARKIME_INSTALL_DIR/etc/config.ini
    sed -e "s/ARKIME_INTERFACE/${ARKIME_INTERFACE}/g" -e "s,ARKIME_ELASTICSEARCH,${ARKIME_ELASTICSEARCH},g" -e "s/ARKIME_PASSWORD/${ARKIME_PASSWORD}/g" -e "s,ARKIME_INSTALL_DIR,${ARKIME_INSTALL_DIR},g" < $ARKIME_INSTALL_DIR/etc/config.ini.sample > $ARKIME_INSTALL_DIR/etc/config.ini

    if [ ! -z "$ARKIME_ELASTICSEARCH_USER" ]; then
        sed -i "" -e "s/# elasticsearchBasicAuth=/elasticsearchBasicAuth=${ARKIME_ELASTICSEARCH_USER}:${ARKIME_ELASTICSEARCH_PASSWORD}/g" $ARKIME_INSTALL_DIR/etc/config.ini
    fi
else
    echo "Not overwriting $ARKIME_INSTALL_DIR/etc/config.ini, delete and run again if update required (usually not), or edit by hand"
    sleep 2
fi

################################################################################
# re-create these directories after installation so they are not part of the package manifest
CREATEDIRS="logs raw"
for CREATEDIR in $CREATEDIRS; do
    if [ ! -e $ARKIME_INSTALL_DIR/$CREATEDIR ]; then
        mkdir -m 0700 -p $ARKIME_INSTALL_DIR/$CREATEDIR && \
        chown nobody $ARKIME_INSTALL_DIR/$CREATEDIR
    fi
done

################################################################################
ARCHRPM=$(uname -m)
case $ARCHRPM in
    "x86_64")
        ARCHDEB="amd64"
        ;;
    "aarch64")
        ARCHDEB="arm64"
        ;;
esac

if [ "$ARKIME_INSTALLELASTICSEARCH" == "yes" ]; then
    echo "Arkime - Downloading and installing demo OSS version of Elasticsearch"
    ES_VERSION=7.10.2
    if [ -f "/etc/redhat-release" ] || [ -f "/etc/system-release" ]; then
        yum install https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-oss-${ES_VERSION}-${ARCHRPM}.rpm
    else
        wget -nv https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-oss-${ES_VERSION}-${ARCHDEB}.deb
        dpkg -i elasticsearch-oss-${ES_VERSION}-$ARCHDEB.deb
        /bin/rm -f elasticsearch-oss-${ES_VERSION}-$ARCHDEB.deb
    fi
fi

################################################################################
if [ -d "/etc/security/limits.d" ] && [ ! -f "/etc/security/limits.d/99-arkime.conf" ]; then
    echo "Arkime - Installing /etc/security/limits.d/99-arkime.conf to make core and memlock unlimited"
    cat << EOF > /etc/security/limits.d/99-arkime.conf
nobody  -       core    unlimited
root    -       core    unlimited
nobody  -       memlock    unlimited
root    -       memlock    unlimited
EOF
fi

################################################################################

ARKIME_INET=not-set
until [ "$ARKIME_INET" == "yes" ] || [ "$ARKIME_INET" == "no" ] || [ "$ARKIME_INET" == "" ]; do
    echo -n "Download GEO files? You'll need a MaxMind account https://arkime.com/faq#maxmind (yes or no) [yes] "
    read -r ARKIME_INET
done

if [ "$ARKIME_INET" != "no" ]; then
    echo "Arkime - Downloading GEO files"
    $ARKIME_INSTALL_DIR/bin/arkime_update_geo.sh > /dev/null
else
    echo "Arkime - NOT downloading GEO files"
fi


################################################################################
echo ""
echo "Arkime - Configured - Now continue with step 4 in $ARKIME_INSTALL_DIR/README.txt"
echo ""
tail -n +9 $ARKIME_INSTALL_DIR/README.txt

