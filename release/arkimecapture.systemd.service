[Unit]
Description=Arkime Capture
After=network.target opensearch.service elasticsearch.service

[Service]
Type=simple
Restart=on-failure
StandardOutput=tty
EnvironmentFile=-BUILD_ARKIME_INSTALL_DIR/etc/capture.env
ExecStartPre=-BUILD_ARKIME_INSTALL_DIR/bin/arkime_config_interfaces.sh -c BUILD_ARKIME_INSTALL_DIR/etc/config.ini -n default
# Modify BUILD_ARKIME_INSTALL_DIR/etc/capture.env instead of ExecStart
ExecStart=/bin/sh -c 'BUILD_ARKIME_INSTALL_DIR/bin/capture -c BUILD_ARKIME_INSTALL_DIR/etc/config.ini ${OPTIONS} >> BUILD_ARKIME_INSTALL_DIR/logs/capture.log 2>&1'
WorkingDirectory=BUILD_ARKIME_INSTALL_DIR
LimitCORE=infinity
LimitMEMLOCK=infinity

[Install]
WantedBy=multi-user.target
