[Unit]
Description=Arkime Parliament
After=network.target

[Service]
Type=simple
Restart=on-failure
StandardOutput=tty
EnvironmentFile=-BUILD_ARKIME_INSTALL_DIR/etc/parliament.env
# Modify BUILD_ARKIME_INSTALL_DIR/etc/parliament.env instead of ExecStart
ExecStart=/bin/sh -c 'BUILD_ARKIME_INSTALL_DIR/bin/node parliament.js -c BUILD_ARKIME_INSTALL_DIR/etc/parliament.ini ${OPTIONS} >> BUILD_ARKIME_INSTALL_DIR/logs/parliament.log 2>&1'
WorkingDirectory=BUILD_ARKIME_INSTALL_DIR/parliament

[Install]
WantedBy=multi-user.target
