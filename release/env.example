# Copy this file to any/all of: viewer.env, capture.env, cont3xt.env, wise.env
# 
# This is how to add systemd options that will survive reinstalls, place any
# of all of these in the OPTIONS quotes.
# --insecure = don't check certificates
# --debug = turn on debugging, can have multiple of them
# --host <hostname> = for capture/viewer our hostname

# Capture/Viewer Only options
# -n <nodename> = our node name
# -o <key>=<value> = override part of the config file

# WISE
# -o <section><key>=<value> = override part of the config file
# --webconfig = allow configuration thru the web page

# Cont3xt
# -o <section><key>=<value> = override part of the config file


# Example to turn on level 2 of debugging
# OPTIONS="--debug --debug"
