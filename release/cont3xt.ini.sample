# Cont3xt Service Config - https://arkime.com/cont3xt

[cont3xt]
# The OpenSearch/Elasticsearch where all the cont3xt data is stored
# For user/password use **********************:port OR elasticsearchBasicAuth
elasticsearch=ARKIME_ELASTICSEARCH
# elasticsearchBasicAuth=

# Where the user database is, the above is used if not set
# For user/password use **********************:port OR usersElasticsearchBasicAuth
# usersElasticsearch=ARKIME_ELASTICSEARCH
# usersElasticsearchBasicAuth=

# Use digest auth by default
# If using SSO change to the http header with the username in it
userNameHeader=digest

# Password Hash Secret - Must be in default section. Since OpenSearch/Elasticsearch
# is wide open by default, we encrypt the stored password hashes with this
# so a malicous person can't insert a working new account.
# Changing the value will make all previously stored passwords no longer work.
# Make this RANDOM, you never need to type in
passwordSecret=ARKIME_PASSWORD

# HTTP Digest Realm - Must be in default section. Changing the value
# will make all previously stored passwords no longer work
# This must be the same setting as Ark<PERSON> uses, specified in config.ini
httpRealm=Moloch

# Port Cont3xt listens on
# port=3218

# Cert file to use, comment out to use http instead
# certFile=ARKIME_INSTALL_DIR/etc/arkime.cert

# Private key file to use, comment out to use http instead
# keyFile=ARKIME_INSTALL_DIR/etc/arkime.key

# default cache timeout
cacheTimeout=1d

# By default use a cache that will survive restarts but local to instance
[cache]
type=lmdb
lmdbDir=ARKIME_INSTALL_DIR/cont3xt-cache

##### INTEGRATION SETTINGS

[Threatstream]
# For threatstream we don't share the cache, it is per user
cachePolicy=user
#host=threatstreamonprem.host

[VirusTotal]
# For VT lets increase the cache timeout
cacheTimeout=1w

# WARNING: This is an ini file with sections, most likely you don't want to put a setting
# here, unless creating a new section
