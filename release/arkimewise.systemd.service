[Unit]
Description=Arkime WISE
After=network.target

[Service]
Type=simple
Restart=on-failure
StandardOutput=tty
EnvironmentFile=-BUILD_ARKIME_INSTALL_DIR/etc/wise.env
# Modify BUILD_ARKIME_INSTALL_DIR/etc/wise.env instead of ExecStart
ExecStart=/bin/sh -c 'BUILD_ARKIME_INSTALL_DIR/bin/node wiseService.js -c BUILD_ARKIME_INSTALL_DIR/etc/wise.ini ${OPTIONS} >> BUILD_ARKIME_INSTALL_DIR/logs/wise.log 2>&1'
WorkingDirectory=BUILD_ARKIME_INSTALL_DIR/wiseService

[Install]
WantedBy=multi-user.target
