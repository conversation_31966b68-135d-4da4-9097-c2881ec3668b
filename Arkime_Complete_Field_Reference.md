# Arkime完整字段参考手册

本文档是Arkime字段系统的完整参考手册，包含了所有字段定义、内置字段和会话数据映射关系的全面信息。

## 📊 字段统计概览

### 总体统计
- **字段定义总数**: 224个 (通过`arkime_field_define`注册)
- **内置字段数**: 15个 (直接在会话数据中使用)
- **字段组数**: 23个
- **支持协议数**: 22个

### 字段分类统计
```
已定义字段: 224个
├── general: 42个     (通用字段，IP、MAC、端口等)
├── http: 37个        (HTTP协议字段)
├── dns: 29个         (DNS协议字段)
├── email: 19个       (邮件协议字段)
├── cert: 18个        (证书相关字段)
├── tls: 12个         (TLS/SSL协议字段)
├── smb: 9个          (SMB协议字段)
├── suricata: 7个     (Suricata集成字段)
└── 其他: 51个        (DHCP、SOCKS、Modbus等)

内置字段: 15个
├── 基础时间: 3个     (@timestamp, firstPacket, lastPacket)
├── 会话信息: 4个     (node, ipProtocol, length, protocols)
├── 文件引用: 2个     (fileId, packetPos)
├── 网络结构: 6个     (source.*, destination.*, network.*)
```

## 🏗️ 内置字段完整列表

以下字段直接存在于会话数据中，无需通过`arkime_field_define`注册：

### 基础时间字段
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `@timestamp` | timestamp | ES处理时间戳(毫秒) | `1752149929057` |
| `firstPacket` | timestamp | 会话第一包时间戳(毫秒) | `1041342931300` |
| `lastPacket` | timestamp | 会话最后一包时间戳(毫秒) | `1041342932300` |

### 会话基础信息
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `node` | string | 捕获节点名称 | `"localhost"` |
| `ipProtocol` | integer | IP协议号 | `6` (TCP), `17` (UDP) |
| `length` | integer | 会话持续时间(毫秒) | `1000` |

### 文件和位置引用
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `fileId` | array | 文件ID数组(引用arkime_files_v30) | `[1, 2]` |
| `packetPos` | array | 数据包在PCAP文件中的位置 | `[-1, 24]` |

### 网络结构字段
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `source.ip` | string | 源IP地址 | `"*************"` |
| `source.port` | integer | 源端口 | `3267` |
| `source.bytes` | integer | 源端字节数 | `207` |
| `source.packets` | integer | 源端包数 | `1` |
| `source.mac` | array | 源MAC地址数组 | `["00:09:6b:88:f5:c9"]` |
| `destination.ip` | string | 目标IP地址 | `"***********"` |
| `destination.port` | integer | 目标端口 | `2000` |
| `destination.bytes` | integer | 目标端字节数 | `0` |
| `destination.packets` | integer | 目标端包数 | `0` |
| `destination.mac` | array | 目标MAC地址数组 | `["00:e0:81:00:b0:28"]` |

### 网络统计字段
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `network.packets` | integer | 总包数 | `1` |
| `network.bytes` | integer | 总字节数 | `207` |

### TCP特定字段 (当ipProtocol=6时)
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `tcpflags.syn` | integer | SYN标志计数 | `1` |
| `tcpflags.ack` | integer | ACK标志计数 | `2` |
| `tcpflags.fin` | integer | FIN标志计数 | `1` |
| `tcpflags.rst` | integer | RST标志计数 | `0` |
| `tcpflags.psh` | integer | PSH标志计数 | `1` |
| `tcpflags.urg` | integer | URG标志计数 | `0` |

## 🔗 字段映射关系

### 1. 内置字段映射
内置字段直接存储在会话数据中，无需字段定义：

```json
{
  "@timestamp": 1752149929057,
  "firstPacket": 1041342931300,
  "lastPacket": 1041342932300,
  "node": "localhost",
  "ipProtocol": 17,
  "source": {
    "ip": "*************",
    "port": 3267,
    "bytes": 207,
    "packets": 1,
    "mac": ["00:09:6b:88:f5:c9"]
  },
  "destination": {
    "ip": "***********", 
    "port": 2000,
    "bytes": 0,
    "packets": 0,
    "mac": ["00:e0:81:00:b0:28"]
  },
  "network": {
    "packets": 1,
    "bytes": 207
  }
}
```

### 2. 定义字段映射
通过`arkime_field_define`注册的字段需要通过`dbField2`建立映射：

| 字段定义ID | dbField2 | 会话数据路径 | 说明 |
|------------|----------|--------------|------|
| `session.length` | `length` | `length` | 会话持续时间 |
| `protocols` | `protocol` | `protocols` | 协议数组 |
| `mac.src` | `source.mac` | `source.mac` | 源MAC地址 |
| `mac.dst` | `destination.mac` | `destination.mac` | 目标MAC地址 |
| `ip.src` | `source.ip` | `source.ip` | 源IP地址 |
| `ip.dst` | `destination.ip` | `destination.ip` | 目标IP地址 |
| `port.src` | `source.port` | `source.port` | 源端口 |
| `port.dst` | `destination.port` | `destination.port` | 目标端口 |
| `bytes.src` | `source.bytes` | `source.bytes` | 源端字节数 |
| `bytes.dst` | `destination.bytes` | `destination.bytes` | 目标端字节数 |
| `packets.src` | `source.packets` | `source.packets` | 源端包数 |
| `packets.dst` | `destination.packets` | `destination.packets` | 目标端包数 |

### 3. 协议特定字段映射
协议字段存储在对应的协议对象中：

```json
{
  "http": {
    "method": "GET",
    "host": "example.com",
    "uri": "/path",
    "statuscode": 200
  },
  "dns": {
    "host": "example.com",
    "answers": {
      "ip": ["*******"],
      "type": ["A"]
    }
  },
  "tls": {
    "ja4": "t13d1516h2_8daaf6152771_b0da82dd1658",
    "cipher": "TLS_AES_128_GCM_SHA256"
  }
}
```

## 🔍 查询示例

### 1. 查询内置字段
```json
// 查询特定时间范围的会话
{
  "query": {
    "range": {
      "firstPacket": {
        "gte": 1041342931000,
        "lte": 1041342932000
      }
    }
  }
}

// 查询特定节点的会话
{
  "query": {
    "term": {
      "node": "localhost"
    }
  }
}

// 查询TCP协议会话
{
  "query": {
    "term": {
      "ipProtocol": 6
    }
  }
}
```

### 2. 查询定义字段
```json
// 查询HTTP GET请求
{
  "query": {
    "term": {
      "http.method": "GET"
    }
  }
}

// 查询特定源IP
{
  "query": {
    "term": {
      "source.ip": "*************"
    }
  }
}

// 查询DNS查询
{
  "query": {
    "exists": {
      "field": "dns.host"
    }
  }
}
```

### 3. 复合查询
```json
// 查询特定时间范围内的HTTP会话
{
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "firstPacket": {
              "gte": 1041342931000
            }
          }
        },
        {
          "exists": {
            "field": "http"
          }
        }
      ]
    }
  }
}
```

## 📚 字段类型说明

### Elasticsearch字段类型
- **termfield**: 可搜索的精确匹配字符串
- **textfield**: 全文搜索文本字段
- **integer**: 整数类型
- **ip**: IP地址类型
- **date**: 日期时间类型

### 内置字段类型
- **timestamp**: 时间戳(毫秒)
- **string**: 字符串
- **integer**: 整数
- **array**: 数组

## 🛠️ 开发指南

### 1. 字段查找流程
1. 确定要查询的数据类型
2. 检查是否为内置字段(直接使用字段路径)
3. 如果是定义字段，查找对应的`dbField2`值
4. 使用正确的字段路径构建查询

### 2. 最佳实践
- **内置字段**: 直接使用字段路径查询
- **定义字段**: 使用`dbField2`的值构建查询
- **协议字段**: 注意协议对象的嵌套结构
- **性能优化**: 优先使用term查询进行精确匹配

### 3. 常见错误
- ❌ 使用字段定义ID查询: `"mac.src": "value"`
- ✅ 使用正确路径查询: `"source.mac": "value"`
- ❌ 忽略协议嵌套: `"method": "GET"`
- ✅ 使用完整路径: `"http.method": "GET"`

## 📋 完整字段定义列表

### General组字段 (42个)

| 字段定义ID | 友好名称 | dbField2 | 类型 | 会话数据路径 | 说明 |
|------------|----------|----------|------|--------------|------|
| `asset` | Asset | `asset` | termfield | `asset` | 资产标识 |
| `bytes` | Bytes | `totBytes` | integer | `network.bytes` | 总字节数 |
| `bytes.dst` | Dst Bytes | `destination.bytes` | integer | `destination.bytes` | 目标端字节数 |
| `bytes.src` | Src Bytes | `source.bytes` | integer | `source.bytes` | 源端字节数 |
| `databytes` | Data bytes | `totDataBytes` | integer | `databytes` | 数据字节数 |
| `databytes.dst` | Dst data bytes | `dstDataBytes` | integer | `databytes.dst` | 目标端数据字节数 |
| `databytes.src` | Src data bytes | `srcDataBytes` | integer | `databytes.src` | 源端数据字节数 |
| `ip` | Src or Dst IP | `ipall` | ip | `source.ip` 或 `destination.ip` | 源或目标IP |
| `ip.dst` | Dst IP | `destination.ip` | ip | `destination.ip` | 目标IP地址 |
| `ip.src` | Src IP | `source.ip` | ip | `source.ip` | 源IP地址 |
| `mac` | Src or Dst MAC | `macall` | termfield | `source.mac` 或 `destination.mac` | 源或目标MAC |
| `mac.dst` | Dst MAC | `destination.mac` | termfield | `destination.mac` | 目标MAC地址 |
| `mac.src` | Src MAC | `source.mac` | termfield | `source.mac` | 源MAC地址 |
| `packets` | Packets | `totPackets` | integer | `network.packets` | 总包数 |
| `packets.dst` | Dst Packets | `destination.packets` | integer | `destination.packets` | 目标端包数 |
| `packets.src` | Src Packets | `source.packets` | integer | `source.packets` | 源端包数 |
| `port` | Src or Dst Port | `portall` | integer | `source.port` 或 `destination.port` | 源或目标端口 |
| `port.dst` | Dst Port | `destination.port` | integer | `destination.port` | 目标端口 |
| `port.src` | Src Port | `source.port` | integer | `source.port` | 源端口 |
| `protocols` | Protocols | `protocol` | termfield | `protocols` | 协议数组 |
| `session.length` | Session Length | `length` | integer | `length` | 会话持续时间 |
| `tags` | Tags | `tags` | termfield | `tags` | 标签 |
| `user` | User | `user` | termfield | `user` | 用户 |
| `vlan` | VLan | `vlan` | integer | `network.vlan.id` | VLAN ID |

### HTTP组字段 (37个)

| 字段定义ID | 友好名称 | dbField2 | 类型 | 会话数据路径 | 说明 |
|------------|----------|----------|------|--------------|------|
| `http.authtype` | Auth Type | `http.authType` | termfield | `http.authType` | HTTP认证类型 |
| `http.bodymagic` | Body Magic | `http.bodyMagic` | termfield | `http.bodyMagic` | HTTP体魔数 |
| `http.clientversion` | Client Version | `http.clientVersion` | termfield | `http.clientVersion` | HTTP客户端版本 |
| `http.cookie` | Cookie | `http.cookieKey` | termfield | `http.cookieKey` | HTTP Cookie键 |
| `http.cookievalue` | Cookie Value | `http.cookieValue` | termfield | `http.cookieValue` | HTTP Cookie值 |
| `http.hasheader` | Has Src or Dst Header | `hhall` | termfield | `http.hhall` | 包含源或目标头 |
| `http.hasheader.dst` | Has Dst Header | `http.responseHeader` | termfield | `http.responseHeader` | 包含目标头 |
| `http.hasheader.src` | Has Src Header | `http.requestHeader` | termfield | `http.requestHeader` | 包含源头 |
| `http.md5` | Body MD5 | `http.md5` | termfield | `http.md5` | HTTP体MD5 |
| `http.method` | Request Method | `http.method` | termfield | `http.method` | HTTP请求方法 |
| `http.requestbody` | Request Body | `http.requestBody` | textfield | `http.requestBody` | HTTP请求体 |
| `http.responsebody` | Response Body | `http.responseBody` | textfield | `http.responseBody` | HTTP响应体 |
| `http.sha256` | Body SHA256 | `http.sha256` | termfield | `http.sha256` | HTTP体SHA256 |
| `http.statuscode` | Status Code | `http.statuscode` | integer | `http.statuscode` | HTTP状态码 |
| `http.uri` | URI | `http.uri` | termfield | `http.uri` | HTTP URI |
| `http.useragent` | Useragent | `http.useragent` | termfield | `http.useragent` | HTTP用户代理 |
| `http.version` | Version | `http.version` | termfield | `http.version` | HTTP版本 |
| `http.xffgeo` | XFF GEO | `http.xffGEO` | termfield | `http.xffGEO` | X-Forwarded-For地理位置 |
| `http.xffip` | XFF IP | `http.xffIp` | ip | `http.xffIp` | X-Forwarded-For IP |
| `host.http` | Hostname | `http.host` | termfield | `http.host` | HTTP主机名 |
| `user.http` | User | `http.user` | termfield | `http.user` | HTTP用户 |

### DNS组字段 (29个)

| 字段定义ID | 友好名称 | dbField2 | 类型 | 会话数据路径 | 说明 |
|------------|----------|----------|------|--------------|------|
| `dns.answers` | Answers | `dns.answers` | integer | `dns.answers` | DNS应答数量 |
| `dns.opcode` | Opcode | `dns.opcode` | integer | `dns.opcode` | DNS操作码 |
| `dns.puny` | Puny | `dns.puny` | termfield | `dns.puny` | DNS Punycode |
| `dns.qc` | Query class | `dns.qc` | termfield | `dns.qc` | DNS查询类 |
| `dns.qt` | Query type | `dns.qt` | termfield | `dns.qt` | DNS查询类型 |
| `dns.rcode` | Return code | `dns.rcode` | integer | `dns.rcode` | DNS返回码 |
| `dns.status` | Status | `dns.status` | termfield | `dns.status` | DNS状态 |
| `host.dns` | Hostname | `dns.host` | termfield | `dns.host` | DNS主机名 |
| `ip.dns` | IP | `dns.ip` | ip | `dns.ip` | DNS IP地址 |

### TLS组字段 (12个)

| 字段定义ID | 友好名称 | dbField2 | 类型 | 会话数据路径 | 说明 |
|------------|----------|----------|------|--------------|------|
| `tls.cipher` | Cipher | `tls.cipher` | termfield | `tls.cipher` | TLS加密套件 |
| `tls.ja3` | JA3 | `tls.ja3` | termfield | `tls.ja3` | TLS JA3指纹 |
| `tls.ja3s` | JA3S | `tls.ja3s` | termfield | `tls.ja3s` | TLS JA3S指纹 |
| `tls.ja4` | JA4 | `tls.ja4` | termfield | `tls.ja4` | TLS JA4指纹 |
| `tls.sessionid` | Session Id | `tls.sessionId` | termfield | `tls.sessionId` | TLS会话ID |
| `tls.version` | Version | `tls.version` | termfield | `tls.version` | TLS版本 |

## 🔧 dbField与dbField2的区别

### 关键概念
- **dbField**: `arkime_field_define()`函数中的参数，用于内部处理
- **dbField2**: 存储在ES字段定义中的值，用于建立与会话数据的映射

### 实际示例
```c
// 函数调用中的dbField参数
arkime_field_define("http", "lotermfield",
                    "host.http", "Hostname", "http.host",  // <- dbField
                    "HTTP host header field", ...);
{
  ip
  protocol
  http[
    host
    method
  ]
}

// 存储在ES中的dbField2
{
  "_id": "host.http",
  "_source": {
    "dbField2": "http.host"  // <- 保持原始完整值
  }
}
```

### 映射规律
1. **dbField2是查询的关键**: 构建ES查询时必须使用dbField2的值
2. **字段ID ≠ 会话路径**: 字段定义ID可能与实际存储路径不同
3. **内置字段无dbField2**: 内置字段直接使用字段路径

## 📖 完整字段定义JSON

所有224个字段的完整定义保存在`arkime_fields_clean.json`文件中，格式如下：

```json
[
  {
    "_index": "arkime_fields_v30",
    "_type": "_doc",
    "_id": "tls.ja4",
    "_score": 1,
    "_source": {
      "friendlyName": "JA4",
      "group": "tls",
      "help": "SSL/TLS JA4 field",
      "dbField2": "tls.ja4",
      "type": "termfield"
    }
  }
]
```

---

*本文档提供了Arkime字段系统的完整参考，包含所有内置字段和定义字段的详细信息，是开发和运维工作的重要参考资料。*
