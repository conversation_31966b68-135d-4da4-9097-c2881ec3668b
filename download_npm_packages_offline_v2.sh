#!/bin/bash
# 改进的npm离线包下载脚本
# 修复网络和配置问题

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OFFLINE_NPM_DIR="${SCRIPT_DIR}/npm_offline_packages"
CACHE_DIR="${OFFLINE_NPM_DIR}/.npm_cache"
TARBALLS_DIR="${OFFLINE_NPM_DIR}/tarballs"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查必要文件
check_requirements() {
    log_info "检查必要文件..."
    
    if [[ ! -f "${SCRIPT_DIR}/package.json" ]]; then
        log_error "package.json 文件不存在"
        exit 1
    fi
    
    if [[ ! -f "${SCRIPT_DIR}/package-lock.json" ]]; then
        log_error "package-lock.json 文件不存在"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    log_info "Node.js版本: $node_version"
    log_info "npm版本: $npm_version"
    
    log_success "必要文件检查完成"
}

# 创建目录结构
create_directories() {
    log_info "创建离线包目录结构..."
    
    # 清理旧目录
    if [[ -d "$OFFLINE_NPM_DIR" ]]; then
        rm -rf "$OFFLINE_NPM_DIR"
    fi
    
    mkdir -p "${OFFLINE_NPM_DIR}"
    mkdir -p "${CACHE_DIR}"
    mkdir -p "${TARBALLS_DIR}"
    
    log_success "目录结构创建完成"
}

# 配置npm环境
configure_npm() {
    log_info "配置npm环境..."
    
    # 备份原有配置
    if [[ -f ~/.npmrc ]]; then
        cp ~/.npmrc ~/.npmrc.backup.$(date +%s) 2>/dev/null || true
    fi
    
    # 设置临时npm配置
    export NPM_CONFIG_CACHE="${CACHE_DIR}"
    export NPM_CONFIG_AUDIT=false
    export NPM_CONFIG_FUND=false
    export NPM_CONFIG_UPDATE_NOTIFIER=false
    export NPM_CONFIG_FETCH_RETRY_MINTIMEOUT=20000
    export NPM_CONFIG_FETCH_RETRY_MAXTIMEOUT=120000
    export NPM_CONFIG_FETCH_RETRIES=3
    export NPM_CONFIG_REGISTRY=https://registry.npmjs.org/
    export NPM_CONFIG_STRICT_SSL=false
    
    # 创建临时.npmrc
    cat > "${OFFLINE_NPM_DIR}/.npmrc" << EOF
cache=${CACHE_DIR}
audit=false
fund=false
update-notifier=false
fetch-retry-mintimeout=20000
fetch-retry-maxtimeout=120000
fetch-retries=3
registry=https://registry.npmjs.org/
strict-ssl=false
prefer-offline=false
EOF
    
    log_success "npm环境配置完成"
}

# 下载npm包
download_npm_packages() {
    log_info "开始下载npm包..."
    
    # 创建临时工作目录
    local temp_dir="${OFFLINE_NPM_DIR}/temp_download"
    mkdir -p "$temp_dir"
    
    # 复制必要文件
    cp "${SCRIPT_DIR}/package.json" "$temp_dir/"
    cp "${SCRIPT_DIR}/package-lock.json" "$temp_dir/"
    cp "${OFFLINE_NPM_DIR}/.npmrc" "$temp_dir/"
    
    cd "$temp_dir"
    
    # 下载生产依赖
    log_info "下载生产依赖包..."
    local install_cmd="npm ci --omit=dev --no-audit --no-fund --cache=${CACHE_DIR} --loglevel verbose --progress=true"
    
    if ! eval "$install_cmd"; then
        log_warning "npm ci失败，尝试使用npm install..."
        if ! npm install --omit=dev --no-audit --no-fund --cache="${CACHE_DIR}"; then
            log_error "npm install也失败了，请检查网络连接和package.json"
            return 1
        fi
    fi
    
    # 验证安装
    if [[ ! -d "node_modules" ]]; then
        log_error "node_modules目录未创建"
        return 1
    fi
    
    local prod_count=$(find node_modules -maxdepth 1 -type d | wc -l)
    log_info "生产依赖包数量: $((prod_count - 1))"
    
    # 打包生产环境
    log_info "打包生产环境node_modules..."
    tar -czf "${OFFLINE_NPM_DIR}/node_modules_production.tar.gz" node_modules/
    
    # 下载完整依赖（包含开发依赖）
    log_info "下载完整依赖包..."
    rm -rf node_modules/
    
    if ! npm ci --no-audit --no-fund --cache="${CACHE_DIR} --loglevel verbose --progress=true"; then
        log_warning "npm ci失败，尝试使用npm install..."
        if ! npm install --no-audit --no-fund --cache="${CACHE_DIR}"; then
            log_error "完整依赖安装失败"
            return 1
        fi
    fi
    
    local full_count=$(find node_modules -maxdepth 1 -type d | wc -l)
    log_info "完整依赖包数量: $((full_count - 1))"
    
    # 打包完整环境
    log_info "打包完整node_modules..."
    tar -czf "${OFFLINE_NPM_DIR}/node_modules_full.tar.gz" node_modules/
    
    # 清理临时目录
    cd "${SCRIPT_DIR}"
    rm -rf "$temp_dir"
    
    log_success "npm包下载完成"
}

# 提取tarball文件
extract_tarballs() {
    log_info "提取tarball文件..."
    
    # 从缓存中复制所有tarball文件
    if [[ -d "${CACHE_DIR}" ]]; then
        find "${CACHE_DIR}" -name "*.tgz" -exec cp {} "${TARBALLS_DIR}/" \; 2>/dev/null || true
        find "${CACHE_DIR}" -name "*.tar.gz" -exec cp {} "${TARBALLS_DIR}/" \; 2>/dev/null || true
    fi
    
    local tarball_count=$(find "${TARBALLS_DIR}" -name "*.tgz" -o -name "*.tar.gz" | wc -l)
    log_info "提取了 ${tarball_count} 个tarball文件"
    
    log_success "tarball文件提取完成"
}

# 创建离线安装脚本
create_offline_install_script() {
    log_info "创建离线npm安装脚本..."
    
    cat > "${OFFLINE_NPM_DIR}/install_npm_offline.sh" << 'EOF'
#!/bin/bash
# npm离线安装脚本 v2

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTALL_DIR="${1:-$(pwd)}"
MODE="${2:-production}"

log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 验证参数
if [[ "$MODE" != "production" && "$MODE" != "full" ]]; then
    log_error "模式参数错误，只支持 'production' 或 'full'"
    exit 1
fi

log_info "开始离线安装npm包到: $INSTALL_DIR"
log_info "安装模式: $MODE"

# 确保目标目录存在
mkdir -p "$INSTALL_DIR"

# 根据模式选择安装包
if [[ "$MODE" == "production" ]]; then
    package_file="${SCRIPT_DIR}/node_modules_production.tar.gz"
else
    package_file="${SCRIPT_DIR}/node_modules_full.tar.gz"
fi

if [[ ! -f "$package_file" ]]; then
    log_error "安装包文件不存在: $package_file"
    exit 1
fi

log_info "解压npm包..."
if tar -xzf "$package_file" -C "$INSTALL_DIR"; then
    log_success "npm包解压完成"
else
    log_error "npm包解压失败"
    exit 1
fi

# 设置权限
if [[ -d "${INSTALL_DIR}/node_modules" ]]; then
    log_info "设置文件权限..."
    find "${INSTALL_DIR}/node_modules" -type d -exec chmod 755 {} \;
    find "${INSTALL_DIR}/node_modules" -type f -exec chmod 644 {} \;
    
    # 设置可执行文件权限
    find "${INSTALL_DIR}/node_modules/.bin" -type f -exec chmod 755 {} \; 2>/dev/null || true
    
    log_success "权限设置完成"
fi

log_success "npm离线安装完成"
EOF
    
    chmod +x "${OFFLINE_NPM_DIR}/install_npm_offline.sh"
    
    log_success "离线安装脚本创建完成"
}

# 复制配置文件
copy_config_files() {
    log_info "复制配置文件..."
    
    cp "${SCRIPT_DIR}/package.json" "${OFFLINE_NPM_DIR}/"
    cp "${SCRIPT_DIR}/package-lock.json" "${OFFLINE_NPM_DIR}/"
    
    log_success "配置文件复制完成"
}

# 生成使用说明
create_usage_guide() {
    log_info "生成使用说明..."
    
    cat > "${OFFLINE_NPM_DIR}/README.md" << EOF
# npm离线安装包 v2

本目录包含Arkime项目的所有npm依赖包，支持离线安装。

## 使用方法

### 推荐方式：使用安装脚本
\`\`\`bash
# 安装生产环境依赖
./install_npm_offline.sh /opt/arkime production

# 安装完整依赖（包含开发依赖）
./install_npm_offline.sh /opt/arkime full
\`\`\`

### 手动解压
\`\`\`bash
# 解压生产环境依赖
tar -xzf node_modules_production.tar.gz -C /opt/arkime

# 解压完整依赖
tar -xzf node_modules_full.tar.gz -C /opt/arkime
\`\`\`

## 与Makefile集成

将此目录放在Arkime项目根目录下，Makefile会自动检测并使用离线包。

## 文件说明

- \`node_modules_production.tar.gz\` - 生产环境依赖包
- \`node_modules_full.tar.gz\` - 完整依赖包
- \`install_npm_offline.sh\` - 离线安装脚本
- \`tarballs/\` - 原始tarball文件
- \`.npm_cache/\` - npm缓存目录

EOF
    
    log_success "使用说明生成完成"
}

# 主函数
main() {
    log_info "开始创建npm离线安装包 v2..."
    
    check_requirements
    create_directories
    configure_npm
    download_npm_packages
    extract_tarballs
    create_offline_install_script
    copy_config_files
    create_usage_guide
    
    # 显示统计信息
    local package_size=$(du -sh "${OFFLINE_NPM_DIR}" | cut -f1)
    local tarball_count=$(find "${TARBALLS_DIR}" -name "*.tgz" -o -name "*.tar.gz" | wc -l)
    
    log_success "npm离线安装包创建完成!"
    echo ""
    echo "=== 统计信息 ==="
    echo "包大小: $package_size"
    echo "tarball数量: $tarball_count"
    echo "位置: $OFFLINE_NPM_DIR"
    echo ""
    echo "=== 使用方法 ==="
    echo "1. 将 npm_offline_packages 目录放到Arkime项目根目录"
    echo "2. 运行 make install 会自动使用离线包"
    echo "3. 或手动运行: ./npm_offline_packages/install_npm_offline.sh /opt/arkime production"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
