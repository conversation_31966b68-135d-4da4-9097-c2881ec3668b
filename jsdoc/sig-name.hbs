{{#if virtual}}*{{/if}}{{#with (parentObject)}}{{#if virtual}}*{{/if~}}{{/with~}}
{{#if name}}{{#sig~}}
{{{@depOpen}~}}
{{{@codeOpen}~}}
{{#if @prefix}}{{@prefix}} {{/if~}}
{{@parent~}}
{{@accessSymbol}}{{#if (isEvent)}}"{{{name}}}"{{else}}{{{escape name}}}{{/if~}}
{{#if @methodSign}}{{#if (isEvent)}} {{@methodSign}}{{else}}{{@methodSign}}{{/if}}{{/if~}}
{{{@codeClose}~}}
{{#if @suffix}} {{@suffix}}{{/if~}}
{{{@depClose}~}}
{{~/sig}}{{/if~}}
{{#if virtual}}*{{/if}}{{#with (parentObject)}}{{#if virtual}}*{{/if~}}{{/with~}}
{{{getKind kind name}}}
