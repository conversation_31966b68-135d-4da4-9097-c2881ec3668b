#!/bin/bash
# Arkime 离线安装包收集脚本
# 用于CentOS 7.4系统
# 作者: 自动生成
# 日期: $(date)

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OFFLINE_DIR="${SCRIPT_DIR}/arkime_offline_packages"
YUM_CACHE_DIR="${OFFLINE_DIR}/yum_packages"
NPM_CACHE_DIR="${OFFLINE_DIR}/npm_packages"
SOURCE_DIR="${OFFLINE_DIR}/source_packages"
NODE_DIR="${OFFLINE_DIR}/nodejs"
DOCS_DIR="${OFFLINE_DIR}/docs"

# Node.js版本 (从package.json获取)
NODE_VERSION="20.19.2"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建离线包目录结构..."
    mkdir -p "${YUM_CACHE_DIR}"
    mkdir -p "${NPM_CACHE_DIR}"
    mkdir -p "${SOURCE_DIR}"
    mkdir -p "${NODE_DIR}"
    mkdir -p "${DOCS_DIR}"
    log_success "目录结构创建完成"
}

# 安装必要的工具
install_tools() {
    log_info "安装必要的工具..."
    
    # 安装开发工具
    yum groupinstall -y "Development Tools"
    
    # 安装必要的软件包
    yum install -y \
        wget curl git \
        epel-release \
        yum-utils \
        createrepo \
        rpm-build \
        autoconf automake libtool \
        gcc gcc-c++ make \
        zlib-devel openssl-devel \
        pcre-devel \
        libyaml-devel \
        perl-libwww-perl perl-JSON perl-LWP-Protocol-https \
        ethtool
    
    log_success "工具安装完成"
}

# 收集系统依赖包
collect_system_packages() {
    log_info "收集系统依赖包..."
    
    # CentOS 7.4 Arkime依赖包列表
    local packages=(
        # 基础开发工具
        "gcc" "gcc-c++" "make" "autoconf" "automake" "libtool"
        "pkgconfig" "cmake" "git" "wget" "curl"
        
        # 系统库
        "zlib-devel" "openssl-devel" "pcre-devel" "libyaml-devel"
        "glib2-devel" "libpcap-devel" "libcurl-devel"
        
        # Perl依赖
        "perl" "perl-libwww-perl" "perl-JSON" "perl-LWP-Protocol-https"
        "perl-ExtUtils-MakeMaker" "perl-Test-Simple"
        
        # 网络工具
        "ethtool" "tcpdump" "wireshark"
        
        # 其他依赖
        "file-devel" "which" "sudo"
        
        # Elasticsearch/OpenSearch Java依赖
        "java-1.8.0-openjdk" "java-1.8.0-openjdk-devel"
    )
    
    # 下载包及其依赖
    for package in "${packages[@]}"; do
        log_info "下载包: $package"
        yumdownloader --resolve --destdir="${YUM_CACHE_DIR}" "$package" || log_warning "无法下载包: $package"
    done
    
    # 创建本地仓库
    log_info "创建本地YUM仓库..."
    createrepo "${YUM_CACHE_DIR}"
    
    log_success "系统依赖包收集完成"
}

# 下载Node.js
download_nodejs() {
    log_info "下载Node.js ${NODE_VERSION}..."
    
    local node_url="https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-linux-x64.tar.xz"
    local node_file="${NODE_DIR}/node-v${NODE_VERSION}-linux-x64.tar.xz"
    
    if [[ ! -f "$node_file" ]]; then
        wget -O "$node_file" "$node_url" || {
            log_error "Node.js下载失败"
            return 1
        }
    fi
    
    log_success "Node.js下载完成"
}

# 收集第三方源码包
collect_source_packages() {
    log_info "收集第三方源码包..."
    
    # 从easybutton-build.sh获取版本信息
    local packages=(
        "https://download.gnome.org/sources/glib/2.72/glib-2.72.4.tar.xz"
        "https://github.com/VirusTotal/yara/archive/v4.2.3.tar.gz"
        "https://github.com/maxmind/libmaxminddb/releases/download/1.7.1/libmaxminddb-1.7.1.tar.gz"
        "https://www.tcpdump.org/release/libpcap-1.10.4.tar.gz"
        "https://curl.se/download/curl-8.4.0.tar.gz"
        "https://www.lua.org/ftp/lua-5.3.6.tar.gz"
        "https://github.com/nghttp2/nghttp2/releases/download/v1.57.0/nghttp2-1.57.0.tar.gz"
        "https://github.com/facebook/zstd/releases/download/v1.5.5/zstd-1.5.5.tar.gz"
    )
    
    for url in "${packages[@]}"; do
        local filename=$(basename "$url")
        local filepath="${SOURCE_DIR}/${filename}"
        
        if [[ ! -f "$filepath" ]]; then
            log_info "下载: $filename"
            wget -O "$filepath" "$url" || log_warning "无法下载: $filename"
        else
            log_info "已存在: $filename"
        fi
    done
    
    log_success "第三方源码包收集完成"
}

# 安装临时Node.js用于npm包收集
install_temp_nodejs() {
    log_info "安装临时Node.js用于npm包收集..."
    
    local node_file="${NODE_DIR}/node-v${NODE_VERSION}-linux-x64.tar.xz"
    local temp_node_dir="/tmp/node_temp"
    
    if [[ -f "$node_file" ]]; then
        mkdir -p "$temp_node_dir"
        tar -xf "$node_file" -C "$temp_node_dir" --strip-components=1
        export PATH="$temp_node_dir/bin:$PATH"
        log_success "临时Node.js安装完成"
    else
        log_error "Node.js文件不存在"
        return 1
    fi
}

# 收集npm包
collect_npm_packages() {
    log_info "收集npm包..."

    # 确保有Node.js环境
    if ! command -v npm &> /dev/null; then
        install_temp_nodejs
    fi

    # 使用专门的npm离线下载脚本
    if [[ -f "${SCRIPT_DIR}/download_npm_packages_offline.sh" ]]; then
        log_info "使用专门的npm离线下载脚本..."
        chmod +x "${SCRIPT_DIR}/download_npm_packages_offline.sh"
        "${SCRIPT_DIR}/download_npm_packages_offline.sh"

        # 移动生成的npm离线包到指定位置
        if [[ -d "${SCRIPT_DIR}/npm_offline_packages" ]]; then
            mv "${SCRIPT_DIR}/npm_offline_packages" "${NPM_CACHE_DIR}/"
            log_success "npm离线包已移动到: ${NPM_CACHE_DIR}/npm_offline_packages"
        fi
    else
        # 回退到原有方法
        log_warning "npm离线下载脚本不存在，使用传统方法..."

        # 配置npm缓存目录
        npm config set cache "${NPM_CACHE_DIR}/.npm"

        # 安装npm包到指定目录
        cd "${SCRIPT_DIR}"

        # 创建临时package.json用于下载
        local temp_package_dir="${NPM_CACHE_DIR}/temp_install"
        mkdir -p "$temp_package_dir"
        cp package.json "$temp_package_dir/"
        cp package-lock.json "$temp_package_dir/" 2>/dev/null || true

        cd "$temp_package_dir"

        # 下载所有依赖
        log_info "下载生产依赖..."
        npm ci --production --cache "${NPM_CACHE_DIR}/.npm" || npm install --production --cache "${NPM_CACHE_DIR}/.npm"

        # 下载开发依赖
        log_info "下载开发依赖..."
        npm install --cache "${NPM_CACHE_DIR}/.npm"

        # 打包node_modules
        log_info "打包node_modules..."
        tar -czf "${NPM_CACHE_DIR}/node_modules.tar.gz" node_modules/

        # 复制package文件
        cp package*.json "${NPM_CACHE_DIR}/"

        cd "${SCRIPT_DIR}"
        rm -rf "$temp_package_dir"
    fi

    log_success "npm包收集完成"
}

# 生成安装脚本
generate_install_script() {
    log_info "生成离线安装脚本..."
    
    cat > "${OFFLINE_DIR}/install_arkime_offline.sh" << 'EOF'
#!/bin/bash
# Arkime 离线安装脚本
# 适用于CentOS 7.4

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NODE_VERSION="20.19.2"

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    exit 1
fi

# 安装系统依赖
install_system_packages() {
    log_info "安装系统依赖包..."
    
    # 配置本地YUM仓库
    cat > /etc/yum.repos.d/arkime-offline.repo << EOL
[arkime-offline]
name=Arkime Offline Repository
baseurl=file://${SCRIPT_DIR}/yum_packages
enabled=1
gpgcheck=0
EOL
    
    yum clean all
    yum makecache
    
    # 安装依赖包
    yum install -y --disablerepo="*" --enablerepo="arkime-offline" \
        gcc gcc-c++ make autoconf automake libtool \
        zlib-devel openssl-devel pcre-devel libyaml-devel \
        glib2-devel libpcap-devel libcurl-devel \
        perl perl-libwww-perl perl-JSON perl-LWP-Protocol-https \
        ethtool java-1.8.0-openjdk
    
    log_success "系统依赖包安装完成"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js ${NODE_VERSION}..."
    
    local node_file="${SCRIPT_DIR}/nodejs/node-v${NODE_VERSION}-linux-x64.tar.xz"
    local install_dir="/opt/arkime"
    
    mkdir -p "$install_dir"
    tar -xf "$node_file" -C "$install_dir"
    
    # 创建符号链接
    ln -sf "/opt/arkime/node-v${NODE_VERSION}-linux-x64/bin/node" /usr/local/bin/node
    ln -sf "/opt/arkime/node-v${NODE_VERSION}-linux-x64/bin/npm" /usr/local/bin/npm
    
    log_success "Node.js安装完成"
}

# 编译第三方库
compile_thirdparty() {
    log_info "编译第三方库..."

    local build_dir="/tmp/arkime_build"
    mkdir -p "$build_dir"
    cd "$build_dir"

    # 设置环境变量
    export PKG_CONFIG_PATH="/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH"
    export LD_LIBRARY_PATH="/usr/local/lib:$LD_LIBRARY_PATH"

    # 编译libmaxminddb
    log_info "编译libmaxminddb..."
    if [[ -f "${SOURCE_DIR}/libmaxminddb-1.7.1.tar.gz" ]]; then
        tar -xzf "${SOURCE_DIR}/libmaxminddb-1.7.1.tar.gz"
        cd libmaxminddb-1.7.1
        ./configure --prefix=/usr/local
        make -j$(nproc)
        make install
        cd ..
    fi

    # 编译yara
    log_info "编译yara..."
    if [[ -f "${SOURCE_DIR}/yara-4.2.3.tar.gz" ]]; then
        tar -xzf "${SOURCE_DIR}/yara-4.2.3.tar.gz"
        cd yara-4.2.3
        ./bootstrap.sh
        ./configure --prefix=/usr/local
        make -j$(nproc)
        make install
        cd ..
    fi

    # 编译zstd
    log_info "编译zstd..."
    if [[ -f "${SOURCE_DIR}/zstd-1.5.5.tar.gz" ]]; then
        tar -xzf "${SOURCE_DIR}/zstd-1.5.5.tar.gz"
        cd zstd-1.5.5
        make -j$(nproc)
        make install PREFIX=/usr/local
        cd ..
    fi

    # 编译nghttp2
    log_info "编译nghttp2..."
    if [[ -f "${SOURCE_DIR}/nghttp2-1.57.0.tar.gz" ]]; then
        tar -xzf "${SOURCE_DIR}/nghttp2-1.57.0.tar.gz"
        cd nghttp2-1.57.0
        ./configure --prefix=/usr/local
        make -j$(nproc)
        make install
        cd ..
    fi

    # 编译curl
    log_info "编译curl..."
    if [[ -f "${SOURCE_DIR}/curl-8.4.0.tar.gz" ]]; then
        tar -xzf "${SOURCE_DIR}/curl-8.4.0.tar.gz"
        cd curl-8.4.0
        ./configure --prefix=/usr/local --with-ssl --with-nghttp2=/usr/local
        make -j$(nproc)
        make install
        cd ..
    fi

    # 编译lua
    log_info "编译lua..."
    if [[ -f "${SOURCE_DIR}/lua-5.3.6.tar.gz" ]]; then
        tar -xzf "${SOURCE_DIR}/lua-5.3.6.tar.gz"
        cd lua-5.3.6
        make linux -j$(nproc)
        make install INSTALL_TOP=/usr/local
        cd ..
    fi

    # 更新库路径
    echo "/usr/local/lib" > /etc/ld.so.conf.d/arkime.conf
    ldconfig

    log_success "第三方库编译完成"
}

# 编译Arkime
compile_arkime() {
    log_info "编译Arkime..."

    # 进入源码目录
    cd "${SCRIPT_DIR}/../"

    # 配置编译选项
    ./configure \
        --prefix=/opt/arkime \
        --with-libpcap=/usr \
        --with-yara=/usr/local \
        --with-maxminddb=/usr/local \
        --with-curl=/usr/local \
        --with-nghttp2=/usr/local

    # 编译
    make -j$(nproc)

    log_success "Arkime编译完成"
}

# 安装Arkime
install_arkime() {
    log_info "安装Arkime..."

    cd "${SCRIPT_DIR}/../"

    # 创建arkime用户
    if ! id arkime &>/dev/null; then
        useradd -r -s /bin/false arkime
    fi

    # 安装二进制文件
    make install

    # 解压npm包
    if [[ -f "${SCRIPT_DIR}/npm_packages/node_modules.tar.gz" ]]; then
        cd /opt/arkime
        tar -xzf "${SCRIPT_DIR}/npm_packages/node_modules.tar.gz"
    fi

    # 设置权限
    chown -R arkime:arkime /opt/arkime

    log_success "Arkime安装完成"
}

# 配置Elasticsearch
configure_elasticsearch() {
    log_info "配置Elasticsearch..."

    # 安装Java
    yum install -y --disablerepo="*" --enablerepo="arkime-offline" java-1.8.0-openjdk

    # 这里需要手动安装Elasticsearch
    log_warning "请手动安装Elasticsearch 7.17.x"
    log_info "下载地址: https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-7.17.15-x86_64.rpm"

    log_success "Elasticsearch配置完成"
}

# 主安装流程
main() {
    log_info "开始Arkime离线安装..."

    install_system_packages
    install_nodejs
    compile_thirdparty
    compile_arkime
    install_arkime
    configure_elasticsearch

    log_success "Arkime离线安装完成!"
    log_info "请参考文档完成Elasticsearch安装和Arkime配置"
}

main "$@"
EOF
    
    chmod +x "${OFFLINE_DIR}/install_arkime_offline.sh"
    log_success "离线安装脚本生成完成"
}

# 生成说明文档
generate_documentation() {
    log_info "生成安装说明文档..."
    
    cat > "${DOCS_DIR}/README.md" << 'EOF'
# Arkime 离线安装包

本包包含了在CentOS 7.4系统上离线安装Arkime所需的所有文件。

## 目录结构

```
arkime_offline_packages/
├── yum_packages/          # RPM包和YUM仓库
├── npm_packages/          # Node.js依赖包
├── source_packages/       # 第三方源码包
├── nodejs/               # Node.js安装包
├── docs/                 # 文档
└── install_arkime_offline.sh  # 离线安装脚本
```

## 安装步骤

1. 将整个 `arkime_offline_packages` 目录复制到目标服务器
2. 以root用户运行安装脚本：
   ```bash
   cd arkime_offline_packages
   chmod +x install_arkime_offline.sh
   ./install_arkime_offline.sh
   ```

## 系统要求

- CentOS 7.4 或更高版本
- 至少4GB内存
- 至少20GB可用磁盘空间
- root权限

## 注意事项

- 安装过程中请保持网络连接稳定
- 建议在安装前备份重要数据
- 如遇到问题，请查看日志文件

EOF
    
    log_success "安装说明文档生成完成"
}

# 主函数
main() {
    log_info "开始收集Arkime离线安装包..."
    
    check_root
    create_directories
    install_tools
    collect_system_packages
    download_nodejs
    collect_source_packages
    collect_npm_packages
    generate_install_script
    generate_documentation
    
    log_success "离线安装包收集完成!"
    log_info "安装包位置: ${OFFLINE_DIR}"
    log_info "请将整个目录复制到目标服务器进行离线安装"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
