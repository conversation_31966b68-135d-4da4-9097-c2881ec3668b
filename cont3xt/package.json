{"license": "Apache-2.0", "scripts": {"bundle": "cd ../ && npm run cont3xt:bundle", "bundle:min": "cd ../ && npm run cont3xt:build", "addtestuser": "cd ../ && npm run cont3xt:addtestuser", "dev": "cd ../ && npm run cont3xt:dev", "doc": "cd ../ && npm run cont3xt:doc", "lint": "cd ../ && npm run cont3xt:lint", "test": "cd ../ && npm run cont3xt:testui"}, "engines": {"node": ">= 18.15.0 < 21", "npm": ">= 3.0.0"}, "nodemonConfig": {"ignore": ["node_modules", "vueap<PERSON>"], "watch": [".", "../common", "../tests/cont3xt.ini"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "dependencies": {"email-deep-validator": "^3.3.0", "extract-domain": "^4.1.5", "maxmind": "^4.3.22", "punycode": "^2.3.1", "whois-json": "^2.0.4"}}