/* lighten dark table rows on-hover */
body.dark .table-striped tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* dim border colors on dark tables */
body.dark .table-striped tbody tr td,
body.dark .table-striped tfoot th,
body.dark .table-striped thead th {
    border-color: #525252;
}

/* remove default colorings from bootstrap dark variant */
.table-dark {
    background-color: revert;
}

/* use light text on tags for light and dark theme */
.tag {
    color: #EEE;
}

/* external link buttons fill their entire cell when in a table */
td .integration-external-link-button {
    width: 100%;
    height: 100%;
    padding: 0 !important;
}

/* reorder list handle for link groups and overview fields */
.link-handle {
    top: 18px;
    left: -9px;
    z-index: 10;
    padding: 5px 6px;
    position: relative;
    border-radius: 14px;
    background: var(--secondary);
}

.gap-1 {
    gap: 0.25rem;
}
.gap-2 {
    gap: 0.5rem;
}
.gap-3 {
    gap: 1rem;
}
