User Settings Description - an object with each sub object a setting.
  * sub objects can have
    * help - some help text
    * password - if true then its a password
    * uiSetting - true if value is needed in the UI (do not set true for secure values like passwords)


Card Description - an object of the following
    - NOTE: see also Overviews section in pages/Help
  * title - title of the card
  * fields - an array of fields.
    * If the element is just a string that means
      * both "label" and "field" are that string
      * type is string
    * otherwise it can be an object with
      * label - the field label to show the user
      * field - the path (it can have dots) to the data for field, if not set the field is the same as name. For table type this will be the path to the array.
      * path - alternative to field, this is the field path pre-separated into a string array (eg. field: 'foo.bar' is equivalent to path: ['foo', 'bar'])
      * fields - for tables, the list of fields, same format as this
      * type - if not set it is string
        * string - obvious
        * url - a url that should be made clickable
        * externalLink - a button that leads out to an external link
        * table - there will be a fields element
        * array - the field var will point to an array, display 1 per line unless join set
        * date - a date value
        * ms - a ms time value
        * seconds - a second time value
        * json - just display raw json, call in JSON.stringify(blah, false, 2)
        * dnsRecords - display of non-A/AAAA dns records (for use with DNS integration and `path: []`)
      * defang - when true defang the string, change http to hXXp and change . to [.]
      * pivot - when set this field should include a dropdown action menu, options include:
        * copy - show a copy option in the pivot dropdown to copy the value
        * pivot - replace query with the value
        * custom - create a custom button
          * field - requires path (see path above)
          * name - name to display on the button
          * href - the url for the button. use %{var} to replace pieces of the url when constructing the card
      * join - with array, display with value as the separator on one line (example single: ', ')
      * fieldRoot - for arrays/tables, the path (it can have dots) from each object in the array to its desired field. Effectively maps the root array of objects to an array of values/sub-objects.
      * fieldRootPath - alternative to fieldRoot, this is the fieldRoot path pre-separated into a string array (eg. fieldRoot: 'foo.bar' is equivalent to fieldRootPath: ['foo', 'bar'])
      * filterEmpty - for arrays/tables, removes empty (nullish & empty string/array) rows/elements when true (default is true)
      * defaultSortField - with array, sorts the table by this field
      * defaultSortDirection - with array and defaultSortField, sorts the table in this direction ('asc' or 'desc')
      * altText - optional text to be displayed on externalLink tooltip instead of URL
      * noSearch - for tables, boolean to turn off search-ability on column, default false (but true for externalLink types)
      * postProcess - one postProcessor or an array of postProcessors (see "PostProcessor Description") -- to customize value

Tidbit Description - an object of the following
  * order - the default order to apply to all fields
  * fields - an array of the following objects (used for specifying tidbit UI)
    * label - the field label to show the user. If set, this will cause the tidbit to be shown as a key-value pair at the bottom of the display.
    * field - the path (it can have dots) to the data for field, if not set the field is the same as name. For table type this will be the path to the array.
    * fields - for tables, the list of fields, same format as this
    * type - if not set it is string
      * string - obvious
      * array - the field var will point to an array
      * date - a date value
      * ms - a ms time value
      * seconds - a second time value
      * json - just display raw json, call in JSON.stringify(blah, false, 2)
    * fieldRoot - for arrays, the path (it can have dots) from each object in the array to its desired field. Effectively maps the root array of objects to an array of values/sub-objects.
    * template - string used to create display value with pseudo string-interpolation (applied before postProcess)
      * string with angle brackets surrounding paths (they can have dots) that are replaced by the data at that path (starting at value [the field] or data [entire data object]). Example: 'one value: <value> and two value: <data.something.otherValue>'
    * postProcess - one postProcessor or an array of postProcessors (see "PostProcessor Description") -- to customize value
    * tooltip - the string to display as the tooltip
    * tooltipTemplate - pseudo string-interpolation string used to generate tooltip
      * see "template" for details on templating
    * display - describes how the resulting value is displayed (default is badge)
      * badge - simple badge with information
      * cont3xtField - clickable field with options: "copy", "pivot"
      * cont3xtCopyLink - clickable field with option: "copy link"
      * <color>Group - (for array type), color options: primary, secondary, success, info, warning, danger
    * order - number by which tidbits are sorted (ascending order), default 0
    * purpose - optional, string to identify the purpose of a tidbit when the same information can be provided by more than one source. When multiple valid tidbits have the same purpose, only the one with the highest precedence will be kept
    * precedence - order of preference as a number, where a higher precedence level makes a data-source more preferred for the given purpose
      * only required when purpose is set

PostProcessor Description - can be either of the following
  * a string that identifies the name of the parameter-less filter to be applied (e.g. 'trim')
  * an object, whose filter's name is identified by the primary parameter's key (e.g. { mapTo: 'some.nested.value' })
