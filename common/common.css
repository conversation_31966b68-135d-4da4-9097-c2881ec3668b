/*!
 *  Common CSS for all web apps (viewer, wise, parliament)
 */

/* ENTIRE PAGE ------------------------------- */
body {
 -webkit-font-smoothing: antialiased;
 -moz-osx-font-smoothing: grayscale;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
}

/* VARIABLES --------------------------------- */
body {
  --px-none   : 0;          /* 0px */
  --px-xs     : 0.125rem;   /* 2px */
  --px-sm     : .25rem;     /* 4px */
  --px-md     : .5rem;      /* 8px */
  --px-lg     : .75rem;     /* 12px */
  --px-xlg    : 1rem;       /* 16px */
  --px-xxlg   : 1.5rem;     /* 24px */
  --px-xxxlg  : 2rem;       /* 32px */
  --px-xxxxlg : 3rem;       /* 48px */
  --px-xxxxxlg: 3.5rem;     /* 56px */
}

/* MISC -------------------------------------- */
.margin-for-nav {
  margin-top: 70px !important;
}

/* widths */
.w-40 { width: 40% !important; }

/* cursors */
.cursor-help, .help-cursor { cursor: help; }
.cursor-text, .text-cursor { cursor: text; }
.cursor-move, .move-cursor { cursor: move; }
.cursor-grab, .grab-cursor { cursor: grab; }
.cursor-pointer, .pointer-cursor { cursor: pointer; }
.cursor-crosshair, .crosshair-cursor { cursor: crosshair; }

/* font sizes */
.xsmall { font-size: 50%; }
.medium { font-size: 95%; }
.large  { font-size: 1.8rem; }
.xlarge { font-size: 2rem; }

.bold   { font-weight: bold; }

/* wrapping */
.no-wrap { white-space: nowrap; }

/* overflow */
.no-overflow    { overflow: hidden; }
.no-overflow-x  { overflow-x: hidden; }
.no-overflow-y  { overflow-y: hidden; }
.break-word     { word-break: break-word; }
.word-break     { word-break: break-word; }
.break-all      { word-break: break-all; }
.all-break      { word-break: break-all; }

.ellipsis { text-overflow: ellipsis; }

/* display */
.display-inline { display: inline; }
.display-inline-block { display: inline-block; }

/* remove underline for anchor tag hover */
.no-decoration { text-decoration: none !important; }

/* small/condensed horizontal rule */
.hr-small {
  margin-top: var(--px-sm);
  margin-bottom: var(--px-xs);
}

/* extra small buttons */
.btn-xs, .btn-group-xs > .btn {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.dropdown-btn-xs > button.dropdown-toggle {
  padding: 1px 5px;
  font-size: 12px;
  border-radius: 3px;
}

/* user roles dropdown scrolls for long lists */
.roles-dropdown ul {
  overflow: auto;
  max-width: 220px;
  max-height: 200px;
}

/* user dropdown scrolls for long lists */
/* padding helps hide content behind sticky search bar */
.users-dropdown ul {
  overflow: auto;
  min-width: 500px;
  max-width: 600px;
  max-height: 200px;
  padding-top: 0 !important;
}

.users-dropdown form {
  padding-inline: 0 !important;
}

/* xs input groups */
.input-group-xs > .form-control,
.input-group-xs > .custom-select,
.input-group-xs > .input-group-prepend > .input-group-text,
.input-group-xs > .input-group-append > .input-group-text,
.input-group-xs > .input-group-prepend > .btn,
.input-group-xs > .input-group-append > .btn {
  line-height: 1;
  padding: 0.25rem;
  font-size: 0.825rem;
  border-radius: 2px;
}
.input-group-xs > .custom-select,
.input-group-xs > .form-control:not(textarea) {
  height: calc(0.825em + 0.5rem + 4px);
}

/* INFO AREA --------------------------------- */
/* displays large text for important information
 * note: must contain an inner div with the text
 * example:
 * <div class="info-area">
 *   <div>Some important text!</div>
 * </div>
 */
.info-area {
  font-size: var(--px-xxxlg);
}

.info-area > div {
  padding: var(--px-xxxlg);
  border-radius: var(--px-sm);
}

.info-area span.fa {
  display: flex;
  justify-content: center;
}

/* display for whole page info
 * takes up the entire screen except navbar
 * used for 404's and browser upgrade pages */
.whole-page-info {
  margin-top: 20px;
}
.whole-page-info .center-area > img {
  z-index: 99;
}
.whole-page-info .center-area {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex-direction: column;
  min-height: 75vh;
}
.whole-page-info .well {
  margin-top: -6px;
  min-width: 25%;
  padding: 12px;
  background-color: #F6F6F6;
  border: 1px solid #EEEEEE;
  border-radius: 3px;
  box-shadow: 4px 4px 10px 0 rgba(0,0,0,0.5);
}
.whole-page-info .well > h1 {
  margin-top: 0;
  color: #DB0A65;
}

/* flex block to display text centered
 * (horizontally and vertically) on the page
 * note: takes up half of the page
 */
.vertical-horizontal-center {
  min-height      : 40vh;
  display         : flex;
  align-items     : center;
  justify-content : center;
  text-align      : center;
  flex-direction  : column;
}

.vertical-center {
  min-height      : 40vh;
  display         : flex;
  align-items     : center;
  justify-content : center;
  flex-direction  : column;
}

.horizontal-center {
  display         : flex;
  align-items     : center;
  justify-content : center;
  text-align      : center;
  flex-direction  : column;
}

/* LASTPASS ---------------------------------- */
/* remove lastpass icons from all inputs
 * could also use 'data-lpignore="true"' on each individual input
 * that we want to exclude */
div[id^=__lpform_] {
  display: none;
}
input {
  background-image: none !important;
  background-attachment: none !important;
}

/* DARK THEME -------------------------------- */
/* darken background/lighten text */
body.dark {
  background-color: #222;
  color: #EEE;
}

/* darken navbar */
body.dark .navbar.bg-dark {
  background-color: #131313 !important;
}

/* darken cards */
body.dark .card {
  background-color: #303030;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
body.dark .card .card-header,
body.dark .card .card-footer {
  background-color: #383838;
}
body.dark .cluster-handle {
  background: #303030;
}

/* darken subtext */
.subtext {
  color: #777777;
}
body.dark .subtext {
  color: #bfbfbf;
}

/* darken info area */
body.dark .info-area > div {
  background-color: #444;
  color: #CCC !important;
}

/* darken pre */
body.dark pre:not([class*="text-"]) {
  color: white !important;
}

/* darken alerts */
body.dark .alert.alert-danger {
  color: #FCABCA;
  background-color: #8E2049;
  border-color: #871D45;
}
body.dark .alert .issue-date {
  color: #9F9F9F;
}

/* darken warnings */
body.dark .alert.alert-warning {
  color: #FFF3CD;
  background-color: #916012;
  border-color: #895A10;
}

/* darken dropdown menus */
body.dark .dropdown-menu {
  background-color: #222;
  border: 1px solid #333;
}
body.dark .dropdown-menu .dropdown-item,
body.dark .dropdown-menu .b-dropdown-form {
  color: #EEE;
}
body.dark .dropdown-menu .dropdown-item:hover,
body.dark .dropdown-menu .dropdown-item:focus {
  color: #EEE;
  text-decoration: none;
  background-color: #444;
}

/* lighten dark buttons */
body.dark .btn-outline-dark {
  color: #ADB5BD;
}
body.dark .btn-outline-dark:hover {
  color: #222;
  background-color: #ADB5BD;
  border-color: #ADB5BD;
}

body.dark hr {
  background-color: #ADB5BD;
}

/* darken tables */
body.dark .table-danger,
body.dark .table-danger > th,
body.dark .table-danger > td {
  background-color: #871D45;
}
body.dark .table-hover .table-danger:hover,
body.dark .table-hover .table-danger:hover > th,
body.dark .table-hover .table-danger:hover > td {
  background-color: #A32252;
}
body.dark .table-warning,
body.dark .table-warning > th,
body.dark .table-warning > td {
  background-color: #895A10;
}
body.dark .table-hover .table-warning:hover,
body.dark .table-hover .table-warning:hover > th,
body.dark .table-hover .table-warning:hover > td {
  background-color: #9B6611;
}
body.dark .table-secondary,
body.dark .table-secondary > th,
body.dark .table-secondary > td {
  background-color: #494949;
}
body.dark .table-hover .table-secondary:hover,
body.dark .table-hover .table-secondary:hover > th,
body.dark .table-hover .table-secondary:hover > td {
  background-color: #4F4F4F;
}
body.dark .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

/* lighten table text in dark mode */
body.dark table.table {
  color: #EEE;
}
body.dark table.table-hover tbody tr:hover {
  color: #EEE;
}

/* dark form controls */
body.dark input.form-control,
body.dark input.form-control:focus,
body.dark select.form-control,
body.dark select.form-control:focus,
body.dark textarea.form-control,
body.dark textarea.form-control:focus {
  color: #FFF;
  background-color: #000;
}
body.dark .input-group-prepend > .input-group-text,
body.dark .input-group-append:not(.color) > .input-group-text {
  border-color: gray !important;
  color: #EEE !important;
  background-color: #555 !important;
}

/* darken navs */
body.dark .nav > li > a:hover,
body.dark .nav > li > a:focus {
  color: #2B91FF;
  background-color: #222;
}
body.dark .nav-tabs > li.nav-item > a.nav-link.active,
body.dark .nav-tabs > li.nav-item > a.nav-link.active:hover,
body.dark .nav-tabs > li.nav-item > a.nav-link.active:focus {
  color: #FFF;
  background-color: #222;
  border-bottom: 1px solid transparent;
}

/* dark wells */
body.dark .well {
  background-color: #333;
  border-color: #444;
}

/* dark paging controls */
body.dark .pagination-info {
  display: inline-block;
  font-size: .8rem;
  color: #999;
  border: 1px solid #F6F6F6;
  padding: 5px 10px;
  margin-left: -6px;
  border-radius: 0 var(--px-sm) var(--px-sm) 0;
  background-color: #333;
}
body.dark .pagination .page-item > .page-link {
  cursor: pointer !important;
  color: #FFF;
  background-color: #333;
  border: 1px solid #F6F6F6;
}
body.dark .pagination .page-item.disabled {
  cursor: not-allowed !important;
}
body.dark .pagination .page-item.disabled > span,
body.dark .pagination .page-item.disabled > span:hover,
body.dark .pagination .page-item.disabled > span:focus,
body.dark .pagination .page-item.disabled > a,
body.dark .pagination .page-item.disabled > a:hover,
body.dark .pagination .page-item.disabled > button:focus,
body.dark .pagination .page-item.disabled > button,
body.dark .pagination .page-item.disabled > button:hover,
body.dark .pagination .page-item.disabled > button:focus {
  color: #777;
  border-color: #F6F6F6;
  background-color: #333;
}

body.dark .pagination .page-item.active > a,
body.dark .pagination .page-item.active > span,
body.dark .pagination .page-item.active > button,
body.dark .pagination .page-item.active > a:hover,
body.dark .pagination .page-item.active > span:hover,
body.dark .pagination .page-item.active > button:hover,
body.dark .pagination .page-item.active > a:focus,
body.dark .pagination .page-item.active > span:focus,
body.dark .pagination .page-item.active > button:focus {
  z-index: 3;
  cursor: default;
  color: #FFF;
  border-color: #F6F6F6;
  background-color: #111;
}

/* dark modal */
body.dark .modal-content {
  color: #EEE;
  background-color: #222;
}
body.dark .modal-content .modal-header,
body.dark .modal-content .modal-footer {
  background-color: #333;
}

/* dark panel */
body.dark .b-sidebar-body {
  color: #EEE;
  background-color: #333;
}

/* dark alert */
body.dark .alert-dark {
  color: #EEE;
  border-color: #444;
  background-color: #333;
}

/* ANIMATIONS -------------------------------- */
/* for transition groups (any block element)
   enters/exits items by moving them to the right */
.list-enter-active, .list-leave-active {
  transition: all .5s;
}
.list-enter, .list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
.list-move {
  transition: transform .5s;
}

/* for transition groups (must use lists - styles added to remove list stying)
   expands/shrinks each item in the group in/out
   NOTE: make sure 300px is tall enough */
.shrink-list {
  margin-block-end: 0;
  margin-block-start: 0;
  padding-inline-start: 0;
}
.shrink-item {
  display: block;
  transition: all 1s;
}
.shrink-enter, .shrink-leave-to {
  max-height: 0px;
  overflow: hidden;
  padding-top: 0px;
  padding-bottom: 0px;
}
.shrink-enter-to, .shrink-leave {
  max-height: 300px;
}

/* for any single element
   expands/shrinks the item in/out
   NOTE: make sure 300px is tall enough */
.item-shrink-enter-active, .item-shrink-leave-active {
  transition: all .5s;
}
.item-shrink-enter, .item-shrink-leave-to {
  max-height: 0px;
  overflow: hidden;
}
.item-shrink-enter-to, .item-shrink-leave {
  max-height: 300px;
}

/* for any single element
   fades the item in/out */
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* for transition groups (any block element)
   enters/exits items by fading them */
.fade-list-enter-active:not(.no-transition),
.fade-list-leave-active:not(.no-transition) {
  transition: opacity .3s;
}
.fade-list-enter:not(.no-transition),
.fade-list-leave-to:not(.no-transition) {
  opacity: 0;
}
.fade-list-move:not(.no-transition) {
  transition: transform .3s;
}

/* confirm button animations */
.buttons-enter-active {
  animation-delay: .5s;
  animation: bounce-in 1s;
}
.buttons-leave-active {
  transition: all .1s ease-out;
}
.buttons-enter, .buttons-leave-to {
  opacity: 0;
}
@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  25% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* MEDIA QUERIES ----------------------------- */
@media (min-width: 1600px) {
  .col-xxl-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
}
