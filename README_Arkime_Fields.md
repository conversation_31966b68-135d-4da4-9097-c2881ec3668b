# Arkime字段系统完整文档

本文档集提供了Arkime字段系统的完整参考，包含所有字段定义、内置字段和映射关系。

## 📁 文档结构

### 核心文档

#### `Arkime_Complete_Field_Reference.md`
**完整的字段参考手册** - 主要文档
- ✅ **内置字段完整列表** (15个)
- ✅ **字段定义完整统计** (224个)
- ✅ **字段映射关系详解**
- ✅ **查询示例和最佳实践**
- ✅ **dbField vs dbField2 区别说明**

#### `arkime_fields_clean.json`
**字段定义数据文件** - 程序处理用
- ✅ **224个字段的ES格式定义**
- ✅ **标准Elasticsearch v30格式**
- ✅ **可直接用于ES导入或程序解析**

## 📊 内容概览

### 字段统计
```
总字段数: 239个
├── 已定义字段: 224个 (通过arkime_field_define注册)
│   ├── general: 42个     (通用字段)
│   ├── http: 37个        (HTTP协议)
│   ├── dns: 29个         (DNS协议)
│   ├── email: 19个       (邮件协议)
│   ├── cert: 18个        (证书相关)
│   ├── tls: 12个         (TLS/SSL)
│   └── 其他: 67个        (SMB、SSH、DHCP等)
└── 内置字段: 15个 (直接在会话数据中使用)
    ├── 基础时间: 3个     (@timestamp, firstPacket, lastPacket)
    ├── 会话信息: 4个     (node, ipProtocol, length, protocols)
    ├── 文件引用: 2个     (fileId, packetPos)
    └── 网络结构: 6个     (source.*, destination.*, network.*)
```

### 字段类型分布
- **termfield**: 156个 (可搜索字符串)
- **integer**: 44个 (整数类型)
- **ip**: 12个 (IP地址)
- **textfield**: 10个 (文本字段)
- **date**: 2个 (日期时间)

## 🔗 核心映射关系

### 1. 内置字段 (直接映射)
```
会话数据字段 → 查询路径
@timestamp → @timestamp
firstPacket → firstPacket
source.ip → source.ip
destination.mac → destination.mac
```

### 2. 定义字段 (通过dbField2映射)
```
字段定义ID → dbField2 → 会话数据路径
mac.src → source.mac → source.mac
host.http → http.host → http.host
ip.dns → dns.ip → dns.ip
```

### 3. 协议字段 (嵌套对象)
```
字段定义ID → 会话数据路径
http.method → http.method
dns.host → dns.host
tls.ja4 → tls.ja4
```

## 🔍 快速查询指南

### 查询内置字段
```json
// 查询特定时间范围
{"query": {"range": {"firstPacket": {"gte": 1041342931000}}}}

// 查询特定节点
{"query": {"term": {"node": "localhost"}}}

// 查询TCP协议
{"query": {"term": {"ipProtocol": 6}}}
```

### 查询定义字段
```json
// 查询HTTP方法 (注意使用dbField2路径)
{"query": {"term": {"http.method": "GET"}}}

// 查询源IP (注意使用dbField2路径)
{"query": {"term": {"source.ip": "*************"}}}

// 查询DNS主机 (注意使用dbField2路径)
{"query": {"term": {"dns.host": "example.com"}}}
```

## ⚠️ 重要注意事项

### 1. 字段路径使用
- ✅ **正确**: 使用dbField2的值构建查询
- ❌ **错误**: 使用字段定义ID构建查询

```json
// 正确示例
{"query": {"term": {"source.mac": "00:11:22:33:44:55"}}}

// 错误示例  
{"query": {"term": {"mac.src": "00:11:22:33:44:55"}}}
```

### 2. 内置字段 vs 定义字段
- **内置字段**: 直接使用字段路径，无需查找映射
- **定义字段**: 必须通过dbField2查找正确的会话数据路径

### 3. 协议字段嵌套
- 协议特定字段存储在对应的协议对象中
- 查询时需要使用完整的嵌套路径

## 🛠️ 开发建议

### 1. 字段查找流程
1. 确定要查询的字段类型
2. 检查是否为内置字段
3. 如果是定义字段，查找对应的dbField2值
4. 使用正确的路径构建查询

### 2. 性能优化
- 优先使用term查询进行精确匹配
- 对于文本字段使用match查询
- 合理使用聚合减少数据传输

### 3. 常见错误避免
- 不要混淆字段定义ID和会话数据路径
- 注意协议字段的嵌套结构
- 理解内置字段与定义字段的区别

## 📚 使用场景

### 1. 开发查询
- 参考字段映射表构建正确的ES查询
- 使用字段类型信息优化查询性能

### 2. 数据分析
- 理解会话数据结构进行深度分析
- 利用字段分组进行协议分析

### 3. 系统集成
- 使用JSON文件进行程序化处理
- 建立外部系统与Arkime的字段映射

---

## 📄 文档维护

### 更新方法
当Arkime代码库更新时，可以重新运行字段提取流程来更新文档。

### 验证方法
1. 检查字段总数是否匹配
2. 验证新增协议字段
3. 确认映射关系正确性

---

*本文档集为Arkime字段系统提供了完整的参考，是开发、运维和分析工作的重要资料。*
