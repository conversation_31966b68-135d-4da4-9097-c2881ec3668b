#!/bin/bash
# Arkime 部署测试脚本
# 用于验证部署是否成功

set -e

# 配置变量
TARGET_SERVER="192.168.31.129"
TARGET_USER="root"
TARGET_PASS="VictoR#.0.0"
ARKIME_DIR="/opt/arkime"
WEB_PORT="8005"
ES_PORT="9200"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示测试横幅
show_banner() {
    cat << 'EOF'
    
    ╔═══════════════════════════════════════════════════════════╗
    ║                                                           ║
    ║                Arkime 部署测试工具                        ║
    ║                                                           ║
    ║         验证Arkime在CentOS 7.4上的部署状态               ║
    ║                                                           ║
    ╚═══════════════════════════════════════════════════════════╝
    
EOF
}

# 执行远程命令
run_remote_command() {
    local command="$1"
    local description="$2"
    
    if [[ -n "$description" ]]; then
        log_info "$description"
    fi
    
    sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no \
        "${TARGET_USER}@${TARGET_SERVER}" "$command"
}

# 检查SSH连接
test_ssh_connection() {
    log_info "测试SSH连接..."
    
    if run_remote_command "echo 'SSH连接成功'" >/dev/null 2>&1; then
        log_success "SSH连接正常"
        return 0
    else
        log_error "SSH连接失败"
        return 1
    fi
}

# 检查系统信息
check_system_info() {
    log_info "检查系统信息..."
    
    local os_info=$(run_remote_command "cat /etc/redhat-release" "获取系统版本")
    local uptime=$(run_remote_command "uptime" "获取系统运行时间")
    local memory=$(run_remote_command "free -h | grep Mem" "获取内存信息")
    local disk=$(run_remote_command "df -h /" "获取磁盘信息")
    
    echo "系统版本: $os_info"
    echo "运行时间: $uptime"
    echo "内存信息: $memory"
    echo "磁盘信息: $disk"
    
    log_success "系统信息检查完成"
}

# 检查Arkime安装
check_arkime_installation() {
    log_info "检查Arkime安装状态..."
    
    # 检查安装目录
    if run_remote_command "test -d $ARKIME_DIR" >/dev/null 2>&1; then
        log_success "Arkime安装目录存在: $ARKIME_DIR"
    else
        log_error "Arkime安装目录不存在: $ARKIME_DIR"
        return 1
    fi
    
    # 检查主要二进制文件
    local binaries=("capture" "viewer.js")
    for binary in "${binaries[@]}"; do
        if run_remote_command "test -f $ARKIME_DIR/bin/$binary" >/dev/null 2>&1; then
            log_success "二进制文件存在: $binary"
        else
            log_error "二进制文件缺失: $binary"
            return 1
        fi
    done
    
    # 检查配置文件
    if run_remote_command "test -f $ARKIME_DIR/etc/config.ini" >/dev/null 2>&1; then
        log_success "配置文件存在"
    else
        log_error "配置文件缺失"
        return 1
    fi
    
    log_success "Arkime安装检查完成"
}

# 检查Node.js
check_nodejs() {
    log_info "检查Node.js安装..."
    
    local node_version=$(run_remote_command "node --version 2>/dev/null || echo 'not_installed'" "获取Node.js版本")
    local npm_version=$(run_remote_command "npm --version 2>/dev/null || echo 'not_installed'" "获取npm版本")
    
    if [[ "$node_version" != "not_installed" ]]; then
        log_success "Node.js版本: $node_version"
    else
        log_error "Node.js未安装"
        return 1
    fi
    
    if [[ "$npm_version" != "not_installed" ]]; then
        log_success "npm版本: $npm_version"
    else
        log_error "npm未安装"
        return 1
    fi
    
    log_success "Node.js检查完成"
}

# 检查Elasticsearch
check_elasticsearch() {
    log_info "检查Elasticsearch状态..."
    
    # 检查服务状态
    local es_status=$(run_remote_command "systemctl is-active elasticsearch 2>/dev/null || echo 'inactive'" "获取Elasticsearch服务状态")
    
    if [[ "$es_status" == "active" ]]; then
        log_success "Elasticsearch服务运行中"
    else
        log_warning "Elasticsearch服务未运行: $es_status"
    fi
    
    # 检查端口监听
    local port_status=$(run_remote_command "netstat -tlnp | grep :$ES_PORT || echo 'not_listening'" "检查Elasticsearch端口")
    
    if [[ "$port_status" != "not_listening" ]]; then
        log_success "Elasticsearch端口监听正常"
    else
        log_warning "Elasticsearch端口未监听"
    fi
    
    # 检查健康状态
    local health_status=$(run_remote_command "curl -s http://localhost:$ES_PORT/_cluster/health 2>/dev/null | grep -o '\"status\":\"[^\"]*\"' || echo 'unreachable'" "检查Elasticsearch健康状态")
    
    if [[ "$health_status" != "unreachable" ]]; then
        log_success "Elasticsearch健康状态: $health_status"
    else
        log_warning "Elasticsearch不可访问"
    fi
    
    log_success "Elasticsearch检查完成"
}

# 检查Arkime服务
check_arkime_services() {
    log_info "检查Arkime服务状态..."
    
    local services=("arkimecapture" "arkimeviewer")
    
    for service in "${services[@]}"; do
        local status=$(run_remote_command "systemctl is-active $service 2>/dev/null || echo 'inactive'" "获取$service服务状态")
        
        if [[ "$status" == "active" ]]; then
            log_success "$service 服务运行中"
        else
            log_warning "$service 服务未运行: $status"
        fi
    done
    
    # 检查Web端口
    local web_status=$(run_remote_command "netstat -tlnp | grep :$WEB_PORT || echo 'not_listening'" "检查Arkime Web端口")
    
    if [[ "$web_status" != "not_listening" ]]; then
        log_success "Arkime Web端口监听正常"
    else
        log_warning "Arkime Web端口未监听"
    fi
    
    log_success "Arkime服务检查完成"
}

# 检查网络接口
check_network_interfaces() {
    log_info "检查网络接口配置..."
    
    local interfaces=$(run_remote_command "ip addr show | grep '^[0-9]' | awk '{print \$2}' | sed 's/:$//' | grep -v lo" "获取网络接口列表")
    
    echo "可用网络接口:"
    echo "$interfaces"
    
    # 检查配置文件中的接口设置
    local configured_interface=$(run_remote_command "grep '^interface=' $ARKIME_DIR/etc/config.ini 2>/dev/null | cut -d'=' -f2 || echo 'not_configured'" "获取配置的网络接口")
    
    if [[ "$configured_interface" != "not_configured" ]]; then
        log_success "配置的网络接口: $configured_interface"
    else
        log_warning "网络接口未配置"
    fi
    
    log_success "网络接口检查完成"
}

# 检查数据目录
check_data_directories() {
    log_info "检查数据目录..."
    
    local data_dirs=("$ARKIME_DIR/data" "$ARKIME_DIR/logs" "$ARKIME_DIR/data/raw")
    
    for dir in "${data_dirs[@]}"; do
        if run_remote_command "test -d $dir" >/dev/null 2>&1; then
            local size=$(run_remote_command "du -sh $dir 2>/dev/null | cut -f1" "获取目录大小")
            log_success "数据目录存在: $dir ($size)"
        else
            log_warning "数据目录不存在: $dir"
        fi
    done
    
    # 检查磁盘空间
    local disk_usage=$(run_remote_command "df -h $ARKIME_DIR | tail -1 | awk '{print \$5}'" "获取磁盘使用率")
    log_info "数据分区磁盘使用率: $disk_usage"
    
    log_success "数据目录检查完成"
}

# 测试Web访问
test_web_access() {
    log_info "测试Web界面访问..."
    
    # 测试HTTP访问
    local http_status=$(run_remote_command "curl -s -o /dev/null -w '%{http_code}' http://localhost:$WEB_PORT 2>/dev/null || echo 'failed'" "测试HTTP访问")
    
    if [[ "$http_status" =~ ^[23][0-9][0-9]$ ]]; then
        log_success "HTTP访问正常: $http_status"
    else
        log_warning "HTTP访问失败: $http_status"
    fi
    
    # 测试HTTPS访问
    local https_status=$(run_remote_command "curl -s -k -o /dev/null -w '%{http_code}' https://localhost:$WEB_PORT 2>/dev/null || echo 'failed'" "测试HTTPS访问")
    
    if [[ "$https_status" =~ ^[23][0-9][0-9]$ ]]; then
        log_success "HTTPS访问正常: $https_status"
    else
        log_warning "HTTPS访问失败: $https_status"
    fi
    
    log_success "Web访问测试完成"
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    local report_file="/tmp/arkime_test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
Arkime 部署测试报告
生成时间: $(date)
目标服务器: $TARGET_SERVER

=== 测试结果摘要 ===
$(if test_ssh_connection >/dev/null 2>&1; then echo "✓ SSH连接: 正常"; else echo "✗ SSH连接: 失败"; fi)
$(if check_arkime_installation >/dev/null 2>&1; then echo "✓ Arkime安装: 正常"; else echo "✗ Arkime安装: 异常"; fi)
$(if check_nodejs >/dev/null 2>&1; then echo "✓ Node.js: 正常"; else echo "✗ Node.js: 异常"; fi)

=== 详细信息 ===
系统信息: $(run_remote_command "cat /etc/redhat-release")
Arkime版本: $(run_remote_command "cat $ARKIME_DIR/VERSION 2>/dev/null || echo '未知'")
Node.js版本: $(run_remote_command "node --version 2>/dev/null || echo '未安装'")

=== 建议 ===
1. 如有服务未运行，请检查日志文件
2. 确保防火墙配置正确
3. 验证网络接口配置
4. 检查Elasticsearch集群状态

EOF
    
    echo "测试报告已生成: $report_file"
    cat "$report_file"
}

# 主测试流程
main() {
    show_banner
    
    log_info "开始Arkime部署测试..."
    
    # 基础连接测试
    test_ssh_connection || exit 1
    
    # 系统信息检查
    check_system_info
    
    # 安装状态检查
    check_arkime_installation
    check_nodejs
    check_elasticsearch
    check_arkime_services
    check_network_interfaces
    check_data_directories
    
    # 功能测试
    test_web_access
    
    # 生成报告
    generate_test_report
    
    log_success "Arkime部署测试完成!"
    
    echo ""
    echo "=== 快速访问信息 ==="
    echo "Web界面: https://$TARGET_SERVER:$WEB_PORT"
    echo "SSH连接: ssh $TARGET_USER@$TARGET_SERVER"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 检查sshpass
    if ! command -v sshpass &> /dev/null; then
        log_error "需要安装sshpass工具"
        log_info "CentOS/RHEL: yum install -y sshpass"
        log_info "Ubuntu/Debian: apt-get install -y sshpass"
        exit 1
    fi
    
    main "$@"
fi
