-- general 映射文件，将common字段适配成general字段，用于Arkime session构造
-- 这个脚本将common层的字段映射到general层，C代码会从general对象中提取信息构造Arkime必须字段：
-- srcAddr/dstAddr -> source.ip/destination.ip
-- srcPort/dstPort -> source.port/destination.port
-- upBytes/downBytes -> source.bytes/destination.bytes
-- upPackets/downPackets -> source.packets/destination.packets
-- srcMac/dstMac -> source.mac[]/destination.mac[]
-- firstPacket/lastPacket/length/proto -> session第一级字段

local mapping_general = {
  from_proto_name = "common",
  to_proto_name = "general",
  rule_proto_name = "general",
  rule_name_flag = false,       -- 如果 false ，去 init.lua 的静态表里面找 rule_name
  field = {
    -- 基础网络信息字段
    {from_name = "srcAddr"       ,to_name = "srcAddr"       ,tll = 1},
    {from_name = "dstAddr"       ,to_name = "dstAddr"       ,tll = 1},
    {from_name = "srcPort"       ,to_name = "srcPort"       ,tll = 1},
    {from_name = "dstPort"       ,to_name = "dstPort"       ,tll = 1},
    {from_name = "ipVer"         ,to_name = "ipVer"         ,tll = 1},
    {from_name = "proto"         ,to_name = "proto"         ,tll = 1},
    
    -- 时间相关字段 - 适配为Arkime标准字段
    {from_name = "begTime"       ,to_name = "firstPacket"   ,tll = 1},  -- 会话首包时间戳
    {from_name = "endTime"       ,to_name = "lastPacket"    ,tll = 1},  -- 会话末包时间戳
    {from_name = "comDur"        ,to_name = "length"        ,tll = 1},  -- 会话持续时间(毫秒)
    {from_name = "captureTime"   ,to_name = "captureTime"   ,tll = 1},  -- 捕获时间
    
    -- 流量统计字段
    {from_name = "upBytes"       ,to_name = "upBytes"       ,tll = 1},
    {from_name = "downBytes"     ,to_name = "downBytes"     ,tll = 1},
    {from_name = "upPackets"     ,to_name = "upPackets"     ,tll = 1},
    {from_name = "downPackets"   ,to_name = "downPackets"   ,tll = 1},
    {from_name = "totalBytes"    ,to_name = "totalBytes"    ,tll = 1},
    {from_name = "totalPackets"  ,to_name = "totalPackets"  ,tll = 1},
    
    -- MAC地址相关字段
    {from_name = "srcMac"        ,to_name = "srcMac"        ,tll = 1},
    {from_name = "dstMac"        ,to_name = "dstMac"        ,tll = 1},
    {from_name = "srcMacOui"     ,to_name = "srcMacOui"     ,tll = 1},
    {from_name = "dstMacOui"     ,to_name = "dstMacOui"     ,tll = 1},
    
    -- 网络层信息
    {from_name = "outTransProto" ,to_name = "outTransProto" ,tll = 1},
    {from_name = "outSrcPort"    ,to_name = "outSrcPort"    ,tll = 1},
    {from_name = "outDstPort"    ,to_name = "outDstPort"    ,tll = 1},
    {from_name = "outer.ipv6.src",to_name = "outer.ipv6.src",tll = 1},
    {from_name = "outer.ipv6.dst",to_name = "outer.ipv6.dst",tll = 1},
    
    -- VLAN相关字段
    {from_name = "vlanId"        ,to_name = "vlanId"        ,tll = 1},
    {from_name = "vlanPri"       ,to_name = "vlanPri"       ,tll = 1},
    
    -- MPLS相关字段
    {from_name = "isIPv6"        ,to_name = "isIPv6"        ,tll = 1},
    {from_name = "isMPLS"        ,to_name = "isMPLS"        ,tll = 1},
    {from_name = "nLabel"        ,to_name = "nLabel"        ,tll = 1},
    {from_name = "innerLabel"    ,to_name = "innerLabel"    ,tll = 1},
    {from_name = "otherLabel"    ,to_name = "otherLabel"    ,tll = 1},
    
    -- 设备和线路信息
    {from_name = "DevNo"         ,to_name = "DevNo"         ,tll = 1},
    {from_name = "LineNo"        ,to_name = "LineNo"        ,tll = 1},
    {from_name = "lineName1"     ,to_name = "lineName1"     ,tll = 1},
    {from_name = "lineName2"     ,to_name = "lineName2"     ,tll = 1},
    {from_name = "LinkLayerType" ,to_name = "LinkLayerType" ,tll = 1},
    
    -- 地理位置相关字段
    {from_name = "SrcCountry"    ,to_name = "SrcCountry"    ,tll = 1},
    {from_name = "SrcArea"       ,to_name = "SrcArea"       ,tll = 1},
    {from_name = "SrcCity"       ,to_name = "SrcCity"       ,tll = 1},
    {from_name = "SrcCarrier"    ,to_name = "SrcCarrier"    ,tll = 1},
    {from_name = "DstCountry"    ,to_name = "DstCountry"    ,tll = 1},
    {from_name = "DstArea"       ,to_name = "DstArea"       ,tll = 1},
    {from_name = "DstCity"       ,to_name = "DstCity"       ,tll = 1},
    {from_name = "DstCarrier"    ,to_name = "DstCarrier"    ,tll = 1},
    
    -- 运营商相关字段
    {from_name = "OPERATOR_TYPE" ,to_name = "OPERATOR_TYPE" ,tll = 1},
    {from_name = "TAGTYPE"       ,to_name = "TAGTYPE"       ,tll = 1},
    
    -- 华为相关字段
    {from_name = "HW_NCODE"      ,to_name = "HW_NCODE"      ,tll = 1},
    {from_name = "HW_ACCOUNT"    ,to_name = "HW_ACCOUNT"    ,tll = 1},
    {from_name = "HW_ESN"        ,to_name = "HW_ESN"        ,tll = 1},
    {from_name = "HW_MEID"       ,to_name = "HW_MEID"       ,tll = 1},
    {from_name = "HW_LAC"        ,to_name = "HW_LAC"        ,tll = 1},
    {from_name = "HW_SAC"        ,to_name = "HW_SAC"        ,tll = 1},
    
    -- RTL相关字段
    {from_name = "RTL_TEID"      ,to_name = "RTL_TEID"      ,tll = 1},
    {from_name = "RTL_OUTTER_SRC",to_name = "RTL_OUTTER_SRC",tll = 1},
    {from_name = "RTL_OUTTER_DST",to_name = "RTL_OUTTER_DST",tll = 1},
    {from_name = "RTL_MSISDN"    ,to_name = "RTL_MSISDN"    ,tll = 1},
    {from_name = "RTL_IMEI"      ,to_name = "RTL_IMEI"      ,tll = 1},
    {from_name = "RTL_IMSI"      ,to_name = "RTL_IMSI"      ,tll = 1},
    {from_name = "RTL_TAC"       ,to_name = "RTL_TAC"       ,tll = 1},
    {from_name = "RTL_PLMN_ID"   ,to_name = "RTL_PLMN_ID"   ,tll = 1},
    {from_name = "RTL_ULI"       ,to_name = "RTL_ULI"       ,tll = 1},
    {from_name = "RTL_ENODE_ID"  ,to_name = "RTL_ENODE_ID"  ,tll = 1},
    {from_name = "RTL_CELL_ID"   ,to_name = "RTL_CELL_ID"   ,tll = 1},
    {from_name = "RTL_BS"        ,to_name = "RTL_BS"        ,tll = 1},
    
    -- 安全相关字段
    {from_name = "filesecdeg"    ,to_name = "filesecdeg"    ,tll = 1},
    {from_name = "secdegpro"     ,to_name = "secdegpro"     ,tll = 1},
    
    -- 其他字段
    {from_name = "CapDate"       ,to_name = "CapDate"       ,tll = 1},
    {from_name = "SrcIp"         ,to_name = "SrcIp"         ,tll = 1},
    {from_name = "resv8"         ,to_name = "resv8"         ,tll = 1}
  }
}

-- 注册general适配器
yalua_register_proto(mapping_general)

return mapping_general
