/****************************************************************************************
 * 文 件 名 : dpi_ftp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <stdlib.h>
#include <string.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "libsdt/sdt_types.h"

#define EPRT_AF_IPv4 1
#define EPRT_AF_IPv6 2

#define FTP_CONTROL_PORT 21
#define FTP_DATA_PORT 20

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;

/* add by liugh */
extern int session_protocol_st_size[PROTOCOL_MAX];

struct ftp_info
{
    char      software[64];
    char      hostName[64];
    char      username[64];
    char      password[64];
    char      filename[1024];        //文件名
    uint64_t  filesize;
    uint16_t  ctl_srcport;        //源端口
    uint16_t  ctl_dstport;        //目的端口
    uint8_t is_request;
    char cmd_or_code[32];
    char args[256];
    char met[1024];                //发送的ftp命令
    char met_contend[1024];        //ftp命令参数
    char ret_code[8];            //服务器响应码
    char ret_con[1024];            //返回消息内容
    int offset;                    //文件传输起始偏移位置
    char con_type[8];            //传输文件类型
    char     login_status[COMMON_STATUS_LEN];
    char      server_ip[64];
};

enum ftp_index_em{
    EM_FTP_TYPE,
    EM_FTP_USERNAME,
    EM_FTP_PASSWORD,
    EM_FTP_FILENAME,
    EM_FTP_CONTROL_SRCPORT,
    EM_FTP_CONTROL_DSTPORT,
    EM_FTP_USER_FILE_PATH,
    EM_FTP_CONTROL_CMD,
    EM_FTP_CONTROL_ARGS,
    EM_FTP_SERVER_NAME,
    EM_FTP_SERVER_IP,
    EM_FTP_DATA_FILETYPE,
    EM_FTP_DATA_FILEPATH,
    EM_FTP_MET,
    EM_FTP_MET_CONTEND,
    EM_FTP_RET_CODE,
    EM_FTP_RET_CON,
    EM_FTP_OFFSET,
    EM_FTP_CON_TYPE,
    EM_FTP_DATA_PORT,
    EM_FTP_FILE_SIZE,
    EM_FTP_DATA_IP,
    EM_FTP_MODE,
    EM_FTP_DATA_TOTAL_LEN,  // for test
    EM_FTP_DATA_REAL_LEN,   // for test
    EM_FTP_LOGIN_STATUS,
    EM_FTP_DATA_HOSTNAME,
    EM_FTP_DATA_SOFTWARE,
    EM_FTP_MAX
};

static dpi_field_table  ftp_field_array[] = {
    DPI_FIELD_D(EM_FTP_TYPE,                        YA_FT_UINT16,       "Ftp_type"),
    DPI_FIELD_D(EM_FTP_USERNAME,                    YA_FT_STRING,       "Username"),
    DPI_FIELD_D(EM_FTP_PASSWORD,                    YA_FT_STRING,       "Password"),
    DPI_FIELD_D(EM_FTP_FILENAME,                    YA_FT_STRING,       "Filename"),
    DPI_FIELD_D(EM_FTP_CONTROL_SRCPORT,             YA_FT_UINT16,       "Control_srcport"),
    DPI_FIELD_D(EM_FTP_CONTROL_DSTPORT,             YA_FT_UINT16,       "Control_dstport"),
    DPI_FIELD_D(EM_FTP_USER_FILE_PATH,              YA_FT_STRING,       "User_filepath"),
    DPI_FIELD_D(EM_FTP_CONTROL_CMD,                 YA_FT_STRING,       "Control_cmd"),
    DPI_FIELD_D(EM_FTP_CONTROL_ARGS,                YA_FT_STRING,       "Control_args"),
    DPI_FIELD_D(EM_FTP_SERVER_NAME,                 YA_FT_STRING,       "Server_name"),
    DPI_FIELD_D(EM_FTP_SERVER_IP,                   YA_FT_STRING,       "Server_IP"),
    DPI_FIELD_D(EM_FTP_DATA_FILETYPE,               YA_FT_STRING,       "Data_filetype"),
    DPI_FIELD_D(EM_FTP_DATA_FILEPATH,               YA_FT_STRING,       "Data_filepath"),
    DPI_FIELD_D(EM_FTP_MET,                         YA_FT_STRING,       "Method"),
    DPI_FIELD_D(EM_FTP_MET_CONTEND,                 YA_FT_STRING,       "Method_content"),
    DPI_FIELD_D(EM_FTP_RET_CODE,                    YA_FT_INT32,        "Return_code"),
    DPI_FIELD_D(EM_FTP_RET_CON,                     YA_FT_STRING,       "Return_content"),
    DPI_FIELD_D(EM_FTP_OFFSET,                      YA_FT_STRING,       "Offset"),
    DPI_FIELD_D(EM_FTP_CON_TYPE,                    YA_FT_STRING,       "Content_type"),
    DPI_FIELD_D(EM_FTP_DATA_PORT,                   YA_FT_UINT32,       "DataPort"),
    DPI_FIELD_D(EM_FTP_FILE_SIZE,                   YA_FT_UINT64,       "FileSize"),
    DPI_FIELD_D(EM_FTP_DATA_IP,                     YA_FT_UINT32,       "DataIp"),
    DPI_FIELD_D(EM_FTP_MODE,                        YA_FT_UINT8,        "Mode"),
    DPI_FIELD_D(EM_FTP_DATA_TOTAL_LEN,              YA_FT_STRING,       "Data_totallen"),
    DPI_FIELD_D(EM_FTP_DATA_REAL_LEN,               YA_FT_STRING,       "Data_reallen"),
    DPI_FIELD_D(EM_FTP_LOGIN_STATUS,                YA_FT_UINT8,        "LoginStatus"),
    DPI_FIELD_D(EM_FTP_DATA_HOSTNAME,               YA_FT_STRING,       "hostName"),
    DPI_FIELD_D(EM_FTP_DATA_SOFTWARE,               YA_FT_STRING,       "software"),
};


/*
*ftp的tbl日志函数
*/
static
void ftp_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct ftp_info *info, int *idx, int i)
{
  struct ftp_session *session = (struct ftp_session *)flow->app_session;
    switch(i){
    case EM_FTP_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)"ftp-control", strlen("ftp-control"));
        break;
    case EM_FTP_USERNAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->username, strlen(info->username));
        break;
    case EM_FTP_PASSWORD:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->password,strlen(info->password));
        break;
    case EM_FTP_FILENAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->filename,strlen(info->filename));
        break;
    case EM_FTP_CONTROL_CMD:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->cmd_or_code, strlen(info->cmd_or_code));
        break;
    case EM_FTP_CONTROL_ARGS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->args, strlen(info->args));
        break;
    case EM_FTP_CONTROL_DSTPORT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ntohs(info->ctl_dstport));
        break;
    case EM_FTP_CONTROL_SRCPORT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, ntohs(info->ctl_srcport));
        break;
    case EM_FTP_MET:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->met, strlen(info->met));
        break;
    case EM_FTP_MET_CONTEND:
        // write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->met, strlen(info->met));
        // log_ptr->record[*idx - 1] = ' ';
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->met_contend, strlen(info->met_contend));
        break;
    case EM_FTP_RET_CODE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, atoi(info->ret_code));
        // write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->ret_code, strlen(info->ret_code));
        break;
    case EM_FTP_RET_CON:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->ret_con, strlen(info->ret_con));
        break;
    case EM_FTP_OFFSET:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->offset);
        break;
    case EM_FTP_CON_TYPE:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->con_type, strlen(info->con_type));
        break;
    case EM_FTP_LOGIN_STATUS:
        if (strncmp(info->login_status, "YES", strlen("YES")) == 0) {
          write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        } else {
          write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 0);
        }
        // write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->login_status, strlen((const char *)info->login_status));
        break;
    case EM_FTP_FILE_SIZE:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->filesize);
        break;
    case EM_FTP_DATA_PORT:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, htons(session->ftp_port));
        break;
    case EM_FTP_SERVER_IP:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->server_ip, strlen((const char *)info->server_ip));
        break;
    case EM_FTP_DATA_HOSTNAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->hostName,
            strlen((const char *)info->hostName));
        break;
    case EM_FTP_DATA_SOFTWARE:
        {
          char str_[2048] = {0};
          if (strlen(info->software)!= 0) {
            snprintf(str_, sizeof(str_), "%s %s ", info->hostName, info->software);
          }
          write_coupler_log(
              log_ptr->record, idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)str_, strlen((const char *)str_));
        }
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    return;
}


static int write_ftp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{


    int idx = 0,i=0;
    struct tbl_log *log_ptr;

    const char *str= NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    struct ftp_info *info=(struct ftp_info *)field_info;
    if(!info){
        return PKT_DROP;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ftp");

    for(i=0; i<EM_FTP_MAX;i++){
        ftp_field_element(log_ptr,flow, direction, info, &idx, i);
    }

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_FTP_CONTROL;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    log_ptr->flow        = flow;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}

/*ftp control的识别函数，主要基于端口识别，增加一些过滤判断*/
static void identify_ftp_control(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    int line_len;

    if (g_config.protocol_switch[PROTOCOL_FTP_CONTROL] == 0)
        return;

    if(payload_len>1460){return;}

    /* Exclude SMTP, which uses similar commands. */
    if (flow->tuple.inner.port_src == htons(25) || flow->tuple.inner.port_src == htons(25)) {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_FTP_CONTROL);
        return;
    }

    if (flow->pkt_first_line.has_search == 0) {
        line_len = find_packet_line_end(payload, payload_len);
        flow->pkt_first_line.has_search = 1;
        flow->pkt_first_line.linelen = line_len;
    } else {
        line_len = flow->pkt_first_line.linelen;
    }

    if (line_len <= 0)
        return;
#ifdef ACCURACY
    if (ndpi_ftp_control_check_request(payload, payload_len) || ndpi_ftp_control_check_response(payload, payload_len)) {
        flow->real_protocol_id = PROTOCOL_FTP_CONTROL;
    }
#else

    if (flow->tuple.inner.port_src == htons(g_config.ftp_port) || flow->tuple.inner.port_dst == htons(g_config.ftp_port)) {
        flow->real_protocol_id = PROTOCOL_FTP_CONTROL;
    }
    else if (flow->tuple.inner.port_src == htons(2121) || flow->tuple.inner.port_dst == htons(2121)) {
        flow->real_protocol_id = PROTOCOL_FTP_CONTROL;
    }
#endif

    return;
}

static uint8_t isvalid_rfc2428_delimiter(const char c)
{
    /* RFC2428 sect. 2 states rules for a valid delimiter */
    const char *forbidden = "0123456789abcdef.:";
    if (!isgraph(c))
        return 0;
    if (strchr(forbidden, tolower(c)))
        return 0;
    return 1;
}

/*
*此函数用于ftp-data会话的识别，port请求和pasv响应中会有ip和端口号，目前ftp-data的识别可能不需要
*/
static int parse_port_pasv(const uint8_t *line, int linelen, uint32_t *ftp_ip, uint16_t *ftp_port)
{
    char     args[1024];
    char     *p;
    char      c;
    int       i;
    int       ip_address[4], port[2];
    uint8_t   ret = 0;

    size_t copy_len =  (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
    strncpy(args, (const char *)line, copy_len);
    args[copy_len] = 0;

    p = args;

    for (;;) {
        /*
        * Look for a digit.
        */
        while ((c = *p) != '\0' && !isdigit(c))
            p++;

        if (*p == '\0') {
            /*
            * We ran out of text without finding anything.
            */
            break;
        }

        /*
        * See if we have six numbers.
        */
        i = sscanf(p, "%d,%d,%d,%d,%d,%d",
                &ip_address[0], &ip_address[1], &ip_address[2], &ip_address[3],
                &port[0], &port[1]);
        if (i == 6) {
            /*
            * We have a winner!
            */
            *ftp_port = ((port[0] & 0xFF) << 8) | (port[1] & 0xFF);
            *ftp_ip = htonl((ip_address[0] << 24) | (ip_address[1] << 16) | (ip_address[2] << 8) | ip_address[3]);
#if 0
            *pasv_offset = (uint32)(p - args);
            *ftp_port_len = (port[0] < 10 ? 1 : (port[0] < 100 ? 2 : 3 )) + 1 +
            (port[1] < 10 ? 1 : (port[1] < 100 ? 2 : 3 ));
            *ftp_ip_len = (ip_address[0] < 10 ? 1 : (ip_address[0] < 100 ? 2 : 3)) + 1 +
            (ip_address[1] < 10 ? 1 : (ip_address[1] < 100 ? 2 : 3)) + 1 +
            (ip_address[2] < 10 ? 1 : (ip_address[2] < 100 ? 2 : 3)) + 1 +
            (ip_address[3] < 10 ? 1 : (ip_address[3] < 100 ? 2 : 3));
#endif
            ret = 1;
            break;
        }

        /*
        * Well, that didn't work.  Skip the first number we found,
        * and keep trying.
        */
        while ((c = *p) != '\0' && isdigit(c))
            p++;
    }

    return ret;
}

/*eprt命令的解析，和port，pasv类似*/
static uint8_t parse_eprt_request(const uint8_t *line, int linelen, uint32_t *eprt_af,
        uint32_t *eprt_ip, uint16_t *eprt_ipv6, uint16_t *ftp_port)
{
    int      delimiters_seen = 0;
    char     delimiter;
    int      fieldlen;
    char    *field;
    int      n;
    int      lastn;
    char     *p;
    char      args[1024];
    uint8_t   ret = 0;
    size_t copy_len;


    /* line contains the EPRT parameters, we need at least the 4 delimiters */
    if (!line || linelen < 4)
        return 0;

    copy_len = (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
    strncpy(args, (const char *)line, copy_len);
    args[copy_len] = 0;

    p = args;
    /*
    * Handle a NUL being in the line; if there's a NUL in the line,
    * strlen(args) will terminate at the NUL and will thus return
    * a value less than linelen.
    */
    if (strlen(args) < (unsigned)linelen)
        linelen = strlen(args);

    /*
    * RFC2428 sect. 2 states ...
    *
    *     The EPRT command keyword MUST be followed by a single space (ASCII
    *     32). Following the space, a delimiter character (<d>) MUST be
    *     specified.
    *
    * ... the preceding <space> is already stripped so we know that the first
    * character must be the delimiter and has just to be checked to be valid.
    */
    if (!isvalid_rfc2428_delimiter(*p))
        return 0;  /* EPRT command does not follow a vaild delimiter;
                    * malformed EPRT command - immediate escape */

    delimiter = *p;
    /* Validate that the delimiter occurs 4 times in the string */
    for (n = 0; n < linelen; n++) {
        if (*(p + n) == delimiter)
        delimiters_seen++;
    }

    if (delimiters_seen != 4)
        return 0; /* delimiter doesn't occur 4 times
                    * probably no EPRT request - immediate escape */

    /* we know that the first character is a delimiter... */
    delimiters_seen = 1;
    lastn = 0;
    /* ... so we can start searching from the 2nd onwards */
    for (n = 1; n < linelen; n++) {

        if (*(p + n) != delimiter)
            continue;

        /* we found a delimiter */
        delimiters_seen++;

        fieldlen = n - lastn - 1;
        if (fieldlen <= 0)
            return 0; /* all fields must have data in them */
        field = p + lastn + 1;

        if (delimiters_seen == 2) {     /* end of address family field */
            char af_str[64] = {0};
            strncpy(af_str, field, (uint32_t)fieldlen >= sizeof(af_str) ? (sizeof(af_str) - 1) : (uint32_t)fieldlen);
            *eprt_af = atoi(af_str);
        }
        else if (delimiters_seen == 3) {/* end of IP address field */
            char ip_str[64] = {0};
            strncpy(ip_str, field, (uint32_t)fieldlen >= sizeof(ip_str) ? (sizeof(ip_str) - 1) : (uint32_t)fieldlen);

            if (*eprt_af == EPRT_AF_IPv4) {
                if (inet_pton(AF_INET, ip_str, eprt_ip))
                    ret = 1;
                else
                    ret = 0;
            }
            else if (*eprt_af == EPRT_AF_IPv6) {
                if (inet_pton(AF_INET6, ip_str, eprt_ipv6))
                    ret = 1;
                else
                    ret = 0;
            }
            else
                return 0; /* invalid/unknown address family */

        }
        else if (delimiters_seen == 4) {/* end of port field */
            char pt_str[64] = {0};
            strncpy(pt_str, field, (uint32_t)fieldlen >= sizeof(pt_str) ? (sizeof(pt_str) - 1) : (uint32_t)fieldlen);
            *ftp_port = (uint16_t)atoi(pt_str);
        }
        lastn = n;
    }

    return ret;
}

/*epasv命令的解析，和port，pasv类似*/
static uint8_t parse_extended_pasv_response(const uint8_t *line, int linelen, uint16_t *ftp_port)
{
    int          n;
    char      args[1024];
    char      *p;
    char       c;
    uint8_t   ret = 0;
    uint8_t   delimiters_seen = 0;
    size_t    copy_len;
    /*
     * Copy the rest of the line into a null-terminated buffer.
     */
    copy_len = (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
    strncpy(args, (const char *)line, copy_len);
    args[copy_len] = 0;

    p = args;

    /*
     * Look for ( <d> <d> <d>
       (Try to cope with '(' in description)
     */
    for ( ; !delimiters_seen; ) {
        char delimiter = '\0';
        while ((c = *p) != '\0' && (c != '('))
            p++;

        if (*p == '\0') {
            return 0;
        }

        /* Skip '(' */
        p++;

        /* Make sure same delimiter is used 3 times */
        for (n = 0; n < 3; n++) {
            if ((c = *p) != '\0') {
                if (delimiter == '\0' && isvalid_rfc2428_delimiter(c)) {
                    delimiter = c;
                }
                if (c != delimiter) {
                    break;
                }
                p++;
            }
            else {
                break;
            }
        }
        delimiters_seen = 1;
    }

    /*
     * Should now be at digits.
     */
    if (*p != '\0') {
        int port_valid = atoi(p);

        if (port_valid > 0)
            *ftp_port = (uint16_t)port_valid;
        else
            ret = 0;
    }

    return ret;
}


/*
*ftp-data的tbl日志函数
*/
static void write_ftp_data_log(struct flow_info *flow, int direction, struct ftp_session *info)
{
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ftp");
    int i;
    for(i=0;i<EM_FTP_MAX;i++){
        switch(ftp_field_array[i].index){
            case EM_FTP_TYPE:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)"ftp-data", strlen("ftp-data"));
                break;
            case EM_FTP_USERNAME:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->username, strlen(info->username));
                break;
            case EM_FTP_PASSWORD:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->password,strlen(info->password));
                break;
            case EM_FTP_CONTROL_SRCPORT:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, NULL,ntohs(info->port_src));
                break;
            case EM_FTP_CONTROL_DSTPORT:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, NULL, ntohs(info->port_dst));
                break;
            case EM_FTP_USER_FILE_PATH:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->storpath, strlen(info->storpath));
                break;
            case EM_FTP_DATA_FILETYPE:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->filetype, strlen(info->filetype));
                break;
            case EM_FTP_DATA_FILEPATH:
                if(strlen(info->filepath)>0){
                    char filename[128]={0};
                    if(get_filename(info->filepath, filename)){
                        write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *) filename, strlen(filename));
                        break;
                    }
                }
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, (const uint8_t *)info->filepath, strlen(info->filepath));
                break;
            case EM_FTP_DATA_TOTAL_LEN:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, NULL, info->total_len);
                break;
            case EM_FTP_DATA_REAL_LEN:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ftp_field_array[i].type, NULL, info->real_len);
                break;
            default:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
                break;

        }

    }

    //最后一个|去掉
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_FTP_CONTROL;
    log_ptr->log_len = idx - 1;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return;
}

static int dissect_ftp_server_info(struct ftp_info *info, char *code_str) {
    if (code_str == NULL) {
        return -1;
    }
    char *space_ptr = strchr(code_str, ' ');
    if (space_ptr == NULL) {
        return -1;
    }

    char *server_info_start = space_ptr + 1;
    if (*server_info_start == '(') {
        server_info_start++;
        char *end_ptr = strchr(server_info_start, ')');
        if (end_ptr == NULL) {
            return -1;
        }
        *end_ptr = '\0';
    } else {
        char *end_ptr = strchr(server_info_start, '[');
        if (end_ptr != NULL) {
            *end_ptr = '\0';
        }
    }

    char *token = strtok(server_info_start, " ");
    if (token != NULL) {
        strcpy(info->hostName, token);

        token = strtok(NULL, " ");
        if (token != NULL) {
            strcpy(info->software, token);
        }
    } else {
        return -1;
    }
    return 0;
}

/*
*ftp-control的解析入口函数
*/
static int dissect_ftp_control(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(seq);
    UNUSED(flag);

    uint8_t is_request;
    uint8_t is_port_request = 0;
    uint8_t is_eprt_request = 0;
    uint8_t is_pasv_response = 0;
    uint8_t is_epasv_response = 0;
    size_t copy_len;
    int linelen;
    uint32_t tokenlen = 0;
    const uint8_t *next_token;
    const uint8_t *line;
    char code_str[4];
    int code = 0;
    struct ftp_info info;
    struct ftp_session *session;  // add by liugh
    char    str[128] = { 0 };

    memset(&info, 0, sizeof(info));

    if (direction == FLOW_DIR_DST2SRC)
      is_request = 0;
    else
      is_request = 1;

//    is_request = 1-direction;

    info.is_request = is_request;
    line = payload;

    if (flow->pkt_first_line.has_search == 0) {
        linelen = find_packet_line_end(payload, payload_len);
        flow->pkt_first_line.has_search = 1;
        flow->pkt_first_line.linelen = linelen;
    } else {
        linelen = flow->pkt_first_line.linelen;
    }

    if (linelen <= 0) {
        return PKT_DROP;
    }

    /*add by liugh*/
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct ftp_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct ftp_session));
        session = (struct ftp_session *)flow->app_session;
    }
    session = (struct ftp_session *)flow->app_session;


    if (is_request) {
        /*
        * Extract the first token, and, if there is a first
        * token, add it as the request.
        */
        tokenlen = dpi_get_token_len(line, line + linelen, &next_token);
        if (tokenlen != 0) {
            if (strncmp((const char *)line, "PORT", tokenlen) == 0)
                is_port_request = 1;
            /*
            * EPRT request command, as per RFC 2428
            */
            else if (strncmp((const char *)line, "EPRT", tokenlen) == 0)
                is_eprt_request = 1;

            copy_len = tokenlen >= sizeof(info.cmd_or_code) ? (sizeof(info.cmd_or_code) - 1) : tokenlen;
            strncpy(info.cmd_or_code, (const char *)line, copy_len);

            info.cmd_or_code[copy_len] = 0;
        }

    } else {
        /*
        * This is a response; the response code is 3 digits,
        * followed by a space or hyphen, possibly followed by
        * text.
        *
        * If the line doesn't start with 3 digits, it's part of
        * a continuation.
        *
        * XXX - keep track of state in the first pass, and
        * treat non-continuation lines not beginning with digits
        * as errors?
        */
        if (linelen >= 3 && isdigit(line[0]) && isdigit(line[1])
                && isdigit(line[2])) {
            /*
            * One-line reply, or first or last line
            * of a multi-line reply.
            */
            memcpy(code_str, line, sizeof(code_str) - 1);
            code_str[sizeof(code_str) - 1] = 0;

            code = atoi(code_str);
            /*
            * See if it's a passive-mode response.
            *
            * XXX - does anybody do FOOBAR, as per RFC
            * 1639, or has that been supplanted by RFC 2428?
            */
            if (code == 227)
                is_pasv_response = 1;

            /*
            * Responses to EPSV command, as per RFC 2428
            */
            if (code == 229)
                is_epasv_response = 1;

            if (code == 230){
                strncpy(info.login_status,"YES",COMMON_STATUS_LEN);
            }
            if (code == 530){
                strncpy(info.login_status,"NO",COMMON_STATUS_LEN);
            }
            else if (code == 220) {
                dissect_ftp_server_info(&info,(char*)line);
            }
            /*
            * Skip the 3 digits and, if present, the
            * space or hyphen.
            */
            if (linelen >= 4)
                next_token = line + 4;
            else
                next_token = line + linelen;

            strncpy(info.cmd_or_code, (const char *)line, 3);
            info.cmd_or_code[3] = 0;
        } else {
            /*
            * Line doesn't start with 3 digits; assume it's
            * a line in the middle of a multi-line reply.
            */
            next_token = line;
        }
    }

    linelen -= (int)(next_token - line);
    line = next_token;

    if (linelen > 0) {
        copy_len = (uint32_t)linelen >= sizeof(info.args) ? (sizeof(info.args) - 1) : (uint32_t)linelen;
        strncpy(info.args, (const char *)line, copy_len);
        info.args[copy_len] = 0;

        if(memcmp(info.cmd_or_code,"retr",4)==0 || memcmp(info.cmd_or_code,"stor",4)==0){
            strncpy(session->storpath,info.args, strlen(info.args));
        }
        if (strncasecmp(info.cmd_or_code,"retr",4)==0 || strncasecmp(info.cmd_or_code,"stor",4)==0 || strncasecmp(info.cmd_or_code,"stou",4)==0){
            strncpy(info.filename,info.args, strlen(info.args));
        }
    }

    if (is_request)
    {
        strncpy(info.met, info.cmd_or_code, sizeof(info.cmd_or_code) - 1);
        strncpy(info.met_contend, info.args, sizeof(info.args) - 1);
    }
    else
    {
        strncpy(info.ret_code, info.cmd_or_code, sizeof(info.ret_code) - 1);
        strncpy(info.ret_con, info.args, sizeof(info.args) - 1);

    }

    if (strcasecmp(info.cmd_or_code, "USER") == 0){
        strcpy(info.username, info.args);
        strncpy(session->username, info.username, sizeof(info.username)-1);
    }
    else if (strcasecmp(info.cmd_or_code, "PASS") == 0){
        strcpy(info.password, info.args);
        strncpy(session->password, info.password, sizeof(info.password)-1);

        /** 临时修复bug 2416, user 和 pwd 一起输出*/
        strcpy(info.username, session->username);
    }
    else if (strcasecmp(info.cmd_or_code, "REST") == 0)
    {
        info.offset = atoi(info.args);
    }
    else if (strcasecmp(info.cmd_or_code, "TYPE") == 0)
    {
        if (strcasecmp(info.args, "A") == 0 || strcasecmp(info.args, "ASCII") == 0)
        {
            strncpy(info.con_type, "ASCII", strlen("ASCII"));
        }
        else if (strcasecmp(info.args, "E") == 0 || strcasecmp(info.args, "EBCDIC") == 0)
        {
            strncpy(info.con_type, "EBCDIC", strlen("EBCDIC"));
        }
        else if (strcasecmp(info.args, "i") == 0 || strcasecmp(info.args, "BINARY") == 0)
        {
            strncpy(info.con_type, "BINARY", strlen("BINARY"));
        }
    }

    if (!is_request) {
        switch(code){
        case 213:
          strcpy(str, info.args);
          info.filesize = atol(str);
          break;
        default:
          break;
        }
      }
    info.ctl_dstport = flow->tuple.inner.port_dst;
    info.ctl_srcport = flow->tuple.inner.port_src;

    uint32_t ftp_ip = 0;
    uint16_t ftp_port = 0;
    uint16_t eprt_ipv6[8];
    uint32_t eprt_af = 0;
    uint32_t eprt_ip = 0;

    if (is_port_request || is_pasv_response) {
        parse_port_pasv(line, linelen, &ftp_ip, &ftp_port);

        session->port_dst=flow->tuple.inner.port_dst;
        session->port_src=flow->tuple.inner.port_src;
        session->ftp_ip = ftp_ip;
        session->ftp_port = htons(ftp_port);

        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;
        tuple.ip_src.ip4 = ftp_ip;
        tuple.port_src = htons(ftp_port);
        if (direction == FLOW_DIR_SRC2DST)
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        else
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
        find_or_create_conversation(&tuple, NO_PORT_B, PROTOCOL_FTP_DATA,session);
        if(is_pasv_response)
            get_ipstring(flow->ip_version, (char *)&ftp_ip, info.server_ip, sizeof(info.server_ip));
    }

    if (is_eprt_request) {
        parse_eprt_request(line, linelen, &eprt_af, &eprt_ip, eprt_ipv6, &ftp_port);

        session->port_dst=flow->tuple.inner.port_dst;
        session->port_src=flow->tuple.inner.port_src;

        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;
        if (eprt_af == EPRT_AF_IPv4)
            tuple.ip_src.ip4 = eprt_ip;
        else if (eprt_af == EPRT_AF_IPv6)
            memcpy(tuple.ip_src.ip6, eprt_ipv6, sizeof(tuple.ip_src.ip6));

        tuple.port_src = htons(ftp_port);
        if (direction == FLOW_DIR_SRC2DST)
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        else
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
        find_or_create_conversation(&tuple, NO_PORT_B, PROTOCOL_FTP_DATA,session);
    }

    if (is_epasv_response) {

//        copy_address_shallow(&ftp_ip_address, &pinfo->src);
        parse_extended_pasv_response(line, linelen, &ftp_port);
        struct conversation_tuple tuple;
        memset(&tuple, 0, sizeof(tuple));
        tuple.proto = IPPROTO_TCP;

        session->port_dst=flow->tuple.inner.port_dst;
        session->port_src=flow->tuple.inner.port_src;

        tuple.port_src = htons(ftp_port);
        if (direction == FLOW_DIR_SRC2DST) {
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
        } else {
            memcpy(&tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_src));
            memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
        }
        find_or_create_conversation(&tuple, NO_PORT_B, PROTOCOL_FTP_DATA,session);

        get_ipstring(flow->ip_version, (char *)flow->tuple.inner.ip_src,
                    info.server_ip, sizeof(info.server_ip));
    }

    write_ftp_log(flow, direction, &info, NULL);

    if(code == 234){
        flow->real_protocol_id = PROTOCOL_FTPS;
        free(flow->app_session);
        flow->app_session = NULL;
    }


    return 0;
}

/*
*ftp-data的识别函数，不全，没有pasv等的识别，暂时没有使用
*/
static void identify_ftp_data(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    UNUSED(payload);
    UNUSED(payload_len);
    uint16_t s_port = 0, d_port = 0;
    s_port = ntohs(flow->tuple.inner.port_src);
    d_port = ntohs(flow->tuple.inner.port_dst);

    if (s_port == FTP_DATA_PORT || d_port == FTP_DATA_PORT)
        flow->real_protocol_id = PROTOCOL_FTP_DATA;

    return;
}

#if 0
/*
*ftp-data的解析函数
*/
static int dissect_ftp_data(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(flow);
    UNUSED(direction);
    UNUSED(seq);
    UNUSED(payload);
    UNUSED(payload_len);
    UNUSED(flag);

    struct ftp_session *session;  // add by liugh
    /*add by liugh*/
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct ftp_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct ftp_session));
        session = (struct ftp_session *)flow->app_session;
    }
    session = (struct ftp_session *)flow->app_session;


#if 1
    if (flag == DISSECT_PKT_ORIGINAL) {
        if (direction == FLOW_DIR_SRC2DST && flow->reassemble_pkt_src2dst_num < g_config.tcp_resseamble_max_num) {
            tcp_reassemble_add_item(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
            flow->reassemble_pkt_src2dst_num++;
        } else if (direction == FLOW_DIR_DST2SRC && flow->reassemble_pkt_dst2src_num < g_config.tcp_resseamble_max_num) {
            tcp_reassemble_add_item(&flow->reassemble_dst2src_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
            flow->reassemble_pkt_dst2src_num++;
        } else
            return PKT_DROP;
    }

    //if (flag == DISSECT_PKT_FIANL && session->file_flag==1) {
    if (flag == DISSECT_PKT_FIANL) {
        uint32_t reassemble_result_len;
        uint8_t *reassemble_result;
        struct list_head *reassemble;
        char ftp_name[COMMON_FILE_NAME]={0};

        struct tcp_reassemble *pos;
        uint32_t remainlen = 0;
        if (direction == FLOW_DIR_SRC2DST) {
            reassemble_result_len = flow->reassemble_pkt_src2dst_num * TCP_PAYLOAD_MAX_LEN;
            reassemble = &flow->reassemble_src2dst_head;
        } else {
            reassemble_result_len = flow->reassemble_pkt_dst2src_num * TCP_PAYLOAD_MAX_LEN;
            reassemble = &flow->reassemble_dst2src_head;
        }

        snprintf(ftp_name, COMMON_FILE_NAME, "%s/%s/ftp_%llu_%02u_%ld.bin",
        g_config.tbl_out_dir, tbl_log_array[TBL_LOG_FTP_CONTROL].protoname, (unsigned long long)g_config.g_now_time_usec, flow->thread_id, random());
        strncpy(session->filepath, ftp_name, COMMON_FILE_NAME);
        FILE *fp = fopen(ftp_name, "a");
        //printf("##file_name=%s\n",session->filepath);
        if(fp){
            remainlen = reassemble_result_len;
            uint32_t pkt_count=0;
            uint32_t total_len=0,first_seq=0,real_len=0;

            list_for_each_entry_reverse(pos, reassemble, node){
                if(remainlen >= pos->payload_len){
                    if(pkt_count==0){
                        uint32_t header_4bytes=get_uint32_ntohl(pos->payload,0);
                        //printf("header is:%04x",header_4bytes);
                        if(!detect_file_type(header_4bytes, session->filetype)){
                            if(dpi_is_utf8(pos->payload, pos->payload_len) > 0 ||
                                  dpi_is_gbk(pos->payload, pos->payload_len) > 0){
                                  strncpy(session->filetype,"text",COMMON_SOME_TYPE);
                               }
                        }
                        first_seq=pos->seq;
                    }
                    pkt_count=1;
                    real_len+=pos->payload_len;
                    total_len=pos->seq-first_seq+pos->payload_len;
                    //printf("@@pos->payload_len=%d\n",pos->payload_len);
                    fwrite(pos->payload, pos->payload_len, 1, fp);
                }
            }
            fclose(fp);
            session->total_len=total_len;
            session->real_len=real_len;
            write_ftp_data_log(flow, direction, session);
        }


        if (direction == FLOW_DIR_SRC2DST)
            tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
        else
            tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);

            //dpi_free(reassemble_result);
    }
#endif
    return PKT_OK;

}

#else
/*
*ftp-data的解析函数
*/
static int dissect_ftp_data(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(flow);
    UNUSED(direction);
    UNUSED(seq);
    UNUSED(payload);
    UNUSED(payload_len);
    UNUSED(flag);

    struct ftp_session *session;  // add by liugh
    /*add by liugh*/
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct ftp_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct ftp_session));
        session = (struct ftp_session *)flow->app_session;
    }
    session = (struct ftp_session *)flow->app_session;

#if 1
    /*ftp 内容还原开关*/
    if(g_config.ftp_content){

    if (flag == DISSECT_PKT_ORIGINAL) {
        if (direction == FLOW_DIR_SRC2DST && flow->reassemble_pkt_src2dst_num < g_config.tcp_resseamble_max_num) {
            tcp_reassemble_add_item(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
            flow->reassemble_pkt_src2dst_num++;
        } else if (direction == FLOW_DIR_DST2SRC && flow->reassemble_pkt_dst2src_num < g_config.tcp_resseamble_max_num) {
            tcp_reassemble_add_item(&flow->reassemble_dst2src_head, &flow->rsm_src2dst_len, seq, payload, payload_len);
            flow->reassemble_pkt_dst2src_num++;
        } else
            return PKT_DROP;
    }

    //if (flag == DISSECT_PKT_FIANL && session->file_flag==1) {
    if (flag == DISSECT_PKT_FIANL) {
        uint32_t reassemble_result_len;
        uint8_t *reassemble_result;
        struct list_head *reassemble;
        char ftp_name[COMMON_FILE_NAME]={0};

        struct tcp_reassemble *pos;
        uint32_t remainlen = 0;
        if (direction == FLOW_DIR_SRC2DST) {
            reassemble_result_len = flow->reassemble_pkt_src2dst_num * TCP_PAYLOAD_MAX_LEN;
            reassemble = &flow->reassemble_src2dst_head;
        } else {
            reassemble_result_len = flow->reassemble_pkt_dst2src_num * TCP_PAYLOAD_MAX_LEN;
            reassemble = &flow->reassemble_dst2src_head;
        }

        /* 如果文件大于100M 则不写文件 */
        if(reassemble_result_len>100*1024*1024){
            return PKT_OK;
        }

        reassemble_result = (uint8_t *)dpi_malloc(reassemble_result_len);
        if (reassemble_result) {
            uint32_t pkt_count=0;
            uint32_t total_len=0,first_seq=0,real_len=0;
            uint32_t index = 0;
            remainlen = reassemble_result_len;
            list_for_each_entry_reverse(pos, reassemble, node){
            if(remainlen >= pos->payload_len){
                if(pkt_count==0){
                    if(!detect_file_type(pos->payload, pos->payload_len, session->filetype)){
                        if(dpi_is_utf8(pos->payload, pos->payload_len) > 0 ||
                           dpi_is_gbk(pos->payload, pos->payload_len) > 0){
                              strncpy(session->filetype,"text",COMMON_SOME_TYPE);
                       }else{
                          strncpy(session->filetype,"unknown",COMMON_SOME_TYPE);
                       }
                    }
                    first_seq=pos->seq;
                }
                pkt_count=1;
                real_len+=pos->payload_len;
                total_len=pos->seq-first_seq+pos->payload_len;

                memcpy(reassemble_result + index, pos->payload, pos->payload_len);
                index += pos->payload_len;
                remainlen -= pos->payload_len;
            }
            }
            snprintf(session->filepath, COMMON_FILE_NAME, "%s/%s/ftp_%llu_%02u_%ld.%s",
                                                          g_config.tbl_out_dir,
                                                          tbl_log_array[TBL_LOG_FTP_CONTROL].protoname,
                                                          (unsigned long long)g_config.g_now_time_usec,
                                                          flow->thread_id,
                                                          random(),
                                                          session->filetype);
            FILE *fp = fopen(session->filepath, "w");
            if (fp) {
                fwrite(reassemble_result, reassemble_result_len, 1, fp);
                fclose(fp);
            } else DPI_SYS_LOG(DPI_LOG_WARNING, "can't open ftp reassemble result");
            dpi_free(reassemble_result);
            session->total_len=total_len;
            session->real_len=real_len;
            write_ftp_data_log(flow, direction, session);
        }

        if (direction == FLOW_DIR_SRC2DST)
            tcp_reassemble_free(&flow->reassemble_src2dst_head, &flow->rsm_src2dst_len);
        else
            tcp_reassemble_free(&flow->reassemble_dst2src_head, &flow->rsm_dst2src_len);

    }

    }
#endif
    return PKT_OK;

}
#endif



static void init_ftp_dissector(void)
{
    dpi_register_proto_schema(ftp_field_array,EM_FTP_MAX,"ftp");
    session_protocol_st_size[PROTOCOL_FTP_DATA]=sizeof(struct ftp_session);
    port_add_proto_head(IPPROTO_TCP, g_config.ftp_port, PROTOCOL_FTP_CONTROL);
    port_add_proto_head(IPPROTO_TCP, 2121, PROTOCOL_FTP_CONTROL);

    tcp_detection_array[PROTOCOL_FTP_CONTROL].proto = PROTOCOL_FTP_CONTROL;
    tcp_detection_array[PROTOCOL_FTP_CONTROL].identify_func = identify_ftp_control;
    tcp_detection_array[PROTOCOL_FTP_CONTROL].dissect_func = dissect_ftp_control;
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_FTP_CONTROL].excluded_protocol_bitmask, PROTOCOL_FTP_CONTROL);

    port_add_proto_head(IPPROTO_TCP, FTP_DATA_PORT, PROTOCOL_FTP_DATA);
    tcp_detection_array[PROTOCOL_FTP_DATA].proto = PROTOCOL_FTP_DATA;
    tcp_detection_array[PROTOCOL_FTP_DATA].identify_func = identify_ftp_data;
    tcp_detection_array[PROTOCOL_FTP_DATA].dissect_func = dissect_ftp_data;
    DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_FTP_DATA].excluded_protocol_bitmask, PROTOCOL_FTP_DATA);
    map_fields_info_register(ftp_field_array,PROTOCOL_FTP_CONTROL,EM_FTP_MAX,"ftp");
    return;
}


static __attribute((constructor)) void    before_init_ftp_control(void){
    register_tbl_array(TBL_LOG_FTP_CONTROL, 0, "ftp", init_ftp_dissector);
}
