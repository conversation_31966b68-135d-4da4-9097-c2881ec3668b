/*
 * 测试新的arkime_record函数 - 直接操作arkime_file_pos_node链表
 * 
 * 编译命令：
 * gcc -I../src -I../src/arkime -o test_arkime_record_functions test_arkime_record_functions.c ../src/dpi_detect.c -DDPI_LOG_DEBUG=1 -DDPI_LOG_INFO=2 -DDPI_LOG_WARNING=3 -DDPI_LOG_ERROR=4 -DDPI_LOG=printf
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <assert.h>

// 简化的日志宏定义
#define DPI_LOG(level, fmt, ...) printf("[%d] " fmt "\n", level, ##__VA_ARGS__)

// 包含必要的结构定义
struct arkime_file_pos_node {
    int64_t value;  // 负值表示文件号(-1*file_num)，正值表示文件偏移量
    struct arkime_file_pos_node *next;
};

// 声明要测试的函数
int arkime_record_copy_file_info(struct arkime_file_pos_node *src_list, struct arkime_file_pos_node **dst_list);
void arkime_record_clear_file_info_list(struct arkime_file_pos_node **list);
int arkime_record_get_file_count_from_list(struct arkime_file_pos_node *list);
int arkime_record_get_file_pos_array_from_list(struct arkime_file_pos_node *list, int64_t **pos_array, int *array_size);

// 辅助函数：创建测试链表
struct arkime_file_pos_node* create_test_list() {
    // 创建测试链表：文件1(-1), 位置24, 位置48, 文件2(-2), 位置100
    struct arkime_file_pos_node *head = malloc(sizeof(struct arkime_file_pos_node));
    head->value = -1;  // 文件1
    head->next = malloc(sizeof(struct arkime_file_pos_node));
    
    head->next->value = 24;  // 位置1
    head->next->next = malloc(sizeof(struct arkime_file_pos_node));
    
    head->next->next->value = 48;  // 位置2
    head->next->next->next = malloc(sizeof(struct arkime_file_pos_node));
    
    head->next->next->next->value = -2;  // 文件2
    head->next->next->next->next = malloc(sizeof(struct arkime_file_pos_node));
    
    head->next->next->next->next->value = 100;  // 位置3
    head->next->next->next->next->next = NULL;
    
    return head;
}

// 辅助函数：打印链表
void print_list(struct arkime_file_pos_node *list, const char *name) {
    printf("%s: ", name);
    struct arkime_file_pos_node *current = list;
    while (current) {
        printf("%ld ", current->value);
        current = current->next;
    }
    printf("\n");
}

// 测试复制函数
void test_copy_function() {
    printf("\n=== 测试复制函数 ===\n");
    
    struct arkime_file_pos_node *src_list = create_test_list();
    struct arkime_file_pos_node *dst_list = NULL;
    
    print_list(src_list, "源链表");
    
    int result = arkime_record_copy_file_info(src_list, &dst_list);
    assert(result == 0);
    
    print_list(dst_list, "目标链表");
    
    // 验证复制是否正确
    struct arkime_file_pos_node *src_current = src_list;
    struct arkime_file_pos_node *dst_current = dst_list;
    
    while (src_current && dst_current) {
        assert(src_current->value == dst_current->value);
        src_current = src_current->next;
        dst_current = dst_current->next;
    }
    assert(src_current == NULL && dst_current == NULL);
    
    // 清理
    arkime_record_clear_file_info_list(&src_list);
    arkime_record_clear_file_info_list(&dst_list);
    
    printf("复制函数测试通过！\n");
}

// 测试文件计数函数
void test_file_count_function() {
    printf("\n=== 测试文件计数函数 ===\n");
    
    struct arkime_file_pos_node *list = create_test_list();
    print_list(list, "测试链表");
    
    int count = arkime_record_get_file_count_from_list(list);
    printf("文件数量: %d\n", count);
    assert(count == 2);  // 应该有2个文件（-1和-2）
    
    arkime_record_clear_file_info_list(&list);
    printf("文件计数函数测试通过！\n");
}

// 测试数组获取函数
void test_array_function() {
    printf("\n=== 测试数组获取函数 ===\n");
    
    struct arkime_file_pos_node *list = create_test_list();
    print_list(list, "测试链表");
    
    int64_t *pos_array = NULL;
    int array_size = 0;
    
    int result = arkime_record_get_file_pos_array_from_list(list, &pos_array, &array_size);
    assert(result == 0);
    assert(array_size == 5);  // 应该有5个元素
    
    printf("数组内容: ");
    for (int i = 0; i < array_size; i++) {
        printf("%ld ", pos_array[i]);
    }
    printf("\n");
    
    // 验证数组内容
    assert(pos_array[0] == -1);
    assert(pos_array[1] == 24);
    assert(pos_array[2] == 48);
    assert(pos_array[3] == -2);
    assert(pos_array[4] == 100);
    
    free(pos_array);
    arkime_record_clear_file_info_list(&list);
    printf("数组获取函数测试通过！\n");
}

// 测试清理函数
void test_clear_function() {
    printf("\n=== 测试清理函数 ===\n");
    
    struct arkime_file_pos_node *list = create_test_list();
    print_list(list, "清理前");
    
    arkime_record_clear_file_info_list(&list);
    assert(list == NULL);
    
    printf("清理后: list = NULL\n");
    printf("清理函数测试通过！\n");
}

int main() {
    printf("开始测试新的arkime_record函数...\n");
    
    test_copy_function();
    test_file_count_function();
    test_array_function();
    test_clear_function();
    
    printf("\n所有测试通过！新的arkime_record函数工作正常。\n");
    printf("\n主要改进：\n");
    printf("1. 函数现在直接操作arkime_file_pos_node链表，而不是从flow和ProtoRecord中提取\n");
    printf("2. arkime_flow_add_file_info现在区分TCP与非TCP协议：\n");
    printf("   - 非TCP协议：清空现有链表，只保存当前帧（一帧对应一条session记录）\n");
    printf("   - TCP协议：追加到现有链表（一条记录对应多个报文）\n");
    printf("3. 提供了兼容性函数，现有代码无需修改\n");
    
    return 0;
}
