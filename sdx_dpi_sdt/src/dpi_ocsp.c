/****************************************************************************************
 * 文 件 名 : dpi_ocsp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh             2018/12/10
编码: liugh             2018/12/10
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/


#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"

/*  openssl header*/
#include <openssl/ocsp.h>
#include <openssl/ocsp_lcl.h>
#include <openssl/bio.h>
#include <openssl/err.h>
#include <openssl/pem.h>

#include <openssl/buffer.h>
#include <openssl/asn1.h>
#include <openssl/objects.h>
#include <openssl/bn.h>



#include "dpi_ocsp.h"


extern struct rte_mempool *tbl_log_mempool;

static dpi_field_table  ocsp_field_array[] = {
    DPI_FIELD_D(EM_OCSP_RESPONSETYPEID,              EM_F_TYPE_STRING,               "ResponseTypeId"),
    DPI_FIELD_D(EM_OCSP_BASICOCSPRESPONSE,           EM_F_TYPE_STRING,               "BasicOCSPResponse"),
    DPI_FIELD_D(EM_OCSP_ARCHIVECUTOFF,               EM_F_TYPE_STRING,               "ArchiveCutoff"),
    DPI_FIELD_D(EM_OCSP_ACCEPTABLERESPONSES,         EM_F_TYPE_STRING,               "AcceptableResponses"),
    DPI_FIELD_D(EM_OCSP_SERVICELOCATOR,              EM_F_TYPE_STRING,               "ServiceLocator"),
    DPI_FIELD_D(EM_OCSP_CRLID,                       EM_F_TYPE_STRING,               "CrlID"),
    DPI_FIELD_D(EM_OCSP_REOCSPNONCE,                 EM_F_TYPE_STRING,               "ReOcspNonce"),
    DPI_FIELD_D(EM_OCSP_NULL_ELEMENT,                EM_F_TYPE_STRING,               "NULL_element"),
    DPI_FIELD_D(EM_OCSP_TBSREQUEST_ELEMENT,          EM_F_TYPE_STRING,               "tbsRequest_element"),
    DPI_FIELD_D(EM_OCSP_OPTIONALSIGNATURE_ELEMENT,   EM_F_TYPE_STRING,               "optionalSignature_element"),

    DPI_FIELD_D(EM_OCSP_VERSION,                     EM_F_TYPE_STRING,               "version"),
    DPI_FIELD_D(EM_OCSP_REQUESTORNAME,               EM_F_TYPE_STRING,               "requestorName"),
    DPI_FIELD_D(EM_OCSP_REQUESTLIST,                 EM_F_TYPE_STRING,               "requestList"),
    DPI_FIELD_D(EM_OCSP_REQUEST_ELEMENT,             EM_F_TYPE_STRING,               "Request_element"),
    DPI_FIELD_D(EM_OCSP_REQUESTEXTENSIONS,           EM_F_TYPE_STRING,               "requestExtensions"),
    DPI_FIELD_D(EM_OCSP_SIGNATUREALGORITHM_ELEMENT,  EM_F_TYPE_STRING,               "signatureAlgorithm_element"),
    DPI_FIELD_D(EM_OCSP_SIGNATURE,                   EM_F_TYPE_HEX,                  "signature"),
    DPI_FIELD_D(EM_OCSP_CERTS,                       EM_F_TYPE_STRING,               "certs"),
    DPI_FIELD_D(EM_OCSP_CERTIFICATE_ELEMENT,         EM_F_TYPE_STRING,               "Certificate_element"),
    DPI_FIELD_D(EM_OCSP_REQCERT_ELEMENT,             EM_F_TYPE_STRING,               "reqCert_element"),
    DPI_FIELD_D(EM_OCSP_SINGLEREQUESTEXTENSIONS,     EM_F_TYPE_STRING,               "singleRequestExtensions"),

	// 0 & 1 request, 2 & 3 response
    DPI_FIELD_D(EM_OCSP_HASHALGORITHM_ELEMENT_0,     EM_F_TYPE_STRING,               "hashAlgorithm_element"),
    DPI_FIELD_D(EM_OCSP_ISSUERNAMEHASH_0,            EM_F_TYPE_STRING,               "issuerNameHash"),
    DPI_FIELD_D(EM_OCSP_ISSUERKEYHASH_0,             EM_F_TYPE_STRING,               "issuerKeyHash"),
    DPI_FIELD_D(EM_OCSP_SERIALNUMBER_0,              EM_F_TYPE_STRING,               "serialNumber"),
    DPI_FIELD_D(EM_OCSP_HASHALGORITHM_ELEMENT_1,     EM_F_TYPE_STRING,               "hashAlgorithm_element_1"),
    DPI_FIELD_D(EM_OCSP_ISSUERNAMEHASH_1,            EM_F_TYPE_STRING,               "issuerNameHash_1"),
    DPI_FIELD_D(EM_OCSP_ISSUERKEYHASH_1,             EM_F_TYPE_STRING,               "issuerKeyHash_1"),
    DPI_FIELD_D(EM_OCSP_SERIALNUMBER_1,              EM_F_TYPE_STRING,               "serialNumber_1"),
    DPI_FIELD_D(EM_OCSP_HASHALGORITHM_ELEMENT_2,     EM_F_TYPE_STRING,               "hashAlgorithm_element_2"),
    DPI_FIELD_D(EM_OCSP_ISSUERNAMEHASH_2,            EM_F_TYPE_STRING,               "issuerNameHash_2"),
    DPI_FIELD_D(EM_OCSP_ISSUERKEYHASH_2,             EM_F_TYPE_STRING,               "issuerKeyHash_2"),
    DPI_FIELD_D(EM_OCSP_SERIALNUMBER_2,              EM_F_TYPE_STRING,               "serialNumber_2"),
    DPI_FIELD_D(EM_OCSP_HASHALGORITHM_ELEMENT_3,     EM_F_TYPE_STRING,               "hashAlgorithm_element_3"),
    DPI_FIELD_D(EM_OCSP_ISSUERNAMEHASH_3,            EM_F_TYPE_STRING,               "issuerNameHash_3"),
    DPI_FIELD_D(EM_OCSP_ISSUERKEYHASH_3,             EM_F_TYPE_STRING,               "issuerKeyHash_3"),
    DPI_FIELD_D(EM_OCSP_SERIALNUMBER_3,              EM_F_TYPE_STRING,               "serialNumber_3"),

	DPI_FIELD_D(EM_OCSP_CLIENT_REQUEST_EXT,         EM_F_TYPE_STRING,                "extIDInCliReq"),
	DPI_FIELD_D(EM_OCSP_CLIENT_REQUEST_NOUNCE,      EM_F_TYPE_STRING,                "nonInCliReq"),

    DPI_FIELD_D(EM_OCSP_RESPONSESTATUS,              EM_F_TYPE_STRING,               "responseStatus"),
    DPI_FIELD_D(EM_OCSP_RESPONSEBYTES_ELEMENT,       EM_F_TYPE_STRING,               "responseBytes_element"),
    DPI_FIELD_D(EM_OCSP_RESPONSETYPE,                EM_F_TYPE_STRING,               "responseType"),
    DPI_FIELD_D(EM_OCSP_RESPONSE,                    EM_F_TYPE_STRING,               "response"),
    DPI_FIELD_D(EM_OCSP_TBSRESPONSEDATA_ELEMENT,     EM_F_TYPE_STRING,               "tbsResponseData_element"),
    DPI_FIELD_D(EM_OCSP_RESPONDERID,                 EM_F_TYPE_STRING,               "responderID"),
    DPI_FIELD_D(EM_OCSP_PRODUCEDAT,                  EM_F_TYPE_STRING,               "producedAt"),
    DPI_FIELD_D(EM_OCSP_RESPONSES,                   EM_F_TYPE_STRING,               "responses"),
    DPI_FIELD_D(EM_OCSP_SINGLERESPONSE_ELEMENT,      EM_F_TYPE_STRING,               "SingleResponse_element"),
    DPI_FIELD_D(EM_OCSP_RESPONSEEXTENSIONS,          EM_F_TYPE_STRING,               "responseExtensions"),

    DPI_FIELD_D(EM_OCSP_BYNAME,                      EM_F_TYPE_STRING,               "byName"),
    DPI_FIELD_D(EM_OCSP_BYKEY,                       EM_F_TYPE_HEX,                  "byKey"),
    DPI_FIELD_D(EM_OCSP_CERTID_ELEMENT,              EM_F_TYPE_STRING,               "certID_element"),
    DPI_FIELD_D(EM_OCSP_CERTSTATUS,                  EM_F_TYPE_STRING,               "certStatus"),
    DPI_FIELD_D(EM_OCSP_THISUPDATE,                  EM_F_TYPE_STRING,               "thisUpdate"),
    DPI_FIELD_D(EM_OCSP_NEXTUPDATE,                  EM_F_TYPE_STRING,               "nextUpdate"),
    DPI_FIELD_D(EM_OCSP_SINGLEEXTENSIONS,            EM_F_TYPE_STRING,               "singleExtensions"),
    DPI_FIELD_D(EM_OCSP_GOOD_ELEMENT,                EM_F_TYPE_STRING,               "good_element"),
    DPI_FIELD_D(EM_OCSP_REVOKED_ELEMENT,             EM_F_TYPE_STRING,               "revoked_element"),
    DPI_FIELD_D(EM_OCSP_UNKNOWN_ELEMENT,             EM_F_TYPE_STRING,               "unknown_element"),
    DPI_FIELD_D(EM_OCSP_REVOCATIONTIME,              EM_F_TYPE_STRING,               "revocationTime"),
    DPI_FIELD_D(EM_OCSP_REVOCATIONREASON,            EM_F_TYPE_STRING,               "revocationReason"),
    DPI_FIELD_D(EM_OCSP_ACCEPTABLERESPONSES_ITEM,    EM_F_TYPE_STRING,               "AcceptableResponses_item"),
    DPI_FIELD_D(EM_OCSP_ISSUER,                      EM_F_TYPE_STRING,               "issuer"),
    DPI_FIELD_D(EM_OCSP_LOCATOR,                     EM_F_TYPE_STRING,               "locator"),
    DPI_FIELD_D(EM_OCSP_CRLURL,                      EM_F_TYPE_STRING,               "crlUrl"),
    DPI_FIELD_D(EM_OCSP_CRLNUM,                      EM_F_TYPE_STRING,               "crlNum"),
    DPI_FIELD_D(EM_OCSP_CRLTIME,                     EM_F_TYPE_STRING,               "crlTime"),



    DPI_FIELD_D(EM_OCSP_X509_VERSION,                EM_F_TYPE_STRING,               "X509_version"),
    DPI_FIELD_D(EM_OCSP_X509_SERIALNUMBER,           EM_F_TYPE_HEX,                  "X509_serialNumber"),
    DPI_FIELD_D(EM_OCSP_X509_SIGNATURECERTIFICATE,   EM_F_TYPE_STRING,               "X509_signatureCertificate"),
    DPI_FIELD_D(EM_OCSP_X509_ISSUER,                 EM_F_TYPE_STRING,               "X509_issuer"),
    DPI_FIELD_D(EM_OCSP_X509_CERTNOTBEFORETIME,      EM_F_TYPE_STRING,               "X509_certNotBeforeTime"),
    DPI_FIELD_D(EM_OCSP_X509_CERTNOTAFTERTIME,       EM_F_TYPE_STRING,               "X509_certNotAfterTime"),
    DPI_FIELD_D(EM_OCSP_X509_SUBJECTNAME,            EM_F_TYPE_STRING,               "X509_subjectName"),
    DPI_FIELD_D(EM_OCSP_X509_PUBLICKEYALGORITHM,     EM_F_TYPE_STRING,               "X509_publicKeyAlgorithm"),
    DPI_FIELD_D(EM_OCSP_X509_PUBLICKEYBITS,          EM_F_TYPE_STRING,               "X509_PublickeyBits"),
    DPI_FIELD_D(EM_OCSP_X509_PUBLICMODULUS,          EM_F_TYPE_HEX,                  "X509_publicModulus"),
    DPI_FIELD_D(EM_OCSP_X509_ISSUERUNIQUEID,         EM_F_TYPE_STRING,               "X509_issuerUniqueID"),
    DPI_FIELD_D(EM_OCSP_X509_SUBJECTUNIQUEID,        EM_F_TYPE_STRING,               "X509_subjectUniqueID"),
    DPI_FIELD_D(EM_OCSP_X509_ALGORITHMID,            EM_F_TYPE_STRING,               "X509_algorithmId"),

    DPI_FIELD_D(EM_OCSP_X509_COUNTRY_NAME,           EM_F_TYPE_STRING,               "X509_country_name"),
    DPI_FIELD_D(EM_OCSP_X509_LOCAL_NAME,             EM_F_TYPE_STRING,               "X509_local_name"),
    DPI_FIELD_D(EM_OCSP_X509_STATE_NAME,             EM_F_TYPE_STRING,               "X509_state_name"),
    DPI_FIELD_D(EM_OCSP_X509_STREET_NAME,            EM_F_TYPE_STRING,               "X509_street_name"),
    DPI_FIELD_D(EM_OCSP_X509_ORG_NAME,               EM_F_TYPE_STRING,               "X509_org_name"),
    DPI_FIELD_D(EM_OCSP_X509_ORG_UNIT_NAME,          EM_F_TYPE_STRING,               "X509_unit_name"),
    DPI_FIELD_D(EM_OCSP_X509_TITLE,                  EM_F_TYPE_STRING,               "X509_title"),
    DPI_FIELD_D(EM_OCSP_X509_DISC,                   EM_F_TYPE_STRING,               "X509_disc"),
    DPI_FIELD_D(EM_OCSP_X509_BUS_CAT,                EM_F_TYPE_STRING,               "X509_buscat"),
    DPI_FIELD_D(EM_OCSP_X509_POS_ADDR,               EM_F_TYPE_STRING,               "X509_pos_addr"),
    DPI_FIELD_D(EM_OCSP_X509_POS_CODE,               EM_F_TYPE_STRING,               "X509_pos_code"),
    DPI_FIELD_D(EM_OCSP_X509_POST_BOX,               EM_F_TYPE_STRING,               "X509_pos_box"),
    DPI_FIELD_D(EM_OCSP_X509_DEL_OFF_NAME,           EM_F_TYPE_STRING,               "X509_del_off_name"),
    DPI_FIELD_D(EM_OCSP_X509_TELNUM,                 EM_F_TYPE_STRING,               "X509_telnum"),
    DPI_FIELD_D(EM_OCSP_X509_TELEXNUM,               EM_F_TYPE_STRING,               "X509_telexnum"),
    DPI_FIELD_D(EM_OCSP_X509_FASTELNUM,              EM_F_TYPE_STRING,               "X509_fastlnum"),
};

static int dissect_ocsp_x509_public_key(EVP_PKEY *pkey, ocsp_info_t    *info)
{
    int i;
    char buf[OCSP_STRING_LENGTH]={0};

    int keyid = EVP_PKEY_id(pkey);
    if(keyid ==EVP_PKEY_EC){
        struct ec_key_st *eckey = EVP_PKEY_get0_EC_KEY(pkey);
        if(NULL == eckey){
            printf("ERROR EVP_PKEY_get0_EC_KEY\n");
            return -1;
        }
        const EC_GROUP *group = EC_KEY_get0_group(eckey);
        snprintf(info->ocsp_array[EM_OCSP_X509_PUBLICKEYBITS], OCSP_STRING_LENGTH,"Public-key:%d bit",EC_GROUP_order_bits(group));

        size_t          publen = 0;
        unsigned char  *pub    = NULL;
        publen = EC_KEY_key2buf(eckey, EC_KEY_get_conv_form(eckey), &pub, NULL);
        if (publen == 0)
        {
            printf("ERROR: EC_KEY_key2buf\n");
        }

        if(publen>=OCSP_STRING_LENGTH){publen=OCSP_STRING_LENGTH;}
        for(i=0; i<(int)publen; i++){
            info->ocsp_array[EM_OCSP_X509_PUBLICMODULUS][i]=pub[i];
        }
        OPENSSL_free(pub);
    }else if(keyid ==NID_rsaEncryption){
        const RSA *rsa = EVP_PKEY_get0_RSA(pkey);
        if (rsa != NULL)
            snprintf(info->ocsp_array[EM_OCSP_X509_PUBLICKEYBITS], OCSP_STRING_LENGTH,"Public-key:%d bit",BN_num_bits(RSA_get0_n(rsa)));

        int buflen         = BN_num_bytes(RSA_get0_n(rsa)) + 1;

        buf[0]=0;
        buflen = BN_bn2bin(RSA_get0_n(rsa),( unsigned char*)(buf+1) );
        if (buf[1] & 0x80)
           buflen++;
           /* Modulus public key  */
        if(buflen>=OCSP_STRING_LENGTH){buflen=OCSP_STRING_LENGTH;}
        for(i=0; i<buflen; i++){
           info->ocsp_array[EM_OCSP_X509_PUBLICMODULUS][i]=buf[i];
        }
    }

    return 0;
}


static int dissect_ocsp_x509_serical_number( X509 *x, unsigned long nmflags,unsigned long cflag,ocsp_info_t    *info)
{
    long l;
    int i;
    ASN1_INTEGER *bs;
    const char *neg;
    int len;

//    char buf[OCSP_STRING_LENGTH]={0};
    if (!(cflag & X509_FLAG_NO_SERIAL)) {

        /*[field name] X509SericalNumber*/
        bs = X509_get_serialNumber(x);
        if(bs->length <= (int)sizeof(long)){
            l = ASN1_INTEGER_get(bs);
        }else{
            l=-1;
        }
        if (l != -1) {
            unsigned long ul;
            if (bs->type == V_ASN1_NEG_INTEGER) {
                ul = 0 - (unsigned long)l;
                neg = "-";
            } else {
                ul = l;
                neg = "";
            }
            snprintf(info->ocsp_array[EM_OCSP_X509_SERIALNUMBER], OCSP_STRING_LENGTH,"0x%lx",ul);
        } else {
            len=bs->length;
            if(len>=OCSP_STRING_LENGTH){len=OCSP_STRING_LENGTH;}
            for (i = 0; i < len; i++) {
                info->ocsp_array[EM_OCSP_X509_SERIALNUMBER][i]= bs->data[i];
            }
        }
    }


    return 0;
}


static int dissect_ocsp_x509_cert(X509 *x, unsigned long nmflags,unsigned long cflag,ocsp_info_t    *info)
{
    if (!(cflag & X509_FLAG_NO_SIGNAME)) {
        const X509_ALGOR *tsig_alg = X509_get0_tbs_sigalg(x);

        /*[field name] X509SignatureCertificate*/
        i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_X509_SIGNATURECERTIFICATE], OCSP_STRING_LENGTH, tsig_alg->algorithm);
    }

    if (!(cflag & X509_FLAG_NO_ISSUER)) {
        /*[field name] X509Issuer*/
        X509_NAME_oneline(X509_get_issuer_name(x),info->ocsp_array[EM_OCSP_X509_ISSUER], OCSP_STRING_LENGTH);
    }

    if (!(cflag & X509_FLAG_NO_VALIDITY)) {
        struct tm Certificate_time;
        /*Validity
          Not Before*/
        /*[field name] X509NotBeforeTime*/
        if(ASN1_TIME_to_tm(X509_get0_notBefore(x),  &Certificate_time) > 0){
            snprintf(info->ocsp_array[EM_OCSP_X509_CERTNOTBEFORETIME], OCSP_STRING_LENGTH,"%s",asctime(&Certificate_time));
        }

        /*[field name] X509NotAfterTime*/
        /* Not After */
        if(ASN1_TIME_to_tm(X509_get0_notAfter(x),  &Certificate_time) > 0){
            snprintf(info->ocsp_array[EM_OCSP_X509_CERTNOTAFTERTIME], OCSP_STRING_LENGTH,"%s",asctime(&Certificate_time));
        }
    }

    return 0;
}





static int dissect_ocsp_x509_subject(X509 *x, unsigned long nmflags,unsigned long cflag,ocsp_info_t    *info)
{
    char mlch = ' ';
    //int nmindent;
    EVP_PKEY *pkey = NULL;
    char buf[OCSP_STRING_LENGTH]={0};

    if ((nmflags & XN_FLAG_SEP_MASK) == XN_FLAG_SEP_MULTILINE) {
        mlch = '\n';
    }
    //if (nmflags == X509_FLAG_COMPAT)
    //    nmindent = 16;

    if (!(cflag & X509_FLAG_NO_SUBJECT)) {
        /*[field name] X509SubjectName*/
        X509_NAME_oneline(X509_get_subject_name(x), info->ocsp_array[EM_OCSP_X509_SUBJECTNAME], OCSP_STRING_LENGTH);
    }

    if (!(cflag & X509_FLAG_NO_PUBKEY)) {
       X509_PUBKEY *xpkey = X509_get_X509_PUBKEY(x);
       ASN1_OBJECT *xpoid;
       X509_PUBKEY_get0_param(&xpoid, NULL, NULL, NULL, xpkey);

       /* Subject Public Key Info
          Public Key Algorithm*/
       memset(buf, 0 ,OCSP_STRING_LENGTH);
       /*[field name] X509PublicKeyAlgorithm*/
       i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_X509_PUBLICKEYALGORITHM], OCSP_STRING_LENGTH, xpoid);

       pkey = X509_get0_pubkey(x);
       if (pkey != NULL) {
                  //x509_public_key(pkey);
                  dissect_ocsp_x509_public_key(pkey,info);
       }


       EVP_PKEY_free(pkey);
   }


    return 0;
}

static int dissect_ocsp_x509_signature_print(const X509_ALGOR *sigalg,const ASN1_STRING *sig,ocsp_info_t    *info)
{
    int sig_nid;

    i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_X509_ALGORITHMID], OCSP_STRING_LENGTH, sigalg->algorithm);

    #if 0
    sig_nid = OBJ_obj2nid(sigalg->algorithm);
    if (sig_nid != NID_undef) {
        int pkey_nid, dig_nid;
        const EVP_PKEY_ASN1_METHOD *ameth;
        if (OBJ_find_sigid_algs(sig_nid, &dig_nid, &pkey_nid)) {
            ameth = EVP_PKEY_asn1_find(NULL, pkey_nid);
            if (ameth && ameth->sig_print)
                return ameth->sig_print(bp, sigalg, sig, 9, 0);
        }
    }
    #endif

    /*
    if (sig){
        int len=ASN1_STRING_length(&sig->issuerNameHash);
        if(len > OCSP_STRING_LENGTH){
            memcpy(info->ocsp_array[unkonwn],
                   ASN1_STRING_get0_data(&sig->issuerNameHash),
                   OCSP_STRING_LENGTH);
        }else{
            memcpy(info->ocsp_array[unkonwn],
                   ASN1_STRING_get0_data(&sig->issuerNameHash),
                   len);
        }
    }
    */

    return 1;
}

static int dissect_ocsp_x509_print_ex( X509 *x, unsigned long nmflags,unsigned long cflag, ocsp_info_t    *info)
{
    long l;
    int i;
    int len;
    char buf[OCSP_STRING_LENGTH]={0};

    /*
    if (!(cflag & X509_FLAG_NO_HEADER)) {
        printf("Certificate:\n");
        printf("%4sData\n"," ");
    }*/

    if (!(cflag & X509_FLAG_NO_VERSION)) {
        l = X509_get_version(x);
        if (l >= 0 && l <= 2) {
            snprintf(info->ocsp_array[EM_OCSP_X509_VERSION], OCSP_STRING_LENGTH,"%ld (0x%lx)",  l + 1, (unsigned long)l);
        } else {
            snprintf(info->ocsp_array[EM_OCSP_X509_VERSION], OCSP_STRING_LENGTH,"Unknown (%ld)", (unsigned long)l);
        }
    }

    dissect_ocsp_x509_serical_number(x, nmflags, cflag, info);

    dissect_ocsp_x509_cert(x, nmflags,cflag, info);

    dissect_ocsp_x509_subject(x, nmflags,cflag, info);

    if (!(cflag & X509_FLAG_NO_IDS)) {
        const ASN1_BIT_STRING *iuid, *suid;
        X509_get0_uids(x, &iuid, &suid);
        if (iuid != NULL) {
            /*[field name] IssuertUniqueID*/
            len=ASN1_STRING_length(iuid);
            if(len>=OCSP_STRING_LENGTH){len=OCSP_STRING_LENGTH;}
            memcpy(info->ocsp_array[EM_OCSP_X509_ISSUERUNIQUEID], ASN1_STRING_get0_data(iuid), len );
        }


        if (suid != NULL) {
            /*[field name] SubjectUniqueID*/
            len=ASN1_STRING_length(suid);
            if(len>=OCSP_STRING_LENGTH){len=OCSP_STRING_LENGTH;}
            memcpy(info->ocsp_array[EM_OCSP_X509_SUBJECTUNIQUEID], ASN1_STRING_get0_data(suid),len);
        }
    }


    //if (!(cflag & X509_FLAG_NO_EXTENSIONS))
    //      X509V3_extensions_print(bp, "X509v3 extensions",
    //                              X509_get0_extensions(x), cflag, 8);


    if (!(cflag & X509_FLAG_NO_SIGDUMP)) {
        const X509_ALGOR *sig_alg;
        const ASN1_BIT_STRING *sig;
        X509_get0_signature(&sig, &sig_alg, x);
        dissect_ocsp_x509_signature_print(sig_alg, sig, info);

    }
#if 0
    if (!(cflag & X509_FLAG_NO_AUX)) {
        if (!X509_aux_print(bp, x, 0))
            goto err;
    }
#endif


    return 0;
}


static int dissect_x509V3_extensions_prn(const STACK_OF(X509_EXTENSION) *exts, ocsp_info_t   *info)
{
    int i, j;
    char buf[OCSP_STRING_LENGTH]={0};

    if (sk_X509_EXTENSION_num(exts) <= 0)
        return 1;

    //if (title) {
        //printf("%*s%s:\n", indent, "", title);
    //    indent += 4;
    //}

    for (i = 0; i < sk_X509_EXTENSION_num(exts); i++) {
        ASN1_OBJECT *obj;
        X509_EXTENSION *ex;
        ex = sk_X509_EXTENSION_value(exts, i);
        //printf("%*s", indent, "");

        obj = X509_EXTENSION_get_object(ex);

        i2t_ASN1_OBJECT(buf, sizeof(buf), obj);
        //printf("%*s%s",indent,"", buf);

        j = X509_EXTENSION_get_critical(ex);
        //printf( ": %s\n", j ? "critical" : "");

    #if 0
        if (!X509V3_EXT_print(bp, ex, flag, indent + 4)) {
            BIO_printf(bp, "%*s", indent + 4, "");
            ASN1_STRING_print(bp, X509_EXTENSION_get_data(ex));
        }
    #endif

    }
    return 1;


}



static int dissect_ocsp_certid(OCSP_CERTID *a, int i, ocsp_info_t   *info, int flag)  // flag = 1 request,  flag = 0 response
{
    if(i>1){return 0;}
    switch(i){
    case 0:
        if(flag){
            /* hashAlgorithm_element */
            i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_HASHALGORITHM_ELEMENT_0], OCSP_STRING_LENGTH, a->hashAlgorithm.algorithm);
            /* issuerNameHash */
            memcpy(info->ocsp_array[EM_OCSP_ISSUERNAMEHASH_0], ASN1_STRING_get0_data(&a->issuerNameHash), ASN1_STRING_length(&a->issuerNameHash) );
            /* issuerKeyHash */
            memcpy(info->ocsp_array[EM_OCSP_ISSUERKEYHASH_0], ASN1_STRING_get0_data(&a->issuerKeyHash), ASN1_STRING_length(&a->issuerKeyHash) );
            /* serialNumber */
            memcpy(info->ocsp_array[EM_OCSP_SERIALNUMBER_0], ASN1_STRING_get0_data(&a->serialNumber), ASN1_STRING_length(&a->serialNumber) );
        }
        else{
            i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_HASHALGORITHM_ELEMENT_2], OCSP_STRING_LENGTH, a->hashAlgorithm.algorithm);
            memcpy(info->ocsp_array[EM_OCSP_ISSUERNAMEHASH_2], ASN1_STRING_get0_data(&a->issuerNameHash), ASN1_STRING_length(&a->issuerNameHash) );
            memcpy(info->ocsp_array[EM_OCSP_ISSUERKEYHASH_2], ASN1_STRING_get0_data(&a->serialNumber), ASN1_STRING_length(&a->serialNumber) );
            memcpy(info->ocsp_array[EM_OCSP_SERIALNUMBER_2], ASN1_STRING_get0_data(&a->serialNumber), ASN1_STRING_length(&a->serialNumber) );
        }
        break;
    case 1:
        if(flag){
            i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_HASHALGORITHM_ELEMENT_1], OCSP_STRING_LENGTH, a->hashAlgorithm.algorithm);
            memcpy(info->ocsp_array[EM_OCSP_ISSUERNAMEHASH_1], ASN1_STRING_get0_data(&a->issuerNameHash), ASN1_STRING_length(&a->issuerNameHash) );
            memcpy(info->ocsp_array[EM_OCSP_ISSUERKEYHASH_1], ASN1_STRING_get0_data(&a->serialNumber), ASN1_STRING_length(&a->serialNumber) );
            memcpy(info->ocsp_array[EM_OCSP_SERIALNUMBER_1], ASN1_STRING_get0_data(&a->serialNumber), ASN1_STRING_length(&a->serialNumber) );
        }
        else{
            i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_HASHALGORITHM_ELEMENT_3], OCSP_STRING_LENGTH, a->hashAlgorithm.algorithm);
            memcpy(info->ocsp_array[EM_OCSP_ISSUERNAMEHASH_3], ASN1_STRING_get0_data(&a->issuerNameHash), ASN1_STRING_length(&a->issuerNameHash) );
            memcpy(info->ocsp_array[EM_OCSP_ISSUERKEYHASH_3], ASN1_STRING_get0_data(&a->serialNumber), ASN1_STRING_length(&a->serialNumber) );
            memcpy(info->ocsp_array[EM_OCSP_SERIALNUMBER_3], ASN1_STRING_get0_data(&a->serialNumber), ASN1_STRING_length(&a->serialNumber) );
        }
        break;
    default:
        break;

    }

    return 1;
}


static int dissect_ocsp_general_name(GENERAL_NAME *gen, ocsp_info_t  *info)
{
    unsigned char *p;
    int i;

    switch (gen->type) {
    case GEN_OTHERNAME:
        snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"sothername:<unsupported>");
        break;

    case GEN_X400:
        snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"X400Name:<unsupported>");
        break;

    case GEN_EDIPARTY:
        /* Maybe fix this: it is supported now */
        snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"EdiPartyName:<unsupported>");
        break;

    case GEN_EMAIL:
        snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"email:%s", gen->d.ia5->data);
        break;

    case GEN_DNS:
        snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"DNS:%s",gen->d.ia5->data);
        break;

    case GEN_URI:
        snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"URI:%s",gen->d.ia5->data);
        break;

    case GEN_DIRNAME:
        snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"DirName:");
        //X509_NAME_print_ex(out, gen->d.dirn, 0, XN_FLAG_ONELINE);
        break;

    case GEN_IPADD:
        p = gen->d.ip->data;
        if (gen->d.ip->length == 4)
            snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"IP Address:%d.%d.%d.%d", p[0], p[1], p[2], p[3]);
        else if (gen->d.ip->length == 16) {
            snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"IP Address");
            for (i = 0; i < 8; i++) {
                printf(":%X", p[0] << 8 | p[1]);
                p += 2;
            }
        } else {
            snprintf(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH,"IP Address:<invalid>");
            break;
        }
        break;

    case GEN_RID:
        i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_REQUESTORNAME],OCSP_STRING_LENGTH, gen->d.rid);
        break;
    }
    return 1;

}

//extern int asnl_time_to_tm(struct tm*, void*);
static int dissect_ocsp_ASN1_TIME_print(const ASN1_TIME *tm, ocsp_info_t   *info)
{
    static const char _asn1_mon[12][4] = {
    "Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    };
    char *v;
        int gmt = 0, l;
    struct tm stm;

    if (!ASN1_TIME_to_tm(tm, &stm)) {
        /* asn1_time_to_tm will check the time type */
        return -1;
    }

    l = tm->length;
    v = (char *)tm->data;
    if (v[l - 1] == 'Z')
        gmt = 1;

    if (tm->type == V_ASN1_GENERALIZEDTIME) {
        char *f = NULL;
        int f_len = 0;

        /*
         * Try to parse fractional seconds. '14' is the place of
         * 'fraction point' in a GeneralizedTime string.
         */
        if (tm->length > 15 && v[14] == '.') {
            f = &v[14];
            f_len = 1;
            while (14 + f_len < l && f[f_len]>'0' && f[f_len]<'9')
                ++f_len;
        }
        snprintf(info->ocsp_array[EM_OCSP_PRODUCEDAT],OCSP_STRING_LENGTH,
                 "%s %2d %02d:%02d:%02d%.*s %d%s",
                 _asn1_mon[stm.tm_mon], stm.tm_mday, stm.tm_hour,
                 stm.tm_min, stm.tm_sec, f_len, f, stm.tm_year + 1900,
                 (gmt ? " GMT" : ""));
    } else {
        snprintf(info->ocsp_array[EM_OCSP_PRODUCEDAT],OCSP_STRING_LENGTH,
                 "%s %2d %02d:%02d:%02d %d%s",
                 _asn1_mon[stm.tm_mon], stm.tm_mday, stm.tm_hour,
                 stm.tm_min, stm.tm_sec, stm.tm_year + 1900,
                 (gmt ? " GMT" : ""));
    }

    return 0;
}


static int dissect_ocsp_OCSPRequest(const uint8_t *payload, const uint32_t payload_len, ocsp_info_t   *info)
{

    OCSP_REQUEST     *req = NULL;
    BIO              *derbio = NULL;

    derbio = BIO_new_mem_buf(payload, payload_len);
    if(derbio == NULL){return PKT_DROP;}
    req = d2i_OCSP_REQUEST_bio(derbio, NULL);
    BIO_free(derbio);
    if(req==NULL){return PKT_DROP;}

    int i;
    long l;
    OCSP_CERTID *cid = NULL;
    OCSP_ONEREQ *one = NULL;

    OCSP_REQINFO *inf = &req->tbsRequest;
    OCSP_SIGNATURE *sig = req->optionalSignature;

    /*[field] version*/
    l = ASN1_INTEGER_get(inf->version);
    snprintf(info->ocsp_array[EM_OCSP_VERSION],OCSP_STRING_LENGTH,"%lu",l+1);

    /*[field] requestorName*/
    if (inf->requestorName != NULL) {
        dissect_ocsp_general_name(inf->requestorName, info);
    }

    for (i = 0; i < sk_OCSP_ONEREQ_num(inf->requestList); i++) {
        one = sk_OCSP_ONEREQ_value(inf->requestList, i);
        cid = one->reqCert;
        dissect_ocsp_certid(cid, i, info, 1);
        //dissect_x509V3_extensions_prn(one->singleRequestExtensions, info);

    }
    //dissect_x509V3_extensions_prn(inf->requestExtensions, info);

#if 1
    if(sig){

        dissect_ocsp_x509_signature_print( &sig->signatureAlgorithm, sig->signature, info);

        /*[field name] SignaltureAlgorihmElement*/
        i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_SIGNATUREALGORITHM_ELEMENT],
                        sizeof(info->ocsp_array[EM_OCSP_SIGNATUREALGORITHM_ELEMENT]),
                        sig->signatureAlgorithm.algorithm);

        /*[field name] signature*/
        memcpy(info->ocsp_array[EM_OCSP_SIGNATURE], ASN1_STRING_get0_data(sig->signature), ASN1_STRING_length(sig->signature) );

        /*[field name] certs*/
        #if 1
        for (i = 0; i < sk_X509_num(sig->certs); i++) {
            dissect_ocsp_x509_print_ex( sk_X509_value(sig->certs, i), XN_FLAG_COMPAT, X509_FLAG_COMPAT, info);
        }
        #endif
    }
#endif




    OCSP_REQUEST_free(req);

    return PKT_OK;
}


static int dissect_ocsp_OCSPResponse(const uint8_t *payload, const uint32_t payload_len, ocsp_info_t   *info)
{
    OCSP_RESPONSE    *resp = NULL;
    BIO              *derbio = NULL;
    int              f_data_len=0;


    derbio = BIO_new_mem_buf(payload, payload_len);
    if(derbio == NULL){return PKT_DROP;}

    resp = d2i_OCSP_RESPONSE_bio(derbio, NULL);
    BIO_free(derbio);
    if(resp==NULL){return PKT_DROP;}


    int  i;
    long l;
    OCSP_RESPBYTES   *rb = resp->responseBytes;
    OCSP_BASICRESP   *br = NULL;
    OCSP_RESPDATA    *rd = NULL;
    OCSP_RESPID      *rid = NULL;

    OCSP_CERTID      *cid = NULL;
    OCSP_SINGLERESP  *single = NULL;
    OCSP_CERTSTATUS  *cst = NULL;
    OCSP_REVOKEDINFO *rev = NULL;

    br = OCSP_response_get1_basic(resp);
    if(br == NULL){
       // printf("ocsp response get basic error\n");
        return -1;
    }

    /* [fieldname] ResponseStatus */
    l = ASN1_ENUMERATED_get(resp->responseStatus);
    snprintf(info->ocsp_array[EM_OCSP_RESPONSESTATUS],OCSP_STRING_LENGTH," %s (0x%lx)",OCSP_response_status_str(l), l);

    /* [fieldname] ResponseType */
    i2t_ASN1_OBJECT(info->ocsp_array[EM_OCSP_RESPONSETYPE], sizeof(info->ocsp_array[EM_OCSP_RESPONSETYPE]), rb->responseType);


    /* [fieldname] version */
    rd = &br->tbsResponseData;
    l = ASN1_INTEGER_get(rd->version);
    snprintf(info->ocsp_array[EM_OCSP_VERSION],OCSP_STRING_LENGTH,"%lu",l+1);

    rid = &rd->responderId;
    switch(rid->type){
    case V_OCSP_RESPID_NAME:
        /* [fieldname] ByName */
        X509_NAME_oneline(rid->value.byName, info->ocsp_array[EM_OCSP_BYNAME], sizeof(info->ocsp_array[EM_OCSP_BYNAME]));
        break;
    case V_OCSP_RESPID_KEY:
        /* [fieldname] ByKey */
        memcpy(info->ocsp_array[EM_OCSP_BYKEY], ASN1_STRING_get0_data(rid->value.byKey), DPI_MIN(OCSP_STRING_LENGTH - 1, ASN1_STRING_length(rid->value.byKey)) );

        break;
    default:
        break;
    }

    /* [fieldname] ProducedAt*/
    if(rd->producedAt->type==V_ASN1_GENERALIZEDTIME){
        dissect_ocsp_ASN1_TIME_print(rd->producedAt, info);
    }


    for (i = 0; i < sk_OCSP_SINGLERESP_num(rd->responses); i++) {
        if (!sk_OCSP_SINGLERESP_value(rd->responses, i))
            continue;
        single = sk_OCSP_SINGLERESP_value(rd->responses, i);
        cid = single->certId;

           dissect_ocsp_certid(cid,i, info, 0);

        /*[fieldname] certStatus*/
        cst = single->certStatus;
        snprintf(info->ocsp_array[EM_OCSP_CERTSTATUS], OCSP_STRING_LENGTH, "%s", OCSP_cert_status_str(cst->type));
        if (cst->type == V_OCSP_CERTSTATUS_REVOKED) {
            rev = cst->value.revoked;
            /* [fieldname] RevocationTime*/
           //EM_OCSP_REVOCATIONTIME
            dissect_ocsp_ASN1_TIME_print(rev->revocationTime,info);

            /* [fieldname] RevocationReason*/
            //EM_OCSP_REVOCATIONREASON
            if (rev->revocationReason) {
                l = ASN1_ENUMERATED_get(rev->revocationReason);
                snprintf(info->ocsp_array[EM_OCSP_REVOCATIONREASON], OCSP_STRING_LENGTH," %s (0x%lx)",OCSP_crl_reason_str(l), l);
            }
        }

          /* [fieldname] ThisUpdate*/
        dissect_ocsp_ASN1_TIME_print(single->thisUpdate, info);

        /* [fieldname] NextUpdate*/
        if (single->nextUpdate) {
            dissect_ocsp_ASN1_TIME_print( single->nextUpdate, info);
        }

        //dissect_x509V3_extensions_prn(single->singleExtensions, info);
    }

    //dissect_x509V3_extensions_prn(rd->responseExtensions, info);

    /*[field] x509_cert*/
    for (i = 0; i < sk_X509_num(br->certs); i++) {
        dissect_ocsp_x509_print_ex( sk_X509_value(br->certs, i), XN_FLAG_COMPAT, X509_FLAG_COMPAT, info);
    }


    //if(br!=NULL)
    //    OCSP_BASICRESP_free(br);
    if(resp!=NULL)
        OCSP_RESPONSE_free(resp);



    return PKT_OK;
}

static void distribute_issuer(ocsp_info_t *info)
{
	int len = strlen(info->ocsp_array[EM_OCSP_X509_ISSUER]);
	if(!len)
		return;
	char *begin, *end, *realend;
	realend = info->ocsp_array[EM_OCSP_X509_ISSUER];
	while((begin = realend)){
		realend = strchr(begin+1, '/');
		if(realend)
			end = realend;
		else
			end = info->ocsp_array[EM_OCSP_X509_ISSUER] + len;

		if(!strncmp(begin, "/O=", 3))
			memcpy(info->ocsp_array[EM_OCSP_X509_ORG_NAME], begin+3, end - begin - 3);
		else if(!strncmp(begin, "/C=", 3))
			memcpy(info->ocsp_array[EM_OCSP_X509_COUNTRY_NAME], begin+3, end - begin - 3);
		else if(!strncmp(begin, "/L=", 3))
			memcpy(info->ocsp_array[EM_OCSP_X509_LOCAL_NAME], begin+3, end - begin - 3);
		else if(!strncmp(begin, "/OU=", 4))
			memcpy(info->ocsp_array[EM_OCSP_X509_ORG_UNIT_NAME], begin+4, end - begin - 4);
		else if(!strncmp(begin, "/ST=", 4))
			memcpy(info->ocsp_array[EM_OCSP_X509_STATE_NAME], begin+4, end - begin - 4);


	}
	return;

}

static void write_oscp_log(struct flow_info *flow, int direction, ocsp_info_t *info)
{
    int idx = 0;
    int i,index;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    init_log_ptr_data(log_ptr, flow,PROTOCOL_OCSP);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ocsp");

	if(info->ocsp_array[EM_OCSP_X509_ISSUER][0]){
		distribute_issuer(info);
	}
    for(i=0;i<EM_OCSP_MAX;i++){
        switch(ocsp_field_array[i].index){
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ocsp_field_array[i].type,
                              (const uint8_t*)info->ocsp_array[i],strlen((const char*)info->ocsp_array[i]));
        }
    }

    log_ptr->proto_id = PROTOCOL_OCSP;
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_OCSP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

}


int dissect_ocsp_request(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len)
{
    if(g_config.protocol_switch[PROTOCOL_OCSP] == 0)
       return PKT_OK;

    if(NULL==payload || 0==payload_len){
        return PKT_DROP;
    }
    int http_len=0;
    const uint8_t *p=(const uint8_t *)strstr((const char *)payload,"\r\n\r\n");
    if(p==NULL){
        return PKT_DROP;
    }
    http_len=p-payload+4;
    int payload_new_len=payload_len-http_len;
    if(http_len<0 || payload_new_len<0){return PKT_DROP;}

    uint32_t      offset=0;
    int           direction=0;

    ocsp_info_t   info;
    memset(&info, 0, sizeof(ocsp_info_t));

    dissect_ocsp_OCSPRequest(payload+http_len, payload_len-http_len,&info);



    write_oscp_log(flow, direction, &info);

    return PKT_OK;
}


int dissect_ocsp_response(struct flow_info *flow,  const uint8_t *payload, const uint32_t payload_len)
{
    if(g_config.protocol_switch[PROTOCOL_OCSP] == 0)
       return PKT_OK;

    if(NULL==payload || 0==payload_len){
        return PKT_DROP;
    }
    int http_len=0;
    const uint8_t *p=(const uint8_t *)strstr((const char *)payload,"\r\n\r\n");
    if(p==NULL){
        return PKT_DROP;
    }
    http_len=p-payload+4;
    int payload_new_len=payload_len-http_len;
    if(http_len<0 || payload_new_len<0){return PKT_DROP;}


    uint32_t      offset=0;
    int           direction=1;

    ocsp_info_t   info;
    memset(&info, 0, sizeof(ocsp_info_t));

    dissect_ocsp_OCSPResponse(payload+http_len, payload_len-http_len,  &info);

    write_oscp_log(flow, direction, &info);

    return PKT_OK;
}


static void init_ocsp_dissector(void)
{
    dpi_register_proto_schema(ocsp_field_array,EM_OCSP_MAX,"ocsp");

    map_fields_info_register(ocsp_field_array,PROTOCOL_OCSP, EM_OCSP_MAX, "ocsp");
    return;
}

static __attribute((constructor)) void     before_init_ocsp(void){
    register_tbl_array(TBL_LOG_OCSP, 0, "ocsp", init_ocsp_dissector);
}


