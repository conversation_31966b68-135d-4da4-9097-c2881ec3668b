/*
 * dpi_arkime_pcap.h - Arkime兼容的PCAP文件管理模块头文件
 */

#ifndef DPI_ARKIME_PCAP_H
#define DPI_ARKIME_PCAP_H

#include <stdint.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif

// 文件信息结构体前向声明
typedef struct arkime_file_info arkime_file_info_t;

// iniparser字典类型前向声明
#ifndef INIPARSER_H
typedef struct _dictionary_ dictionary;
#endif

/**
 * 初始化配置参数
 * 从配置文件读取相关参数，创建必要的目录
 * @param ini 配置文件字典指针，如果为NULL则使用默认配置
 */
void dpi_arkime_pcap_init_config(dictionary *ini);

/**
 * 申请一个全局文件序号（线程安全）
 * @return 全局唯一的文件序号
 */
uint64_t dpi_arkime_file_num_creat(void);

/**
 * 存储数据包到PCAP文件
 * 这是主要的接口函数，会自动处理文件轮换
 *
 * @param packet_data 包数据指针
 * @param packet_len 包数据长度
 * @param timestamp_usec 时间戳（微秒）
 * @param file_num 输出参数，返回文件序号
 * @param file_pos 输出参数，返回包在文件中的偏移量
 * @return 0成功，-1失败
 */
int dpi_arkime_store_packet(const uint8_t *packet_data,
                           uint32_t packet_len,
                           uint64_t timestamp_usec,
                           uint64_t *file_num,
                           uint64_t *file_pos);

/**
 * 获取当前文件信息的JSON格式字符串
 * @return JSON格式的文件信息字符串，如果没有当前文件返回NULL
 */
char* dpi_arkime_get_current_file_info(void);

/**
 * 清理资源
 * 关闭所有打开的文件，释放内存
 */
void dpi_arkime_pcap_cleanup(void);


#ifdef __cplusplus
}
#endif

#endif /* DPI_ARKIME_PCAP_H */
