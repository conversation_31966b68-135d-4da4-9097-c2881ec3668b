/*
 * Arkime Elasticsearch发送模块头文件
 * 支持三种发送接口：
 * 1. arkime_sessions3-{YYMMDD} - 会话数据
 * 2. arkime_files_v30/_doc/localhost-{fileid} - 文件信息
 * 3. arkime_fields - 字段定义
 */

#ifndef DPI_ARKIME_ES_H
#define DPI_ARKIME_ES_H

#include <stdint.h>
#include <arpa/inet.h>

// 前向声明结构体
struct flow_info;
struct ProtoRecord;
typedef struct ProtoRecord precord_t;

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 初始化ES模块
 * @return 0成功，-1失败
 */
int dpi_arkime_es_init(void);

/**
 * 清理ES模块（全局清理）
 */
void dpi_arkime_es_cleanup(void);

/**
 * 清理线程本地CURL句柄
 */
void dpi_arkime_es_cleanup_thread(void);

/**
 * 设置ES配置
 * @param host ES服务器地址
 * @param port ES端口
 * @param username ES用户名（可为NULL）
 * @param password ES密码（可为NULL）
 * @param node_name 节点名称
 * @param timeout 超时时间（秒）
 * @param enabled 是否启用ES发送
 */
void dpi_arkime_es_set_config(const char *host, int port, const char *username, 
                             const char *password, const char *node_name, 
                             int timeout, int enabled);

/**
 * 发送会话数据到 arkime_sessions3-{YYMMDD}
 * @param session_json 会话数据的JSON字符串
 * @return 0成功，-1失败
 */
int dpi_arkime_es_send_session(const char *session_json);

/**
 * 发送文件信息到 arkime_files_v30/_doc/{nodename}-{fileid}
 * @param file_id 文件ID
 * @param file_json 文件信息的JSON字符串
 * @return 0成功，-1失败
 */
int dpi_arkime_es_send_file_info(uint64_t file_id, const char *file_json);

/**
 * 发送字段定义到 arkime_fields
 * @param field_id 字段ID
 * @param field_json 字段定义的JSON字符串
 * @return 0成功，-1失败
 */
int dpi_arkime_es_send_field_definition(const char *field_id, const char *field_json);

/**
 * 发送预构造的bulk数据到ES
 * @param bulk_data 预构造的bulk格式数据
 * @return 0成功，-1失败
 */
int dpi_arkime_es_send_bulk_data(const char *bulk_data);

/**
 * 从配置文件初始化ES模块配置
 * @param ini 配置文件字典
 * @return 0成功，-1失败
 */
int dpi_arkime_es_init_config(void *ini);

#ifdef __cplusplus
}
#endif

#endif /* DPI_ARKIME_ES_H */
