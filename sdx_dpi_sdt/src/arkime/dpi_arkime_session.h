/*打包session的json结构*/

#ifndef DPI_ARKIME_SESSION_H
#define DPI_ARKIME_SESSION_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 前向声明
struct flow_info;
struct ProtoRecord;
typedef struct ProtoRecord precord_t;

/**
 * 从flow创建会话JSON数据
 * @param flow flow信息结构体指针
 * @return JSON格式的会话数据字符串，需要调用者释放内存
 */
char* dpi_arkime_create_session_json_from_flow(struct flow_info *flow);

/**
 * 从record创建会话JSON数据 - 完全基于record，因为flow可能已失效
 * 使用record中保存的arkime文件信息和协议字段信息
 * @param record 协议记录指针（包含flow指针和arkime文件信息）
 * @return JSON格式的会话数据字符串，需要调用者释放内存
 */
char* dpi_arkime_create_session_json_from_record(precord_t *record);

/**
 * 发送会话数据到ES - 完全基于record
 * @param record 协议记录指针（包含所有必要信息）
 * @return 0成功，-1失败
 */
int dpi_arkime_send_session_to_es(precord_t *record);

#ifdef __cplusplus
}
#endif

#endif /* DPI_ARKIME_SESSION_H */
