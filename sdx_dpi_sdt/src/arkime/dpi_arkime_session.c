/*打包session的json结构*/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <time.h>
#include <sys/time.h>
#include <stdint.h>

#include "dpi_arkime_session.h"
#include "dpi_arkime_es.h"
#include "../dpi_log.h"
#include "../dpi_detect.h"
#include "../dpi_tbl_log.h"
#include "../sdtapp_interface.h"
#include "../../include/cJSON.h"

extern struct global_config g_config;

// 从record创建会话JSON数据
char* dpi_arkime_create_session_json_from_record(precord_t *record) {
    // 检查ES是否启用
    if (!g_config.es_config.enabled) {
        return NULL; // ES未启用时返回NULL
    }

    if (!record) {
        DPI_LOG(DPI_LOG_ERROR, "Invalid record pointer");
        return NULL;
    }

    cJSON *session = cJSON_CreateObject();
    if (!session) {
        return NULL;
    }

    // 添加@timestamp字段（当前时间戳，毫秒）
    struct timeval current_time;
    gettimeofday(&current_time, NULL);
    uint64_t timestamp_ms = ((uint64_t)current_time.tv_sec) * 1000 + ((uint64_t)current_time.tv_usec) / 1000;
    cJSON_AddNumberToObject(session, "@timestamp", timestamp_ms);

    // 创建protocols数组
    cJSON *protocols_array = cJSON_CreateArray();
    if (!protocols_array) {
        cJSON_Delete(session);
        return NULL;
    }

    // 创建source和destination对象
    cJSON *source = cJSON_CreateObject();
    cJSON *destination = cJSON_CreateObject();
    cJSON *network = cJSON_CreateObject();
    if (!source || !destination || !network) {
        cJSON_Delete(session);
        cJSON_Delete(protocols_array);
        if (source) cJSON_Delete(source);
        if (destination) cJSON_Delete(destination);
        if (network) cJSON_Delete(network);
        return NULL;
    }

    // 初始化统计变量
    uint64_t src_bytes = 0, dst_bytes = 0, src_packets = 0, dst_packets = 0;
    uint64_t first_packet = 0, last_packet = 0, session_length = 0;
    int ip_protocol = 0;

    // 遍历所有层
    for (player_t *layer = precord_layer_get_first(record); layer != NULL;
         layer = precord_layer_get_next(record, layer)) {

        const char *layer_name = precord_layer_get_layer_name(layer);
        precord_layer_move_cursor(record, layer_name);

        // 添加协议到protocols数组
        cJSON *protocol_name = cJSON_CreateString(layer_name);
        if (protocol_name) {
            cJSON_AddItemToArray(protocols_array, protocol_name);
        }

        // 遍历当前层的所有字段
        for (pfield_t *field = precord_field_get_first(record);
            field != NULL;
            field = precord_field_get_next(record, field)) {

            pfield_desc_t *fdesc = precord_field_get_fdesc(field);
            ya_fvalue_t *fvalue = precord_field_get_fvalue(field);
            const char *field_name = pfdesc_get_name(fdesc);

            if (fvalue == NULL) {
                if (g_config.sdx_out_all_field) {
                    // 如果配置要求输出所有字段，添加null值
                    cJSON_AddNullToObject(session, field_name);
                }
                continue;
            }

            char *value_str = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
            if (!value_str) {
                continue;
            }

            // 通用字段处理逻辑 - 所有字段统一处理，映射由Lua适配层完成
            // 检查是否已存在该协议的对象
            cJSON *proto_obj = cJSON_GetObjectItem(session, layer_name);
            if (!proto_obj) {
                proto_obj = cJSON_CreateObject();
                if (proto_obj) {
                    cJSON_AddItemToObject(session, layer_name, proto_obj);
                }
            }

            if (proto_obj) {
                // 智能类型转换：尝试解析为数字，失败则作为字符串
                char *endptr;
                long long num_val = strtoll(value_str, &endptr, 10);
                if (*endptr == '\0') {
                    // 成功解析为整数
                    cJSON_AddNumberToObject(proto_obj, field_name, num_val);
                } else {
                    // 作为字符串处理
                    cJSON_AddStringToObject(proto_obj, field_name, value_str);
                }

                // 如果是general层，还需要收集一些统计信息用于构造必须字段
                if (strcmp(layer_name, "general") == 0) {
                    // 收集统计信息，用于后续构造source/destination/network对象
                    if (strcmp(field_name, "upBytes") == 0) {
                        src_bytes = strtoull(value_str, NULL, 10);
                    } else if (strcmp(field_name, "downBytes") == 0) {
                        dst_bytes = strtoull(value_str, NULL, 10);
                    } else if (strcmp(field_name, "upPackets") == 0) {
                        src_packets = strtoull(value_str, NULL, 10);
                    } else if (strcmp(field_name, "downPackets") == 0) {
                        dst_packets = strtoull(value_str, NULL, 10);
                    } else if (strcmp(field_name, "firstPacket") == 0) {
                        first_packet = strtoull(value_str, NULL, 10);
                    } else if (strcmp(field_name, "lastPacket") == 0) {
                        last_packet = strtoull(value_str, NULL, 10);
                    } else if (strcmp(field_name, "length") == 0) {
                        session_length = strtoull(value_str, NULL, 10);
                    } else if (strcmp(field_name, "proto") == 0) {
                        ip_protocol = atoi(value_str);
                    }
                }
            }

            free(value_str);
        }
    }

    // 添加node字段
    cJSON_AddStringToObject(session, "node", g_config.es_config.node_name);

    // 从general对象中提取信息构造Arkime必须字段
    cJSON *general_obj = cJSON_GetObjectItem(session, "general");
    if (general_obj) {
        // 构造source对象
        cJSON *src_addr = cJSON_GetObjectItem(general_obj, "srcAddr");
        cJSON *src_port = cJSON_GetObjectItem(general_obj, "srcPort");
        cJSON *src_mac = cJSON_GetObjectItem(general_obj, "srcMac");

        if (src_addr && cJSON_IsString(src_addr)) {
            cJSON_AddStringToObject(source, "ip", cJSON_GetStringValue(src_addr));
        }
        if (src_port && cJSON_IsNumber(src_port)) {
            cJSON_AddNumberToObject(source, "port", cJSON_GetNumberValue(src_port));
        }
        if (src_bytes > 0) {
            cJSON_AddNumberToObject(source, "bytes", src_bytes);
        }
        if (src_packets > 0) {
            cJSON_AddNumberToObject(source, "packets", src_packets);
        }
        if (src_mac && cJSON_IsString(src_mac)) {
            cJSON *mac_array = cJSON_CreateArray();
            if (mac_array) {
                cJSON_AddItemToArray(mac_array, cJSON_CreateString(cJSON_GetStringValue(src_mac)));
                cJSON_AddItemToObject(source, "mac", mac_array);
            }
        }

        // 构造destination对象
        cJSON *dst_addr = cJSON_GetObjectItem(general_obj, "dstAddr");
        cJSON *dst_port = cJSON_GetObjectItem(general_obj, "dstPort");
        cJSON *dst_mac = cJSON_GetObjectItem(general_obj, "dstMac");

        if (dst_addr && cJSON_IsString(dst_addr)) {
            cJSON_AddStringToObject(destination, "ip", cJSON_GetStringValue(dst_addr));
        }
        if (dst_port && cJSON_IsNumber(dst_port)) {
            cJSON_AddNumberToObject(destination, "port", cJSON_GetNumberValue(dst_port));
        }
        if (dst_bytes > 0) {
            cJSON_AddNumberToObject(destination, "bytes", dst_bytes);
        }
        if (dst_packets > 0) {
            cJSON_AddNumberToObject(destination, "packets", dst_packets);
        }
        if (dst_mac && cJSON_IsString(dst_mac)) {
            cJSON *mac_array = cJSON_CreateArray();
            if (mac_array) {
                cJSON_AddItemToArray(mac_array, cJSON_CreateString(cJSON_GetStringValue(dst_mac)));
                cJSON_AddItemToObject(destination, "mac", mac_array);
            }
        }

        // 添加session级别的必须字段
        if (first_packet > 0) {
            cJSON_AddNumberToObject(session, "firstPacket", first_packet);
        }
        if (last_packet > 0) {
            cJSON_AddNumberToObject(session, "lastPacket", last_packet);
        }
        if (session_length > 0) {
            cJSON_AddNumberToObject(session, "length", session_length);
        }
        if (ip_protocol > 0) {
            cJSON_AddNumberToObject(session, "ipProtocol", ip_protocol);
        }
    }

    // 计算并添加network统计
    uint64_t total_bytes = src_bytes + dst_bytes;
    uint64_t total_packets = src_packets + dst_packets;
    cJSON_AddNumberToObject(network, "bytes", total_bytes);
    cJSON_AddNumberToObject(network, "packets", total_packets);

    // 添加packetPos和fileId数组（从record的文件信息链表中获取）
    cJSON *packet_pos = cJSON_CreateArray();
    cJSON *file_id = cJSON_CreateArray();
    if (packet_pos && file_id) {
        // 获取record的文件位置数组
        int64_t *pos_array = NULL;
        int array_size = 0;

        // 使用函数获取文件位置数组
        if (arkime_record_get_file_pos_array(record, &pos_array, &array_size) == 0 && pos_array && array_size > 0) {
            // 添加所有文件位置信息到packetPos数组
            for (int i = 0; i < array_size; i++) {
                cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(pos_array[i]));
            }

            // 提取文件ID到fileId数组（负值表示文件ID）
            for (int i = 0; i < array_size; i++) {
                if (pos_array[i] < 0) {  // 负值表示文件ID
                    cJSON_AddItemToArray(file_id, cJSON_CreateNumber(-pos_array[i]));
                }
            }

            free(pos_array);  // 释放分配的内存
        } else {
            // 如果没有文件信息，添加默认值
            cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(-1));
            cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(0));
            cJSON_AddItemToArray(file_id, cJSON_CreateNumber(1));
        }
    }

    // 将所有对象添加到session
    cJSON_AddItemToObject(session, "source", source);
    cJSON_AddItemToObject(session, "destination", destination);
    cJSON_AddItemToObject(session, "network", network);
    cJSON_AddItemToObject(session, "protocols", protocols_array);
    if (packet_pos) cJSON_AddItemToObject(session, "packetPos", packet_pos);
    if (file_id) cJSON_AddItemToObject(session, "fileId", file_id);

    char *json_string = cJSON_PrintUnformatted(session);
    cJSON_Delete(session);

    DPI_LOG(DPI_LOG_DEBUG, "Created session JSON from record with file info and general fields");

    return json_string;
}

// 发送会话数据到ES - 完全基于record
int dpi_arkime_send_session_to_es(precord_t *record) {
    if (!record || !g_config.es_config.enabled) {
        return 0; // ES未启用或无效record时直接返回成功
    }

    char *session_json = dpi_arkime_create_session_json_from_record(record);
    if (!session_json) {
        DPI_LOG(DPI_LOG_ERROR, "Failed to create session JSON from record");
        return -1;
    }

    int result = dpi_arkime_es_send_session(session_json);
    if (result == 0) {
        DPI_LOG(DPI_LOG_DEBUG, "Sent session data to ES");
    } else {
        DPI_LOG(DPI_LOG_WARNING, "Failed to send session data to ES");
    }

    free(session_json);
    return result;
}