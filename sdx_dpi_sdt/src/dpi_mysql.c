/****************************************************************************************
 * 文 件 名 : dpi_mysql.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/09/10
编码: wangy            2018/09/10
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>

#include "dpi_pint.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"


extern struct rte_mempool *tbl_log_mempool;
extern dpi_field_table dbbasic_field_array[];

/* status bitfield */
#define MYSQL_STAT_IT 0x0001
#define MYSQL_STAT_AC 0x0002
#define MYSQL_STAT_MR 0x0004
#define MYSQL_STAT_MU 0x0008
#define MYSQL_STAT_BI 0x0010
#define MYSQL_STAT_NI 0x0020
#define MYSQL_STAT_CR 0x0040
#define MYSQL_STAT_LR 0x0080
#define MYSQL_STAT_DR 0x0100
#define MYSQL_STAT_BS 0x0200
#define MYSQL_STAT_SESSION_STATE_CHANGED 0x0400
#define MYSQL_STAT_QUERY_WAS_SLOW 0x0800
#define MYSQL_STAT_PS_OUT_PARAMS 0x1000

/* extended capabilities: 4.1+ client only
 *
 * These are libmysqlclient flags and NOT present
 * in the protocol:
 * CLIENT_SSL_VERIFY_SERVER_CERT (1UL << 30)
 * CLIENT_REMEMBER_OPTIONS (1UL << 31)
 */
#define MYSQL_CAPS_MS 0x0001 /* CLIENT_MULTI_STATMENTS */
#define MYSQL_CAPS_MR 0x0002 /* CLIENT_MULTI_RESULTS */
#define MYSQL_CAPS_PM 0x0004 /* CLIENT_PS_MULTI_RESULTS */
#define MYSQL_CAPS_PA 0x0008 /* CLIENT_PLUGIN_AUTH */
#define MYSQL_CAPS_CA 0x0010 /* CLIENT_CONNECT_ATTRS */
#define MYSQL_CAPS_AL 0x0020 /* CLIENT_PLUGIN_AUTH_LENENC_CLIENT_DATA */
#define MYSQL_CAPS_EP 0x0040 /* CLIENT_CAN_HANDLE_EXPIRED_PASSWORDS */
#define MYSQL_CAPS_ST 0x0080 /* CLIENT_SESSION_TRACK */
#define MYSQL_CAPS_DE 0x0100 /* CLIENT_DEPRECATE_EOF */
#define MYSQL_CAPS_UNUSED 0xFE00

/* client/server capabilities
 * Source: http://dev.mysql.com/doc/internals/en/capability-flags.html
 * Source: mysql_com.h
 */
#define MYSQL_CAPS_LP 0x0001 /* CLIENT_LONG_PASSWORD */
#define MYSQL_CAPS_FR 0x0002 /* CLIENT_FOUND_ROWS */
#define MYSQL_CAPS_LF 0x0004 /* CLIENT_LONG_FLAG */
#define MYSQL_CAPS_CD 0x0008 /* CLIENT_CONNECT_WITH_DB */
#define MYSQL_CAPS_NS 0x0010 /* CLIENT_NO_SCHEMA */
#define MYSQL_CAPS_CP 0x0020 /* CLIENT_COMPRESS */
#define MYSQL_CAPS_OB 0x0040 /* CLIENT_ODBC */
#define MYSQL_CAPS_LI 0x0080 /* CLIENT_LOCAL_FILES */
#define MYSQL_CAPS_IS 0x0100 /* CLIENT_IGNORE_SPACE */
#define MYSQL_CAPS_CU 0x0200 /* CLIENT_PROTOCOL_41 */
#define MYSQL_CAPS_IA 0x0400 /* CLIENT_INTERACTIVE */
#define MYSQL_CAPS_SL 0x0800 /* CLIENT_SSL */
#define MYSQL_CAPS_II 0x1000 /* CLIENT_IGNORE_SPACE */
#define MYSQL_CAPS_TA 0x2000 /* CLIENT_TRANSACTIONS */
#define MYSQL_CAPS_RS 0x4000 /* CLIENT_RESERVED */
#define MYSQL_CAPS_SC 0x8000 /* CLIENT_SECURE_CONNECTION */

/* MySQL command codes */
#define MYSQL_SLEEP               0  /* not from client */
#define MYSQL_QUIT                1
#define MYSQL_INIT_DB             2
#define MYSQL_QUERY               3
#define MYSQL_FIELD_LIST          4
#define MYSQL_CREATE_DB           5
#define MYSQL_DROP_DB             6
#define MYSQL_REFRESH             7
#define MYSQL_SHUTDOWN            8
#define MYSQL_STATISTICS          9
#define MYSQL_PROCESS_INFO        10
#define MYSQL_CONNECT             11 /* not from client */
#define MYSQL_PROCESS_KILL        12
#define MYSQL_DEBUG               13
#define MYSQL_PING                14
#define MYSQL_TIME                15 /* not from client */
#define MYSQL_DELAY_INSERT        16 /* not from client */
#define MYSQL_CHANGE_USER         17
#define MYSQL_BINLOG_DUMP         18 /* replication */
#define MYSQL_TABLE_DUMP          19 /* replication */
#define MYSQL_CONNECT_OUT         20 /* replication */
#define MYSQL_REGISTER_SLAVE      21 /* replication */
#define MYSQL_STMT_PREPARE        22
#define MYSQL_STMT_EXECUTE        23
#define MYSQL_STMT_SEND_LONG_DATA 24
#define MYSQL_STMT_CLOSE          25
#define MYSQL_STMT_RESET          26
#define MYSQL_SET_OPTION          27
#define MYSQL_STMT_FETCH          28

/* Compression states, internal to the dissector */
#define MYSQL_COMPRESS_NONE   0
#define MYSQL_COMPRESS_INIT   1
#define MYSQL_COMPRESS_ACTIVE 2

/* collation codes may change over time, recreate with the following SQL

SELECT CONCAT('  {', ID, ',"', CHARACTER_SET_NAME, ' COLLATE ', COLLATION_NAME, '"},')
FROM INFORMATION_SCHEMA.COLLATIONS
ORDER BY ID
INTO OUTFILE '/tmp/mysql-collations';

*/

enum mysql_index_em{
    EM_MYSQL_MESSAGETYPE,
    EM_MYSQL_PROTOCOLVERSION,
    EM_MYSQL_SERVERVERSION,
    EM_MYSQL_SERVERTHREADID,
    EM_MYSQL_SERVERCHALLENGE,
    EM_MYSQL_EXTSERVERCHALLENGE,
    EM_MYSQL_SERVERCAPABILITIES,
    EM_MYSQL_SERVERSTATUS,
    EM_MYSQL_CHARSET,
    EM_MYSQL_CLIENTCAPABILITIES,
    EM_MYSQL_EXTCLIENTCAPABILITIES,
    EM_MYSQL_USERNAME,
    EM_MYSQL_CLIENTCHALLENGE,
    EM_MYSQL_DBNAME,
    EM_MYSQL_TABLENAME,
    EM_MYSQL_SLAVESERVERID,
    EM_MYSQL_MAINSERVERID,
    EM_MYSQL_MAINSERVERIP,
    EM_MYSQL_MAINSERVERUSERNAME,
    EM_MYSQL_MAINSERVERPASS,
    EM_MYSQL_MAINSERVERPORT,
    EM_MYSQL_SAFELEVEL,
    EM_MYSQL_REQUESTCOMMAND,
    EM_MYSQL_REQUESTARGS,
    EM_MYSQL_LOGIN_STATUS,
    EM_MYSQL_MAX
};

static dpi_field_table  mysql_field_array[] = {
    DPI_FIELD_D(EM_MYSQL_MESSAGETYPE,                   YA_FT_STRING,     "MessageType"),
    DPI_FIELD_D(EM_MYSQL_PROTOCOLVERSION,               YA_FT_UINT8,      "ProtocolVersion"),
    DPI_FIELD_D(EM_MYSQL_SERVERVERSION,                 YA_FT_STRING,     "ServerVersion"),
    DPI_FIELD_D(EM_MYSQL_SERVERTHREADID,                YA_FT_UINT32,     "ServerThreadid"),
    DPI_FIELD_D(EM_MYSQL_SERVERCHALLENGE,               YA_FT_STRING,     "ServerChallenge"),
    DPI_FIELD_D(EM_MYSQL_EXTSERVERCHALLENGE,            YA_FT_STRING,     "ExtServerChallenge"),
    DPI_FIELD_D(EM_MYSQL_SERVERCAPABILITIES,            YA_FT_STRING,     "ServerCapabilities"),
    DPI_FIELD_D(EM_MYSQL_SERVERSTATUS,                  YA_FT_STRING,     "ServerStatus"),
    DPI_FIELD_D(EM_MYSQL_CHARSET,                       YA_FT_UINT32,     "Charset"),
    DPI_FIELD_D(EM_MYSQL_CLIENTCAPABILITIES,            YA_FT_STRING,     "ClientCapabilities"),
    DPI_FIELD_D(EM_MYSQL_EXTCLIENTCAPABILITIES,         YA_FT_STRING,     "ExtClientCapabilities"),
    DPI_FIELD_D(EM_MYSQL_USERNAME,                      YA_FT_STRING,     "UserName"),
    DPI_FIELD_D(EM_MYSQL_CLIENTCHALLENGE,               YA_FT_STRING,     "ClientChallenge"),
    DPI_FIELD_D(EM_MYSQL_DBNAME,                        YA_FT_STRING,     "DbName"),
    DPI_FIELD_D(EM_MYSQL_TABLENAME,                     YA_FT_STRING,     "TableName"),
    DPI_FIELD_D(EM_MYSQL_SLAVESERVERID,                 YA_FT_STRING,     "SlaveServerId"),
    DPI_FIELD_D(EM_MYSQL_MAINSERVERID,                  YA_FT_STRING,     "MainServerId"),
    DPI_FIELD_D(EM_MYSQL_MAINSERVERIP,                  YA_FT_STRING,     "MainServerIp"),
    DPI_FIELD_D(EM_MYSQL_MAINSERVERUSERNAME,            YA_FT_STRING,     "MainServerUserName"),
    DPI_FIELD_D(EM_MYSQL_MAINSERVERPASS,                YA_FT_STRING,     "MainServerPass"),
    DPI_FIELD_D(EM_MYSQL_MAINSERVERPORT,                YA_FT_STRING,     "MainServerPort"),
    DPI_FIELD_D(EM_MYSQL_SAFELEVEL,                     YA_FT_STRING,     "SafeLevel"),
    DPI_FIELD_D(EM_MYSQL_REQUESTCOMMAND,                YA_FT_STRING,     "RequestCommand"),
    DPI_FIELD_D(EM_MYSQL_REQUESTARGS,                   YA_FT_STRING,     "RequestArgs"),
    DPI_FIELD_D(EM_MYSQL_LOGIN_STATUS,                  YA_FT_STRING,     "LoginStatus"),
};




static const struct int_to_string mysql_collation_vals[] = {
    {3,   "dec8 COLLATE dec8_swedish_ci"},
    {4,   "cp850 COLLATE cp850_general_ci"},
    {5,   "latin1 COLLATE latin1_german1_ci"},
    {6,   "hp8 COLLATE hp8_english_ci"},
    {7,   "koi8r COLLATE koi8r_general_ci"},
    {8,   "latin1 COLLATE latin1_swedish_ci"},
    {9,   "latin2 COLLATE latin2_general_ci"},
    {10,  "swe7 COLLATE swe7_swedish_ci"},
    {11,  "ascii COLLATE ascii_general_ci"},
    {14,  "cp1251 COLLATE cp1251_bulgarian_ci"},
    {15,  "latin1 COLLATE latin1_danish_ci"},
    {16,  "hebrew COLLATE hebrew_general_ci"},
    {20,  "latin7 COLLATE latin7_estonian_cs"},
    {21,  "latin2 COLLATE latin2_hungarian_ci"},
    {22,  "koi8u COLLATE koi8u_general_ci"},
    {23,  "cp1251 COLLATE cp1251_ukrainian_ci"},
    {25,  "greek COLLATE greek_general_ci"},
    {26,  "cp1250 COLLATE cp1250_general_ci"},
    {27,  "latin2 COLLATE latin2_croatian_ci"},
    {29,  "cp1257 COLLATE cp1257_lithuanian_ci"},
    {30,  "latin5 COLLATE latin5_turkish_ci"},
    {31,  "latin1 COLLATE latin1_german2_ci"},
    {32,  "armscii8 COLLATE armscii8_general_ci"},
    {33,  "utf8 COLLATE utf8_general_ci"},
    {36,  "cp866 COLLATE cp866_general_ci"},
    {37,  "keybcs2 COLLATE keybcs2_general_ci"},
    {38,  "macce COLLATE macce_general_ci"},
    {39,  "macroman COLLATE macroman_general_ci"},
    {40,  "cp852 COLLATE cp852_general_ci"},
    {41,  "latin7 COLLATE latin7_general_ci"},
    {42,  "latin7 COLLATE latin7_general_cs"},
    {43,  "macce COLLATE macce_bin"},
    {44,  "cp1250 COLLATE cp1250_croatian_ci"},
    {45,  "utf8mb4 COLLATE utf8mb4_general_ci"},
    {46,  "utf8mb4 COLLATE utf8mb4_bin"},
    {47,  "latin1 COLLATE latin1_bin"},
    {48,  "latin1 COLLATE latin1_general_ci"},
    {49,  "latin1 COLLATE latin1_general_cs"},
    {50,  "cp1251 COLLATE cp1251_bin"},
    {51,  "cp1251 COLLATE cp1251_general_ci"},
    {52,  "cp1251 COLLATE cp1251_general_cs"},
    {53,  "macroman COLLATE macroman_bin"},
    {57,  "cp1256 COLLATE cp1256_general_ci"},
    {58,  "cp1257 COLLATE cp1257_bin"},
    {59,  "cp1257 COLLATE cp1257_general_ci"},
    {63,  "binary COLLATE binary"},
    {64,  "armscii8 COLLATE armscii8_bin"},
    {65,  "ascii COLLATE ascii_bin"},
    {66,  "cp1250 COLLATE cp1250_bin"},
    {67,  "cp1256 COLLATE cp1256_bin"},
    {68,  "cp866 COLLATE cp866_bin"},
    {69,  "dec8 COLLATE dec8_bin"},
    {70,  "greek COLLATE greek_bin"},
    {71,  "hebrew COLLATE hebrew_bin"},
    {72,  "hp8 COLLATE hp8_bin"},
    {73,  "keybcs2 COLLATE keybcs2_bin"},
    {74,  "koi8r COLLATE koi8r_bin"},
    {75,  "koi8u COLLATE koi8u_bin"},
    {77,  "latin2 COLLATE latin2_bin"},
    {78,  "latin5 COLLATE latin5_bin"},
    {79,  "latin7 COLLATE latin7_bin"},
    {80,  "cp850 COLLATE cp850_bin"},
    {81,  "cp852 COLLATE cp852_bin"},
    {82,  "swe7 COLLATE swe7_bin"},
    {83,  "utf8 COLLATE utf8_bin"},
    {92,  "geostd8 COLLATE geostd8_general_ci"},
    {93,  "geostd8 COLLATE geostd8_bin"},
    {94,  "latin1 COLLATE latin1_spanish_ci"},
    {99,  "cp1250 COLLATE cp1250_polish_ci"},
    {192, "utf8 COLLATE utf8_unicode_ci"},
    {193, "utf8 COLLATE utf8_icelandic_ci"},
    {194, "utf8 COLLATE utf8_latvian_ci"},
    {195, "utf8 COLLATE utf8_romanian_ci"},
    {196, "utf8 COLLATE utf8_slovenian_ci"},
    {197, "utf8 COLLATE utf8_polish_ci"},
    {198, "utf8 COLLATE utf8_estonian_ci"},
    {199, "utf8 COLLATE utf8_spanish_ci"},
    {200, "utf8 COLLATE utf8_swedish_ci"},
    {201, "utf8 COLLATE utf8_turkish_ci"},
    {202, "utf8 COLLATE utf8_czech_ci"},
    {203, "utf8 COLLATE utf8_danish_ci"},
    {204, "utf8 COLLATE utf8_lithuanian_ci"},
    {205, "utf8 COLLATE utf8_slovak_ci"},
    {206, "utf8 COLLATE utf8_spanish2_ci"},
    {207, "utf8 COLLATE utf8_roman_ci"},
    {208, "utf8 COLLATE utf8_persian_ci"},
    {209, "utf8 COLLATE utf8_esperanto_ci"},
    {210, "utf8 COLLATE utf8_hungarian_ci"},
    {211, "utf8 COLLATE utf8_sinhala_ci"},
    {212, "utf8 COLLATE utf8_german2_ci"},
    {213, "utf8 COLLATE utf8_croatian_ci"},
    {214, "utf8 COLLATE utf8_unicode_520_ci"},
    {215, "utf8 COLLATE utf8_vietnamese_ci"},
    {223, "utf8 COLLATE utf8_general_mysql500_ci"},
    {224, "utf8mb4 COLLATE utf8mb4_unicode_ci"},
    {225, "utf8mb4 COLLATE utf8mb4_icelandic_ci"},
    {226, "utf8mb4 COLLATE utf8mb4_latvian_ci"},
    {227, "utf8mb4 COLLATE utf8mb4_romanian_ci"},
    {228, "utf8mb4 COLLATE utf8mb4_slovenian_ci"},
    {229, "utf8mb4 COLLATE utf8mb4_polish_ci"},
    {230, "utf8mb4 COLLATE utf8mb4_estonian_ci"},
    {231, "utf8mb4 COLLATE utf8mb4_spanish_ci"},
    {232, "utf8mb4 COLLATE utf8mb4_swedish_ci"},
    {233, "utf8mb4 COLLATE utf8mb4_turkish_ci"},
    {234, "utf8mb4 COLLATE utf8mb4_czech_ci"},
    {235, "utf8mb4 COLLATE utf8mb4_danish_ci"},
    {236, "utf8mb4 COLLATE utf8mb4_lithuanian_ci"},
    {237, "utf8mb4 COLLATE utf8mb4_slovak_ci"},
    {238, "utf8mb4 COLLATE utf8mb4_spanish2_ci"},
    {239, "utf8mb4 COLLATE utf8mb4_roman_ci"},
    {240, "utf8mb4 COLLATE utf8mb4_persian_ci"},
    {241, "utf8mb4 COLLATE utf8mb4_esperanto_ci"},
    {242, "utf8mb4 COLLATE utf8mb4_hungarian_ci"},
    {243, "utf8mb4 COLLATE utf8mb4_sinhala_ci"},
    {244, "utf8mb4 COLLATE utf8mb4_german2_ci"},
    {245, "utf8mb4 COLLATE utf8mb4_croatian_ci"},
    {246, "utf8mb4 COLLATE utf8mb4_unicode_520_ci"},
    {247, "utf8mb4 COLLATE utf8mb4_vietnamese_ci"},
    {0, NULL}
};

/* decoding table: command */
static const struct int_to_string mysql_command_vals[] = {
    {MYSQL_SLEEP,    "SLEEP"},
    {MYSQL_QUIT,   "Quit"},
    {MYSQL_INIT_DB,  "Use Database"},
    {MYSQL_QUERY,    "Query"},
    {MYSQL_FIELD_LIST, "Show Fields"},
    {MYSQL_CREATE_DB,  "Create Database"},
    {MYSQL_DROP_DB , "Drop Database"},
    {MYSQL_REFRESH , "Refresh"},
    {MYSQL_SHUTDOWN , "Shutdown"},
    {MYSQL_STATISTICS , "Statistics"},
    {MYSQL_PROCESS_INFO , "Process List"},
    {MYSQL_CONNECT , "Connect"},
    {MYSQL_PROCESS_KILL , "Kill Server Thread"},
    {MYSQL_DEBUG , "Dump Debuginfo"},
    {MYSQL_PING , "Ping"},
    {MYSQL_TIME , "Time"},
    {MYSQL_DELAY_INSERT , "Insert Delayed"},
    {MYSQL_CHANGE_USER , "Change User"},
    {MYSQL_BINLOG_DUMP , "Send Binlog"},
    {MYSQL_TABLE_DUMP, "Send Table"},
    {MYSQL_CONNECT_OUT, "Slave Connect"},
    {MYSQL_REGISTER_SLAVE, "Register Slave"},
    {MYSQL_STMT_PREPARE, "Prepare Statement"},
    {MYSQL_STMT_EXECUTE, "Execute Statement"},
    {MYSQL_STMT_SEND_LONG_DATA, "Send BLOB"},
    {MYSQL_STMT_CLOSE, "Close Statement"},
    {MYSQL_STMT_RESET, "Reset Statement"},
    {MYSQL_SET_OPTION, "Set Option"},
    {MYSQL_STMT_FETCH, "Fetch Data"},
    {0, NULL}
};

typedef enum mysql_state {
    UNDEFINED,
    LOGIN,
    REQUEST,
    RESPONSE_OK,
    RESPONSE_MESSAGE,
    RESPONSE_TABULAR,
    RESPONSE_SHOW_FIELDS,
    FIELD_PACKET,
    ROW_PACKET,
    RESPONSE_PREPARE,
    PREPARED_PARAMETERS,
    PREPARED_FIELDS,
    AUTH_SWITCH_REQUEST,
    AUTH_SWITCH_RESPONSE
} mysql_state_t;

typedef struct my_stmt_data {
    uint16_t nparam;
    uint8_t* param_flags;
} my_stmt_data_t;
    
struct mysql_session {    
    mysql_state_t state;

    uint16_t srv_caps;
    uint16_t srv_caps_ext;
    uint16_t clnt_caps;
    uint16_t clnt_caps_ext;
    uint16_t stmt_num_params;
    uint16_t stmt_num_fields;

    uint32_t frame_start_ssl;
    uint32_t frame_start_compressed;
    uint8_t  major_version;
    uint8_t  compressed_state;
};

struct mysql_info
{
    const char *msgtype;
    uint8_t protocol;
    char serverversion[64];
    uint32_t serverthreadid;
    uint32_t serverchallenge;
    char ExtServerCapabilities[64];
    char ServerCapabilities[64];
    char ServerStatus[64];
    const char *Charset;
    char ClientCapabilities[64];
    char ExtClientCapabilities[64];
    char UserName[64];
    uint8_t ClientChallenge[64];
    char DbName[64];
    char TableName[64];
    uint32_t SlaveServerId;
    uint32_t MainServerId;
    char MainServerIp[64];
    char MainServerUserName[64];
    char MainServerPass[64];
    uint16_t MainServerPort;
    uint16_t SafeLevel;
    const char * RequestCommand;
    char RequestArgs[1024];
};

static void
mysql_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct mysql_info *info, int *idx, int i)
{
    //int local_idx=*idx;
    switch(i){
    case EM_MYSQL_MESSAGETYPE:
        if (info->msgtype)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->msgtype, strlen(info->msgtype));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_MYSQL_PROTOCOLVERSION:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->protocol);
        break;
    case EM_MYSQL_SERVERVERSION:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->serverversion, strlen(info->serverversion));
        break;
    case EM_MYSQL_SERVERTHREADID:
        write_one_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->serverthreadid);
        break;
    case EM_MYSQL_EXTSERVERCHALLENGE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ExtServerCapabilities, strlen(info->ExtServerCapabilities));
        break;
    case EM_MYSQL_SERVERCAPABILITIES:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerCapabilities, strlen(info->ServerCapabilities));
        break;
    case EM_MYSQL_SERVERSTATUS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ServerStatus, strlen(info->ServerStatus));
        break;
    case EM_MYSQL_CHARSET:
        if (info->Charset)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->Charset, strlen(info->Charset));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_MYSQL_CLIENTCAPABILITIES:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientCapabilities, strlen(info->ClientCapabilities));
        break;
    case EM_MYSQL_EXTCLIENTCAPABILITIES:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ExtClientCapabilities, strlen(info->ExtClientCapabilities));
        break;
    case EM_MYSQL_USERNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->UserName, strlen(info->UserName));
        break;
    case EM_MYSQL_CLIENTCHALLENGE:
        write_multi_num_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->ClientChallenge, info->ClientChallenge[0]);
        break;
    case EM_MYSQL_DBNAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->DbName, strlen(info->DbName));
        break;
    case EM_MYSQL_TABLENAME:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->TableName, strlen(info->TableName));
        break;
    case EM_MYSQL_REQUESTCOMMAND:
        if (info->RequestCommand)
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->RequestCommand, strlen(info->RequestCommand));
        else
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_MYSQL_REQUESTARGS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, info->RequestArgs, strlen(info->RequestArgs));
        break;
    case EM_MYSQL_LOGIN_STATUS:
        if(flow->userdata[0])
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "YES", 3);
        else
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, "NO", 2);
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    #if 0
    if(i<PROTO_MAX_FIELDS_NUM){
        if(*idx>(local_idx+1)){
            log_ptr->field_array[i].fType = mysql_field_array[i].type;
            log_ptr->field_array[i].u.v_pBytes= (byte *)&log_ptr->record[local_idx];
            log_ptr->field_array[i].fLen=*idx-local_idx-1;
        }
    }
    #endif
    
    return;
}


static 
int write_mysql_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    struct mysql_info *info=(struct mysql_info *)field_info;
    if(!info){
        return 0;
    }

    int idx = 0,i=0;
    struct tbl_log *log_ptr;
    
    const char *str= NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN,  match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "mysql");

    for(i=0; i<EM_MYSQL_MAX;i++){
        mysql_field_element(log_ptr,flow, direction, info, &idx, i);
    }
    
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_MYSQL;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}

static void write_dbbasic_log(struct flow_info *flow, int direction, struct mysql_info *info, SdtMatchResult *match_result)
{
    char __str[64] = {0};
    int idx = 0,i;
    struct tbl_log *log_ptr;
    
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }
    init_log_ptr_data(log_ptr, flow,PROTOCOL_DBBASIC);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "dbbasic");

    for(i=0;i<EM_DBBASIC_MAX;i++){
        switch(dbbasic_field_array[i].index){
        case EM_DBBASIC_DBTYPE:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "mysql", 5);
            break;
        case EM_DBBASIC_USERNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->UserName, strlen(info->UserName));    
            break;
        case EM_DBBASIC_PASSWORD:
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->ClientChallenge + 1, info->ClientChallenge[0]);
            break;
        case EM_DBBASIC_DBNAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->DbName, strlen(info->DbName));    
            break;
        case EM_DBBASIC_DBSQL:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->RequestArgs, strlen(info->RequestArgs));
            break;
        case EM_DBBASIC_DBIP:
            if (flow->ip_version == 4)
                get_iparray_to_string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
            else
                get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case EM_DBBASIC_DBPORT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, ntohs(flow->tuple.inner.port_dst));
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

    log_ptr->proto_id = PROTOCOL_DBBASIC;
    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_DBBASIC;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return;
}


/*
 get length of string in packet buffer

 SYNOPSIS
   my_tvb_strsize()
     tvb      packet buffer
     offset   current offset

 DESCRIPTION
   deliver length of string, delimited by either \0 or end of buffer

 RETURN VALUE
   length of string found, including \0 (if present)

*/
static int my_strsize(const uint8_t *payload, int offset, uint32_t payload_len)
{
    int len = find_str_end_len(payload + offset, payload_len - offset);
    if (len == -1) {
        len = payload_len - offset;
    }
    
    return len;
}

/*
 read "field length encoded" value from packet buffer

 SYNOPSIS
   tvb_get_fle()
     tvb     in    packet buffer
     offset  in    offset in buffer
     res     out   where to store FLE value, may be NULL
     is_null out   where to store ISNULL flag, may be NULL

 DESCRIPTION
   read FLE from packet buffer and store its value and ISNULL flag
   in caller provided variables

 RETURN VALUE
   length of FLE
*/
static int _get_fle(const uint8_t *payload, uint32_t offset, uint32_t payload_len, uint64_t *res, uint8_t *is_null)
{
    UNUSED(payload_len);
    
    uint8_t prefix;

    if (offset >= payload_len)
        return 0;
    prefix = get_uint8_t(payload, offset);

    if (is_null)
        *is_null = 0;

    switch (prefix) {
    case 251:
        if (res)
            *res = 0;
        if (is_null)
            *is_null = 1;
        break;
    case 252:
        if (res && offset + 1 + 2 <= payload_len)
            *res = pletoh16(payload + offset + 1);
        return 3;
    case 253:
        if (res && offset + 1 + 4 <= payload_len)
            *res = pletoh32(payload + offset + 1);
        return 5;
    case 254:
        if (res && offset + 1 + 8 <= payload_len)
            *res = pletoh64(payload + offset + 1);
        return 9;
    default:
        if (res)
            *res = prefix;
    }

    return 1;
}


/*
  Add a connect attributs entry to the connattrs subtree

  return bytes read
*/
static int add_connattrs_entry_to_tree(const uint8_t *payload, int offset, uint32_t payload_len) {
    uint64_t lenstr;
    int orig_offset = offset, lenfle;
    //const uint8_t *str;


    lenfle = _get_fle(payload, offset, payload_len, &lenstr, NULL);
    //proto_tree_add_uint64(connattrs_tree, hf_mysql_connattrs_name_length, tvb, offset, lenfle, lenstr);
    offset += lenfle;

    //proto_tree_add_item_ret_string(connattrs_tree, hf_mysql_connattrs_name, tvb, offset, (gint)lenstr, ENC_ASCII|ENC_NA, wmem_packet_scope(), &str);
    //proto_item_append_text(ti, " - %s", str);
    offset += (int)lenstr;

    lenfle = _get_fle(payload, offset, payload_len, &lenstr, NULL);
    //proto_tree_add_uint64(connattrs_tree, hf_mysql_connattrs_value_length, tvb, offset, lenfle, lenstr);
    offset += lenfle;

    //proto_tree_add_item_ret_string(connattrs_tree, hf_mysql_connattrs_value, tvb, offset, (gint)lenstr, ENC_ASCII|ENC_NA, wmem_packet_scope(), &str);
    //proto_item_append_text(ti, ": %s", str);
    offset += (int)lenstr;

    //proto_item_set_len(ti, offset - orig_offset);

    return (offset - orig_offset);
}


static int mysql_dissect_result_header(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    UNUSED(info);
    int fle;
    uint64_t num_fields, extra;


    fle = _get_fle(payload, offset, payload_len, &num_fields, NULL);
    //proto_tree_add_uint64(tree, hf_mysql_num_fields, tvb, offset, fle, num_fields);
    if (offset + fle < payload_len)
        offset += fle;
    else
        offset = payload_len;

    if (offset < payload_len) {
        fle = _get_fle(payload, offset, payload_len, &extra, NULL);
        //proto_tree_add_uint64(tree, hf_mysql_extra, tvb, offset, fle, extra);
        if (offset + fle < payload_len)
            offset += fle;
        else
            offset = payload_len;
    }

    if (num_fields) {
        session->state = FIELD_PACKET;
    } else {
        session->state = ROW_PACKET;
    }

    return offset;
}



/*
* Add length encoded string to tree
*/
static int mysql_field_add_lestring(const uint8_t *payload, uint32_t payload_len, uint32_t offset, char *res, int max_len)
{
    int copy_len;
    uint64_t lelen;

    offset += _get_fle(payload, offset, payload_len, &lelen, NULL);
    if (res && offset + lelen <= payload_len) {
        copy_len = (int)lelen >= max_len ? max_len - 1 : (int)lelen;
        strncpy(res, (const char *)payload + offset, copy_len);
        res[copy_len] = 0;
    }

    if (offset + lelen < payload_len)
        offset += lelen;
    else
        offset = payload_len;

    return offset;
}


static int mysql_dissect_response_prepare(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    UNUSED(info);

    if (offset + 1 + 4 + 2 + 2 + 1 > payload_len)
        return payload_len;

    //uint32_t stmt_id;
    /* 0, marker for OK packet */
    offset += 1;
    //proto_tree_add_item(tree, hf_mysql_stmt_id, tvb, offset, 4, ENC_LITTLE_ENDIAN);
    //stmt_id = pletoh32(payload + offset);
    offset += 4;
    //proto_tree_add_item(tree, hf_mysql_num_fields, tvb, offset, 2, ENC_LITTLE_ENDIAN);
    session->stmt_num_fields = pletoh32(payload + offset);
    offset += 2;
    //proto_tree_add_item(tree, hf_mysql_num_params, tvb, offset, 2, ENC_LITTLE_ENDIAN);
    session->stmt_num_params = pletoh32(payload + offset);

    offset += 2;
    /* Filler */
    offset += 1;

    if (session->stmt_num_params > 0)
        session->state = PREPARED_PARAMETERS;
    else if (session->stmt_num_fields > 0)
        session->state = PREPARED_FIELDS;
    else
        session->state = REQUEST;

    return payload_len;
}

/*解析数据，但是现在tbl中没有对应字段，没想好怎么存储*/
static int mysql_dissect_row_packet(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    UNUSED(session);
    UNUSED(info);
    
    while (offset < payload_len) {
        offset = mysql_field_add_lestring(payload, payload_len, offset, NULL, 0);
    }

    return offset;
}

static int mysql_dissect_field_packet(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    UNUSED(session);
    
    offset = mysql_field_add_lestring(payload, payload_len, offset, NULL, 0);
    offset = mysql_field_add_lestring(payload, payload_len, offset, info->DbName, sizeof(info->DbName));
    offset = mysql_field_add_lestring(payload, payload_len, offset, info->TableName, sizeof(info->TableName));
    offset = mysql_field_add_lestring(payload, payload_len, offset, NULL, 0);
    offset = mysql_field_add_lestring(payload, payload_len, offset, NULL, 0);
    offset = mysql_field_add_lestring(payload, payload_len, offset, NULL, 0);

    if (offset + 13 > payload_len)
    {
        return payload_len;
    }

    offset += 1; /* filler */
    
    //uint8_t lang = get_uint8_t(payload, offset);
    info->Charset = val_to_string(pletoh16(payload + offset), mysql_collation_vals);
    offset += 2; /* for charset */


    //proto_tree_add_item(tree, hf_mysql_fld_length, tvb, offset, 4, ENC_LITTLE_ENDIAN);
    offset += 4; /* length */

    //proto_tree_add_item(tree, hf_mysql_fld_type, tvb, offset, 1, ENC_NA);
    offset += 1; /* type */

    //proto_tree_add_bitmask_with_flags(tree, tvb, offset, hf_mysql_fld_flags, ett_field_flags, mysql_fld_flags, ENC_LITTLE_ENDIAN, BMT_NO_APPEND);
    offset += 2; /* flags */

    //proto_tree_add_item(tree, hf_mysql_fld_decimals, tvb, offset, 1, ENC_NA);
    offset += 1; /* decimals */

    offset += 2; /* filler */

    /* default (Only use for show fields) */
    if (offset < payload_len) {
        offset = mysql_field_add_lestring(payload, payload_len, offset, NULL, 0);
    }
    return offset;
}


static int mysql_dissect_auth_switch_request(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    UNUSED(info);
    int lenstr;

    session->state = AUTH_SWITCH_RESPONSE;

    /* Status (Always 0xfe) */
    //proto_tree_add_item(tree, hf_mysql_auth_switch_request_status, tvb, offset, 1, ENC_LITTLE_ENDIAN);
    offset += 1;

    /* name */
    lenstr = my_strsize(payload, offset, payload_len);
    //proto_tree_add_item(tree, hf_mysql_auth_switch_request_name, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
    offset += lenstr;

    /* Data */
    lenstr = my_strsize(payload, offset, payload_len);
    //proto_tree_add_item(tree, hf_mysql_auth_switch_request_data, tvb, offset, lenstr, ENC_NA);
    offset += lenstr;

    return payload_len;

}
        
static int mysql_dissect_auth_switch_response(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    UNUSED(session);
    UNUSED(info);

    int lenstr;

    //col_set_str(pinfo->cinfo, COL_INFO, "Auth Switch Response" );

    /* Data */
    lenstr = my_strsize(payload, offset, payload_len);
    //proto_tree_add_item(tree, hf_mysql_auth_switch_response_data, tvb, offset, lenstr, ENC_NA);
    offset += lenstr;

    return payload_len;

}

static int mysql_dissect_ok_packet(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    UNUSED(payload);
    UNUSED(offset);
    UNUSED(info);
    #if 0
    uint64_t lenstr;
    uint64_t affected_rows;
    uint64_t insert_id;
    int fle;
    guint16 server_status = 0;

    col_append_str(pinfo->cinfo, COL_INFO, " OK" );

    fle = tvb_get_fle(tvb, offset, &affected_rows, NULL);
    proto_tree_add_uint64(tree, hf_mysql_affected_rows, tvb, offset, fle, affected_rows);
    offset += fle;

    fle= tvb_get_fle(tvb, offset, &insert_id, NULL);
    if (tree && insert_id) {
        proto_tree_add_uint64(tree, hf_mysql_insert_id, tvb, offset, fle, insert_id);
    }
    offset += fle;

    if (tvb_reported_length_remaining(tvb, offset) > 0) {
        offset = mysql_dissect_server_status(tvb, offset, tree, &server_status);

        /* 4.1+ protocol only: 2 bytes number of warnings */
        if (conn_data->clnt_caps & conn_data->srv_caps & MYSQL_CAPS_CU) {
            proto_tree_add_item(tree, hf_mysql_num_warn, tvb, offset, 2, ENC_LITTLE_ENDIAN);
        offset += 2;
        }
    }

    if (conn_data->clnt_caps_ext & MYSQL_CAPS_ST) {
        if (tvb_reported_length_remaining(tvb, offset) > 0) {
            guint64 session_track_length;
            proto_item *tf;
            proto_item *session_track_tree = NULL;
            int length;

            offset += tvb_get_fle(tvb, offset, &lenstr, NULL);
            /* first read the optional message */
            if (lenstr) {
                proto_tree_add_item(tree, hf_mysql_message, tvb, offset, (gint)lenstr, ENC_ASCII|ENC_NA);
                offset += (int)lenstr;
            }

            /* session state tracking */
            if (server_status & MYSQL_STAT_SESSION_STATE_CHANGED) {
                fle = tvb_get_fle(tvb, offset, &session_track_length, NULL);
                tf = proto_tree_add_item(tree, hf_mysql_session_track_data, tvb, offset, -1, ENC_NA);
                session_track_tree = proto_item_add_subtree(tf, ett_session_track_data);
                proto_tree_add_uint64(tf, hf_mysql_session_track_data_length, tvb, offset, fle, session_track_length);
                offset += fle;

                while (session_track_length > 0) {
                    length = add_session_tracker_entry_to_tree(tvb, pinfo, session_track_tree, offset);
                    offset += length;
                    session_track_length -= length;
                }
            }
        }
    } else {
        /* optional: message string */
        if (tvb_reported_length_remaining(tvb, offset) > 0) {
            lenstr = tvb_reported_length_remaining(tvb, offset);
            proto_tree_add_item(tree, hf_mysql_message, tvb, offset, (gint)lenstr, ENC_ASCII|ENC_NA);
            offset += (int)lenstr;
        }
    }
    #endif
    session->state = REQUEST;
    return payload_len;
}

/*mysql all use litter endding*/
static int mysql_identify(struct flow_info *flow, uint8_t C2S, const uint8_t *p, const uint32_t len)
{
    uint16_t a;
    const uint8_t *payload = p;
    uint16_t payload_len = len;

    if (g_config.protocol_switch[PROTOCOL_MYSQL] == 0)
    {
        return 0;
    }

    if (payload_len > 38 //min length
            && pletoh16(payload) == payload_len - 4  //first 3 bytes are length
            && get_uint8_t(payload, 2) == 0x00 //3rd byte of packet length
            && get_uint8_t(payload, 3) == 0x00 //packet sequence number is 0 for startup packet
            && get_uint8_t(payload, 5) > 0x30  //server version > 0
            && get_uint8_t(payload, 5) < 0x37  //server version < 7
            && get_uint8_t(payload, 6) == 0x2e //dot  5.x.xxx
            ) 
    {
        for (a = 7; a + 31 < payload_len; a++) {
            if (payload[a] == 0x00) {
                if (get_uint8_t(payload, a + 13) == 0x00 //filler byte
                        //&& get_uint64_t(payload, a + 19) == 0x0ULL   //13 more
                        && get_uint64_t(payload, a + 23) == 0x0ULL   //13 more
                        && get_uint32_t(payload, a + 27) == 0x0  //filler bytes
                        && get_uint8_t(payload, a + 31) == 0x0) {
                    flow->real_protocol_id = PROTOCOL_MYSQL;
                    flow->slave_protocol_id  =   PROTOCOL_DBBASIC;
                    return flow->real_protocol_id;
                }
                break;
            }
        }
    }

    return 0;
}


static int mysql_dissect_error_packet(const uint8_t *payload, uint32_t payload_len, uint32_t offset, struct mysql_session *session)
{
    UNUSED(payload);
    UNUSED(offset);
    UNUSED(session);

/*
    col_append_fstr(pinfo->cinfo, COL_INFO, " Error %d", tvb_get_letohs(tvb, offset));

    proto_tree_add_item(tree, hf_mysql_error_code, tvb, offset, 2, ENC_LITTLE_ENDIAN);
    offset += 2;

    if (tvb_get_guint8(tvb, offset) == '#')
    {
        offset += 1;
        proto_tree_add_item(tree, hf_mysql_sqlstate, tvb, offset, 5, ENC_ASCII|ENC_NA);
        offset += 5;
    }

    proto_tree_add_item(tree, hf_mysql_error_string, tvb, offset, -1, ENC_ASCII|ENC_NA);
    offset += tvb_reported_length_remaining(tvb, offset);
*/
    return payload_len;
}

static int mysql_dissect_login(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    uint8_t col_vals;
    int lenstr;
    int copy_len;
    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = payload_len;

    /* after login there can be OK or DENIED */
    session->state = RESPONSE_OK;

    if (-1 == dpi_get_le16(&pkt, offset, &session->clnt_caps))
        return payload_len;
    snprintf(info->ClientCapabilities, sizeof(info->ClientCapabilities), "0x%04x", session->clnt_caps);
    offset += 2;
    
    if (session->clnt_caps & MYSQL_CAPS_SL) /* Next packet will be use SSL */
    {
        //ssl not do
        return payload_len;
    }
    if (session->clnt_caps & MYSQL_CAPS_CU) /* 4.1 protocol */
    {
        if (-1 == dpi_get_le16(&pkt, offset, &session->clnt_caps_ext))
            return payload_len;
        snprintf(info->ExtClientCapabilities, sizeof(info->ExtClientCapabilities), "0x%04x", session->clnt_caps_ext);
        offset += 2;

        //proto_tree_add_item(login_tree, hf_mysql_max_packet, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        offset += 4;
        if (-1 == dpi_get_uint8(&pkt, offset, &col_vals))
            return payload_len;
        info->Charset = val_to_string(col_vals, mysql_collation_vals);
        offset += 1; /* for charset */
        offset += 23; /* filler bytes */
    } else { /* pre-4.1 */
        //proto_tree_add_item(login_tree, hf_mysql_max_packet, tvb, offset, 3, ENC_LITTLE_ENDIAN);
        offset += 3;
    }

    /* User name */
    
    lenstr = my_strsize(payload, offset, payload_len);
    if (lenstr < 0)
        return payload_len;
    if ((size_t)lenstr > sizeof(info->UserName) - 1)
        copy_len = sizeof(info->UserName) - 1;
    else
        copy_len = lenstr;
    strncpy(info->UserName, (const char *)payload + offset, copy_len);
    
    offset += lenstr;

    /* rest is optional */
    if (offset >= payload_len)
        return offset;

    /* password: asciiz or length+ascii */
    if (session->clnt_caps & MYSQL_CAPS_SC) {
        uint8_t tmp;
        if (-1 == dpi_get_uint8(&pkt, offset, &tmp))
            return payload_len;
        lenstr = tmp;
        offset += 1;
    } else {
        lenstr = my_strsize(payload, offset, payload_len);
    }
    if (lenstr > 1) {
        info->ClientChallenge[0] = lenstr > 63 ? 63 : lenstr;
        memcpy(info->ClientChallenge + 1, payload + offset, info->ClientChallenge[0]);
    }
    offset += lenstr;

    /* optional: initial schema */
    if (session->clnt_caps & MYSQL_CAPS_CD)
    {
        lenstr = my_strsize(payload, offset, payload_len);
        if ((size_t)lenstr > sizeof(info->DbName) - 1)
            copy_len = sizeof(info->DbName) - 1;
        else
            copy_len = lenstr;
        strncpy(info->DbName, (const char *)payload + offset, copy_len);
        offset += lenstr;
    }

    /* optional: authentication plugin */
    if (session->clnt_caps_ext & MYSQL_CAPS_PA)
    {
        session->state = AUTH_SWITCH_REQUEST;
        lenstr = my_strsize(payload, offset, payload_len);
        if (lenstr < 0){
            return offset;
        }
        offset += lenstr;
        //proto_tree_add_item(login_tree, hf_mysql_client_auth_plugin, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
    }

    /* optional: connection attributes */
    if (session->clnt_caps_ext & MYSQL_CAPS_CA && offset < payload_len)
    {
        int lenfle;
        uint64_t connattrs_length;
        int length;

        lenfle = _get_fle(payload, offset, payload_len, &connattrs_length, NULL);
        //tf = proto_tree_add_item(login_tree, hf_mysql_connattrs, tvb, offset, (guint32)connattrs_length, ENC_NA);
        //connattrs_tree = proto_item_add_subtree(tf, ett_connattrs);
        //proto_tree_add_uint64(connattrs_tree, hf_mysql_connattrs_length, tvb, offset, lenfle, connattrs_length);
        offset += lenfle;

        while (connattrs_length > 0) {
            length = add_connattrs_entry_to_tree(payload, offset, payload_len);
            offset += length;
            connattrs_length -= length;
        }
    }

    return offset;
}

static int mysql_dissect_request(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    uint8_t opcode;
    int lenstr;
    int copy_len;
    //uint32_t stmt_id;
    //my_stmt_data_t *stmt_data;
    //int stmt_pos, param_offset;
    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = payload_len;

    if(session->state == AUTH_SWITCH_RESPONSE){
        return mysql_dissect_auth_switch_response(payload, payload_len, offset, session, info);
    }
    
    if (-1 == dpi_get_uint8(&pkt, offset, &opcode))
        return payload_len;
    info->RequestCommand = val_to_string(opcode, mysql_command_vals);
    offset += 1;

    switch (opcode) {

    case MYSQL_QUIT:
        break;

    case MYSQL_PROCESS_INFO:
        session->state = RESPONSE_TABULAR;
        break;

    case MYSQL_DEBUG:
    case MYSQL_PING:
        session->state = RESPONSE_OK;
        break;

    case MYSQL_STATISTICS:
        session->state = RESPONSE_MESSAGE;
        break;

    case MYSQL_INIT_DB:
    case MYSQL_CREATE_DB:
    case MYSQL_DROP_DB:
        lenstr = my_strsize(payload, offset, payload_len);
        if (lenstr < 0)
            return payload_len;
        //if (lenstr > sizeof(info->UserName) - 1)
        //    copy_len = sizeof(info->UserName) - 1;
        //else
        //    copy_len = lenstr;
        //strncpy(info->UserName, payload + offset, copy_len);
        
        offset += lenstr;
        session->state =  RESPONSE_OK;
        break;

    case MYSQL_QUERY:
        lenstr = my_strsize(payload, offset, payload_len);
        if ((size_t)lenstr > sizeof(info->RequestArgs) - 1)
            copy_len = sizeof(info->RequestArgs) - 1;
        else
            copy_len = lenstr;
        strncpy(info->RequestArgs, (const char *)payload + offset, copy_len);

        offset += lenstr;
        session->state = RESPONSE_TABULAR;
        break;

    case MYSQL_STMT_PREPARE:
        lenstr = my_strsize(payload, offset, payload_len);
        if ((size_t)lenstr > sizeof(info->RequestArgs) - 1)
            copy_len = sizeof(info->RequestArgs) - 1;
        else
            copy_len = lenstr;
        strncpy(info->RequestArgs, (const char *)payload + offset, copy_len);

        offset += lenstr;
        session->state = RESPONSE_PREPARE;
        break;

    case MYSQL_STMT_CLOSE:
        if (offset + 4 <= payload_len)
            offset += 4;
        session->state = REQUEST;
        break;

    case MYSQL_STMT_RESET:        
        if (offset + 4 <= payload_len)
            offset += 4;
        session->state = RESPONSE_OK;
        break;

    case MYSQL_FIELD_LIST:
        lenstr = my_strsize(payload, offset, payload_len);
        if ((size_t)lenstr > sizeof(info->TableName) - 1)
            copy_len = sizeof(info->TableName) - 1;
        else
            copy_len = lenstr;
        strncpy(info->TableName, (const char *)payload + offset, copy_len);
        offset += lenstr;
        session->state = RESPONSE_SHOW_FIELDS;
        break;

    case MYSQL_PROCESS_KILL:
        if (-1 == dpi_get_le32(&pkt, offset, &info->MainServerId))
            return payload_len;
        offset += 4;
        session->state = RESPONSE_OK;
        break;

    case MYSQL_CHANGE_USER:
        lenstr = my_strsize(payload, offset, payload_len);
        if ((size_t)lenstr > sizeof(info->UserName) - 1)
            copy_len = sizeof(info->UserName) - 1;
        else
            copy_len = lenstr;
        strncpy(info->UserName, (const char *)payload + offset, copy_len);
        offset += lenstr;

        if (session->clnt_caps & MYSQL_CAPS_SC) {
            uint8_t tmp;
            if (-1 == dpi_get_uint8(&pkt, offset, &tmp))
                return payload_len;
            lenstr = tmp;
            offset += 1;
        } else {
            lenstr = my_strsize(payload, offset, payload_len);
        }
        //proto_tree_add_item(req_tree, hf_mysql_passwd, tvb, offset, lenstr, ENC_NA);
        offset += lenstr;

        lenstr = my_strsize(payload, offset, payload_len);
        if ((size_t)lenstr > sizeof(info->RequestArgs) - 1)
            copy_len = sizeof(info->RequestArgs) - 1;
        else
            copy_len = lenstr;
        strncpy(info->RequestArgs, (const char *)payload + offset, copy_len);
        offset += lenstr;

        if (offset < payload_len) {
            uint8_t charset;
            if (-1 == dpi_get_uint8(&pkt, offset, &charset))
                return payload_len;            
            info->Charset = val_to_string(charset, mysql_collation_vals);
            //proto_tree_add_item(req_tree, hf_mysql_charset, tvb, offset, 1, ENC_NA);
            offset += 2; /* for charset */
        }
        session->state = RESPONSE_OK;

        /* optional: authentication plugin */
        if (session->clnt_caps_ext & MYSQL_CAPS_PA)
        {
            session->state = AUTH_SWITCH_REQUEST;
            lenstr = my_strsize(payload, offset, payload_len);
            if (lenstr < 0)
                return payload_len;
            //proto_tree_add_item(req_tree, hf_mysql_client_auth_plugin, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
            offset += lenstr;
        }
        
        /* optional: connection attributes */
        if (session->clnt_caps_ext & MYSQL_CAPS_CA && offset < payload_len)
        {
            int lenfle;
            uint64_t connattrs_length;
            int length;
        
            lenfle = _get_fle(payload, offset, payload_len, &connattrs_length, NULL);
            //tf = proto_tree_add_item(login_tree, hf_mysql_connattrs, tvb, offset, (guint32)connattrs_length, ENC_NA);
            //connattrs_tree = proto_item_add_subtree(tf, ett_connattrs);
            //proto_tree_add_uint64(connattrs_tree, hf_mysql_connattrs_length, tvb, offset, lenfle, connattrs_length);
            offset += lenfle;
        
            while (connattrs_length > 0) {
                length = add_connattrs_entry_to_tree(payload, offset, payload_len);
                offset += length;
                connattrs_length -= length;
            }
        }

        break;

    case MYSQL_REFRESH:
        //proto_tree_add_bitmask_with_flags(req_tree, tvb, offset,
        //    hf_mysql_refresh, ett_refresh, mysql_rfsh_flags,
        //    ENC_BIG_ENDIAN, BMT_NO_APPEND);
        if (offset + 1 <= payload_len)
            offset += 1;
        session->state = RESPONSE_OK;
        break;

    case MYSQL_SHUTDOWN:
        //proto_tree_add_item(req_tree, hf_mysql_shutdown, tvb, offset, 1, ENC_NA);        
        if (offset + 1 <= payload_len)
            offset += 1;
        session->state = RESPONSE_OK;
        break;

    case MYSQL_SET_OPTION:
        //proto_tree_add_item(req_tree, hf_mysql_option, tvb, offset, 2, ENC_LITTLE_ENDIAN);
        if (offset + 2 <= payload_len)
            offset += 2;
        session->state = RESPONSE_OK;
        break;

    case MYSQL_STMT_FETCH:
        //proto_tree_add_item(req_tree, hf_mysql_stmt_id, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        if (offset + 4 <= payload_len)
            offset += 4;

        //proto_tree_add_item(req_tree, hf_mysql_num_rows, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        if (offset + 4 <= payload_len)
            offset += 4;
        session->state = RESPONSE_TABULAR;
        break;

    case MYSQL_STMT_SEND_LONG_DATA:
        session->state = REQUEST;
        //proto_tree_add_item(req_tree, hf_mysql_stmt_id, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        //stmt_id = pletoh32(payload + offset);
        if (offset + 4 <= payload_len)
            offset += 4;

        /*
        stmt_data = (my_stmt_data_t *)wmem_tree_lookup32(session->stmts, stmt_id);
        if (stmt_data != NULL) {
            guint16 data_param = tvb_get_letohs(tvb, offset);
            if (stmt_data->nparam > data_param) {
                stmt_data->param_flags[data_param] |= MYSQL_PARAM_FLAG_STREAMED;
            }
        }

        proto_tree_add_item(req_tree, hf_mysql_param, tvb, offset, 2, ENC_LITTLE_ENDIAN);*/
        if (offset + 2 <= payload_len)
            offset += 2;

        /* rest is data */
        lenstr = my_strsize(payload, offset, payload_len);
        if (lenstr < 0)
            return payload_len;
        offset += lenstr;
        break;

    case MYSQL_STMT_EXECUTE:

        offset = payload_len;
        session->state = RESPONSE_TABULAR;
        break;
        #if 0
        proto_tree_add_item(req_tree, hf_mysql_stmt_id, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        stmt_id = tvb_get_letohl(tvb, offset);
        offset += 4;

        if (conn_data->major_version >= 5) {
            proto_tree_add_item(req_tree, hf_mysql_exec_flags5, tvb, offset, 1, ENC_NA);
        } else {
            proto_tree_add_item(req_tree, hf_mysql_exec_flags4, tvb, offset, 1, ENC_NA);
        }
        offset += 1;

        proto_tree_add_item(req_tree, hf_mysql_exec_iter, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        offset += 4;

        stmt_data = (my_stmt_data_t *)wmem_tree_lookup32(conn_data->stmts, stmt_id);
        if (stmt_data != NULL) {
            if (stmt_data->nparam != 0) {
                guint8 stmt_bound;
                offset += (stmt_data->nparam + 7) / 8; /* NULL bitmap */
                proto_tree_add_item(req_tree, hf_mysql_new_parameter_bound_flag, tvb, offset, 1, ENC_NA);
                stmt_bound = tvb_get_guint8(tvb, offset);
                offset += 1;
                if (stmt_bound == 1) {
                    param_offset = offset + stmt_data->nparam * 2;
                    for (stmt_pos = 0; stmt_pos < stmt_data->nparam; stmt_pos++) {
                        if (!mysql_dissect_exec_param(req_tree, tvb, &offset, &param_offset,
                                          stmt_data->param_flags[stmt_pos], pinfo))
                            break;
                    }
                    offset = param_offset;
                }
            }
        } else {
            lenstr = tvb_reported_length_remaining(tvb, offset);
            if (tree &&  lenstr > 0) {
                ti = proto_tree_add_item(req_tree, hf_mysql_payload, tvb, offset, lenstr, ENC_NA);
                expert_add_info(pinfo, ti, &ei_mysql_prepare_response_needed);
            }
            offset += lenstr;
        }
#if 0
/* FIXME: rest needs metadata about statement */
#else
        lenstr = tvb_reported_length_remaining(tvb, offset);
        if (tree &&  lenstr > 0) {
            ti = proto_tree_add_item(req_tree, hf_mysql_payload, tvb, offset, lenstr, ENC_NA);
            expert_add_info_format(pinfo, ti, &ei_mysql_dissector_incomplete, "FIXME: execute dissector incomplete");
        }
        offset += lenstr;
#endif
        mysql_set_conn_state(pinfo, conn_data, RESPONSE_TABULAR);
        break;
    #endif
    case MYSQL_BINLOG_DUMP:    
        offset = payload_len;
        session->state = REQUEST;        
        break;
        #if 0
        proto_tree_add_item(req_tree, hf_mysql_binlog_position, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        offset += 4;

        proto_tree_add_item(req_tree, hf_mysql_binlog_flags, tvb, offset, 2, ENC_BIG_ENDIAN);
        offset += 2;

        proto_tree_add_item(req_tree, hf_mysql_binlog_server_id, tvb, offset, 4, ENC_LITTLE_ENDIAN);
        offset += 4;

        /* binlog file name ? */
        lenstr = tvb_reported_length_remaining(tvb, offset);
        if (tree &&  lenstr > 0) {
            proto_tree_add_item(req_tree, hf_mysql_binlog_file_name, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
        }
        offset += lenstr;
        #endif
/* FIXME: implement replication packets */
    case MYSQL_TABLE_DUMP:
    case MYSQL_CONNECT_OUT:
    case MYSQL_REGISTER_SLAVE:
        offset = payload_len;
        session->state = REQUEST;
        break;
        /*
        ti = proto_tree_add_item(req_tree, hf_mysql_payload, tvb, offset, -1, ENC_NA);
        expert_add_info_format(pinfo, ti, &ei_mysql_dissector_incomplete, "FIXME: implement replication packets");
        offset += tvb_reported_length_remaining(tvb, offset);
        mysql_set_conn_state(pinfo, conn_data, REQUEST);
        */

    default:
        offset = payload_len;
        session->state = UNDEFINED;
        break;
        /*
        ti = proto_tree_add_item(req_tree, hf_mysql_payload, tvb, offset, -1, ENC_NA);
        expert_add_info(pinfo, ti, &ei_mysql_command);
        offset += tvb_reported_length_remaining(tvb, offset);
        mysql_set_conn_state(pinfo, conn_data, UNDEFINED);
        */
    }

    //proto_item_set_end(request_item, tvb, offset);
    return offset;
}

static int mysql_dissect_greeting(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    uint8_t protocol;
    int lenstr;
    int ver_offset;
    int copy_len;
    uint16_t tmp;

    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = payload_len;

    if (-1 == dpi_get_uint8(&pkt, offset, &protocol))
        return payload_len;
    //protocol= get_uint8_t(payload, offset);

    if (protocol == 0xff) {
        return mysql_dissect_error_packet(payload, payload_len, offset + 1, session);
    }
    session->state = LOGIN;

    info->protocol = protocol;
    offset += 1;

    /* version string */
    lenstr = my_strsize(payload, offset, payload_len);
    if ((size_t)lenstr > sizeof(info->serverversion) - 1)
        copy_len = sizeof(info->serverversion) - 1;
    else
        copy_len = lenstr;
    strncpy(info->serverversion, (const char *)payload + offset, copy_len);

    session->major_version = 0;
    for (ver_offset = 0; ver_offset < lenstr; ver_offset++) {
        uint8_t ver_char = get_uint8_t(payload, offset + ver_offset);
        if (ver_char == '.')
            break;
        session->major_version = session->major_version * 10 + ver_char - '0';
    }
    offset += lenstr;

    /* 4 bytes little endian thread_id */
    
    if (-1 == dpi_get_le32(&pkt, offset, &info->serverthreadid))
        return payload_len;
    offset += 4;

    /* salt string */
    lenstr = my_strsize(payload, offset, payload_len);
    if (lenstr < 0)
        return payload_len;

    //proto_tree_add_item(greeting_tree, hf_mysql_salt, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
    offset += lenstr;

    if (offset + 2 > payload_len)
        return payload_len;

    /* 2 bytes CAPS */
    if (-1 == dpi_get_le16(&pkt, offset, &session->srv_caps))
        return payload_len;    
    snprintf(info->ServerCapabilities, sizeof(info->ServerCapabilities), "0x%04x", session->srv_caps);
    offset += 2;

    uint8_t lang;
    if (-1 == dpi_get_uint8(&pkt, offset, &lang))
        return payload_len;    
    info->Charset = val_to_string(lang, mysql_collation_vals);
    offset += 1; /* for charset */
    
    /* 2 bytes server status */
    if (-1 == dpi_get_le16(&pkt, offset, &tmp))
        return payload_len;    
    snprintf(info->ServerStatus, sizeof(info->ServerStatus), "0x%04x", tmp);
    offset += 2;
    
    /* 2 bytes server ExtServerCapabilities */
    if (-1 == dpi_get_le16(&pkt, offset, &tmp))
        return payload_len;    
    snprintf(info->ExtServerCapabilities, sizeof(info->ExtServerCapabilities), "0x%04x", tmp);
    offset += 2;


    /* 1 byte Auth Plugin Length */
    //proto_tree_add_item(greeting_tree, hf_mysql_auth_plugin_length, tvb, offset, 1, ENC_NA);
    offset += 1;

    /* 10 bytes unused */
    //proto_tree_add_item(greeting_tree, hf_mysql_unused, tvb, offset, 10, ENC_NA);
    offset += 10;

    /* 4.1+ server: rest of salt */
    if (offset < payload_len) {
        lenstr = my_strsize(payload, offset, payload_len);
        //proto_tree_add_item(greeting_tree, hf_mysql_salt2, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
        offset += lenstr;
    }

    /* 5.x server: auth plugin */
    if (offset < payload_len) {
        lenstr = my_strsize(payload, offset, payload_len);
        //proto_tree_add_item(greeting_tree, hf_mysql_auth_plugin, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
        offset += lenstr;
    }
    return offset;
}

/*
 * Decode the header of a compressed packet
 * https://dev.mysql.com/doc/internals/en/compressed-packet-header.html
 */
static int mysql_dissect_compressed_header(const uint8_t *payload, uint32_t payload_len, int offset)
{
    UNUSED(payload);
    UNUSED(payload_len);
    
    //proto_tree_add_item(mysql_tree, hf_mysql_compressed_packet_length, tvb, offset, 3, ENC_LITTLE_ENDIAN);
    offset += 3;

    //proto_tree_add_item(mysql_tree, hf_mysql_compressed_packet_number, tvb, offset, 1, ENC_NA);
    offset += 1;

    //proto_tree_add_item(mysql_tree, hf_mysql_compressed_packet_length_uncompressed, tvb, offset, 3, ENC_LITTLE_ENDIAN);
    offset += 3;
    return offset;
}

static int mysql_dissect_response(const uint8_t *payload, uint32_t payload_len, uint32_t offset, 
        struct mysql_session *session, struct mysql_info *info)
{
    uint8_t response_code;
    int lenstr;
    uint16_t server_status = 0;
    struct dpi_pkt_st pkt;
    pkt.payload = payload;
    pkt.payload_len = payload_len;
    
    if (-1 == dpi_get_uint8(&pkt, offset, &response_code))
        return payload_len;

    if (response_code == 0xff ) {
        offset = mysql_dissect_error_packet(payload, payload_len, offset + 1, session);
        session->state = REQUEST;
    } else if (response_code == 0xfe && payload_len - offset < 9) {
        //ti = proto_tree_add_item(tree, hf_mysql_eof, tvb, offset, 1, ENC_NA);
        offset += 1;

        /* pre-4.1 packet ends here */
        if (offset < payload_len) {            
            //2 bytes mysql_num_warn
            offset += 2;
            if (-1 == dpi_get_le16(&pkt, offset, &server_status))
                return payload_len;            
            snprintf(info->ServerStatus, sizeof(info->ServerStatus), "0x%04x", server_status);
            offset += 2;
        }

        switch (session->state) {
            case FIELD_PACKET:
                session->state = ROW_PACKET;
                break;
            case ROW_PACKET:
                if (server_status & MYSQL_STAT_MU) {
                    session->state = RESPONSE_TABULAR;
                } else {
                    session->state = REQUEST;
                }
                break;
            case PREPARED_PARAMETERS:
                if (session->stmt_num_fields > 0) {
                    session->state = PREPARED_FIELDS;
                } else {
                    session->state = REQUEST;
                }
                break;
            case PREPARED_FIELDS:
                session->state = REQUEST;
                break;
            default:
                /* This should be an unreachable case */
                session->state = REQUEST;
        }
    } else if (response_code == 0) {
        if (session->state == RESPONSE_PREPARE) {
            offset = mysql_dissect_response_prepare(payload, payload_len, offset, session, info);
        } else if ((int)(payload_len - offset - 1) > _get_fle(payload, offset + 1, payload_len, NULL, NULL)) {
            offset = mysql_dissect_ok_packet(payload, payload_len, offset, session, info);
            if (session->compressed_state == MYSQL_COMPRESS_INIT) {
                /* This is the OK packet which follows the compressed protocol setup */
                session->compressed_state = MYSQL_COMPRESS_ACTIVE;
            }
        } else {
            offset = mysql_dissect_result_header(payload, payload_len, offset, session, info);
        }
    } else {
        switch (session->state) {
            case RESPONSE_MESSAGE:
                if ((lenstr = payload_len - offset)) {
                    //proto_tree_add_item(tree, hf_mysql_message, tvb, offset, lenstr, ENC_ASCII|ENC_NA);
                    offset += lenstr;
                }
                session->state = REQUEST;
                break;

            case RESPONSE_TABULAR:
                offset = mysql_dissect_result_header(payload, payload_len, offset, session, info);
                break;

            case FIELD_PACKET:
            case RESPONSE_SHOW_FIELDS:
            case RESPONSE_PREPARE:
            case PREPARED_PARAMETERS:
                offset = mysql_dissect_field_packet(payload, payload_len, offset, session, info);
                break;

            case ROW_PACKET:
                offset = mysql_dissect_row_packet(payload, payload_len, offset, session, info);
                break;

            case PREPARED_FIELDS:
                offset = mysql_dissect_field_packet(payload, payload_len, offset, session, info);
                break;

            case AUTH_SWITCH_REQUEST:
                offset = mysql_dissect_auth_switch_request(payload, payload_len, offset, session, info);
                break;

            default:
                //ti = proto_tree_add_item(tree, hf_mysql_payload, tvb, offset, -1, ENC_NA);
                //expert_add_info(pinfo, ti, &ei_mysql_unknown_response);
                //offset += tvb_reported_length_remaining(tvb, offset);
                offset = payload_len;
                session->state = UNDEFINED;
        }
    }

    return offset;
}

static void  mysql_flow_finish(struct flow_info *flow)
{
    if (flow->app_session)
    {
        dpi_free(flow->app_session);
        flow->app_session = NULL;
    }
}

static int mysql_pkt_miss(struct flow_info *flow, uint8_t C2S, uint32_t mis_len)
{

    return 0;
}

static int mysql_dissect(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t  payload_len)
{
    uint8_t is_request;
    uint8_t packet_number;
    uint32_t offset = 0;
    uint32_t field_len;
    struct mysql_session *session;
    struct mysql_info info;
    struct dpi_pkt_st pkt;

    int direction   =   !C2S; //direction 与 C2S 是相反关系, 见:enum flow_dir

    memset(&info, 0, sizeof(info));

    flow->match_data_len = payload_len;

    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct mysql_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct mysql_session));
        session = (struct mysql_session *)flow->app_session;

        session->state = UNDEFINED;
        session->srv_caps= 0;
        session->clnt_caps= 0;
        session->clnt_caps_ext= 0;
        session->major_version= 0;
    }

    session = (struct mysql_session *)flow->app_session;
    pkt.payload = payload;
    pkt.payload_len = payload_len;

    if (direction == FLOW_DIR_SRC2DST)
        is_request = 1;
    else
        is_request = 0;

    while (offset < payload_len) {
        if ((session->frame_start_compressed)) {
            if (session->compressed_state == MYSQL_COMPRESS_ACTIVE) {
                offset = mysql_dissect_compressed_header(payload, payload_len, offset);
            }
        }

        if (-1 == dpi_get_le24(&pkt, offset, &field_len))
            break;        
        offset += 3;
        if (offset + field_len > payload_len)
            break;
        
        if (-1 == dpi_get_uint8(&pkt, offset, &packet_number))
            break;        
        offset++;

        if (!is_request) {
            if (packet_number == 0 && session->state == UNDEFINED) {
                info.msgtype = "Server Greeting";
                offset = mysql_dissect_greeting(payload, offset + field_len, offset, session, &info);
            } else {
                info.msgtype = "Response";
                flow->userdata[0] = 1;
                offset = mysql_dissect_response(payload, offset + field_len, offset, session, &info);
            }
        } else {
            if (session->state == LOGIN && (packet_number == 1 /*|| (packet_number == 2 && is_ssl)*/)) {
                info.msgtype = "Login Request";
                offset = mysql_dissect_login(payload, offset + field_len, offset, session, &info);
                if (session->srv_caps & MYSQL_CAPS_CP) {
                    if (session->clnt_caps & MYSQL_CAPS_CP) {
                        session->frame_start_compressed = 1;
                        session->compressed_state = MYSQL_COMPRESS_INIT;
                    }
                }
            } else {
                info.msgtype = "Request";
                flow->userdata[0] = 1;
                offset = mysql_dissect_request(payload, offset + field_len, offset, session, &info);
            }
        }

       write_dbbasic_log(flow, direction, &info, NULL);
    }

    return 0;
}

extern struct decode_t decode_mysql;
static int mysql_initial(struct decode_t *decode)
{
    decode_on_port_tcp(3306, &decode_mysql);

    dpi_register_proto_schema(mysql_field_array,EM_MYSQL_MAX,"mysql");
    map_fields_info_register(mysql_field_array,PROTOCOL_MYSQL, EM_MYSQL_MAX,"mysql");
    register_tbl_array(TBL_LOG_MYSQL, 0, "mysql", NULL);

    dpi_register_proto_schema(dbbasic_field_array,EM_DBBASIC_MAX,"dbbasic");
    map_fields_info_register(dbbasic_field_array,PROTOCOL_DBBASIC, EM_DBBASIC_MAX,"dbbasic");
    register_tbl_array(TBL_LOG_DBBASIC, 0, "dbbasic", NULL);
    return 0;
}

static int mysql_destroy(struct decode_t *decode)
{
    return 0;
}

struct decode_t decode_mysql = {
    .name           =   "mysql",
    .decode_initial =   mysql_initial,
    .pkt_identify   =   mysql_identify,
    .pkt_dissect    =   mysql_dissect,
    .pkt_miss       =   mysql_pkt_miss,
    .flow_finish    =   mysql_flow_finish,
    .decode_destroy =   mysql_destroy,
};


