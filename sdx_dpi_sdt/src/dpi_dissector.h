/****************************************************************************************
 * 文 件 名 : dpi_dissector.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_DISSECTOR_H_
#define _DPI_DISSECTOR_H_

#define IPPROTO_OSPF            89      /* OSPF Interior Gateway Protocol - RFC1583 */
#define IPPROTO_EIGRP           88
#define IPPROTO_VRRP            (112)   // Virtual Router Redundancy Protocol
#ifndef IPPROTO_L2TP
  #define IPPROTO_L2TP			(115)   // Layer 2 tunneling over IP
#endif

#include "dpi_detect.h"

void dissect_isis(const uint8_t *payload, uint32_t payload_len);
void dissect_stp(const uint8_t *payload, uint32_t payload_len, struct work_process_data *workflow);
void dissect_ppp_family(struct flow_info *flow, const uint8_t* payload, uint16_t payload_len, int upper_proto);

int dpi_dissect_ah(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dpi_dissect_esp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dpi_dissect_gre(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dpi_dissect_icmp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dpi_dissect_ospf(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dpi_dissect_rsvp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dpi_dissect_eigrp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dissect_sctp_header(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
int dpi_dissect_ipip(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);

int dpi_dissect_vrrp(struct flow_info* flow, int direction, const uint8_t* payload, const uint32_t payload_len, uint8_t flag);

int dissect_igmp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);

int dissect_cwmp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag);

int dissect_l2tp_ip(struct flow_info *flow, int direction, const uint8_t *payload, uint32_t payload_len, uint8_t flag);

/**
 *  对于多层ip/ipv6数据包，解析内层ip/ipv6，需继承外层链路层的信息
*/
static inline
void dpi_dissect_inherit_out_layer(struct flow_info *flow, struct pkt_info *pkt, SdtAclMatchedRuleInfo *acl)
{
    assert(flow->pkt);

    pkt->raw_pkt = flow->pkt->raw_pkt;
    pkt->pkt_len = flow->pkt->pkt_len;
    pkt->ethhdr  = flow->pkt->ethhdr;
    pkt->mbuf    = flow->pkt->mbuf;

    // 复制 acl hash 到acl, 内层解析时要用到
    acl->aclHashCnt = flow->pkt->aclHashCnt;
    memcpy(acl->aclHashCode, flow->pkt->aclHashCode, sizeof(acl->aclHashCode));
}

#endif
