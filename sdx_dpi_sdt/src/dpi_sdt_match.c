#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include <glib.h>

#include "tcp_rsm.h"
#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_tcp_reassemble.h"
#include "libsdt/libsdt_interface.h"
#include "libsdtacl/yasdtacl.h"
#include "sdt_ip_protocols.h"
#include "dpi_sdt_match.h"
#include "sdt_action_out.h"
#include "dpi_sdt_link.h"
#include "dpi_sdt_ipp.h"
#include "jhash.h"
#include "dpi_memory.h"
#include "dpi_pschema.h"
#include "dpi_flow.h"
#include "dpi_numa.h"

int cloneIndex = 0, freeIndex = 0;
extern struct rte_hash *g_sdt_hash_db;
extern struct rte_mempool *tbl_log_mempool;

extern rte_atomic64_t sdt_pcap_out_pkts;
extern rte_atomic64_t sdt_pcap_success_pkts;
extern rte_atomic64_t sdt_event_success_pkts;
extern rte_atomic64_t sdt_pcap_fail_pkts;
extern rte_atomic64_t sdt_event_fail_pkts;

struct rte_ring *app_match_ring[APP_PROTOCOL_RING_MAX_NUM];
int sdt_match_thfunc_signal = 0;

/**
 *  解析线程中也会进行匹配, 为避免产生竞争,
 *  MATCH_ENGINE_MAX_NUM = 解析线程数量 + 匹配线程数量
 */
SdxMatchProcessStatus  sdx_match_process_status[MATCH_ENGINE_MAX_NUM];

/** 更新命中统计信息 */
void sdx_match_status_add(int engine_id, int proto_id, int value)
{
    g_assert(engine_id < MATCH_ENGINE_MAX_NUM);
    g_assert(proto_id < PROTOCOL_MAX);

    sdx_match_process_status[engine_id].packet_on_rule_hit += value;
    sdx_match_process_status[engine_id].proto_hit_cnt[proto_id] += value;
}

/** 清除命中统计信息 */
void sdx_match_status_clean()
{
    int i;

    for (i=0; i<MATCH_ENGINE_MAX_NUM; i++)
    {
        memset(&sdx_match_process_status[i], 0, sizeof(SdxMatchProcessStatus));
    }
}

static int
_sdt_check_rule_exist(struct flow_info *flow, uint32_t id)
{
    uint32_t i;
    for(i=0;i<flow->sdt_flow.sdt_rule_cnt;i++){
        if(flow->sdt_flow.sdt_rules_rd[i]==id){
            return i;
        }
    }
    return -1;
}

static inline uint32_t gen_rule_hash_code(SdtMatchResult *match_result)
{
    struct user_group_key
    {
        char  uintID[64];
        char  taskID[64];
        char  groupID[64];
        uint32_t ruleID;
    } u_g_key;

    strncpy(u_g_key.uintID, match_result->unitID, sizeof(u_g_key.uintID));
    strncpy(u_g_key.taskID, match_result->taskID, sizeof(u_g_key.taskID));
    strncpy(u_g_key.groupID,match_result->groupID,sizeof(u_g_key.groupID));
    u_g_key.ruleID = match_result->ruleID;

    return jhash(&u_g_key, sizeof(u_g_key), JHASH_INITVAL);
}

static int
sdt_flush_cache_packets(struct flow_info *flow, struct list_head * stream_list,
                         uint8_t node_flag,
                         SdtMatchResult  *sdt_act,
                         sdt_out_status  *flow_rule_status,
                         uint32_t start_index)
{
    uint32_t free_space;
    int      ring_id;
    int      ret=0;
    struct packet_stream *pos= NULL;
    struct packet_stream *n  = NULL;
    uint32_t count=0;

    list_for_each_entry_safe(pos,n, stream_list, node)
    {
        sdt_out_packet_data_out(flow, pos, sdt_act, flow_rule_status);
    }

    if(flow->sdt_flow.sdt_rule_cnt==g_config.sdt_rule_max_num)
    {
        pkt_stream_free_node(&flow->sdt_flow.pkt_stream_head);
    }

    return 1;
}

static int
_sdt_handle_rule_statistics(struct flow_info            *flow,
                                        SdtMatchResult  *sdt_act,
                                        sdt_out_status  *flow_rule_status,
                                        uint16_t        proto_id,
                                        uint8_t         flag)
{
    if(SAE_none==sdt_act->action){
        return 0;
    }

    sdt_rule_perthread_pkt_statistics(flow,
                                      sdt_act,
                                      flow_rule_status,
                                      flow->thread_id,
                                      flag);

    return 1;
}


sdt_out_status* sdtflow_find_or_insert_ipff_outstatus(SdtFlowStatus *sdtflow, SdtMatchResult *result, uint8_t *first_flag)
{
    uint32_t rule_hash = gen_rule_hash_code(result);
    uint32_t rule_index = 0;
    sdt_out_status *out_status = NULL;

    for(rule_index=0; rule_index < sdtflow->ipff_rule_cnt; rule_index++)
    {
        if(sdtflow->ipff_rules_rd[rule_index] == rule_hash)
        {
            *first_flag = 0;
            return sdtflow->ipff_rules_status[rule_index];
        }
    }

    *first_flag = 1;

    out_status = sdt_rule_hash_db_lookup(result);
    out_status->flag = 1;

    sdtflow->ipff_rules_rd[rule_index] = rule_hash;
    sdtflow->ipff_rules_status[rule_index] = out_status;
    sdtflow->ipff_rule_cnt = rule_index + 1;

    return out_status;
}

/**
 * @brief 单包匹配 命中结果统计
 * 
 * @param result 匹配命中的结果
 * @param flow 用于统计命中多少流
 * @param pkt 用于统计命中报文数, 字节数
 */
void sdt_statistic_packet_match_result(SdtMatchResult *result, struct flow_info *flow, struct pkt_info *pkt)
{
    uint8_t first_flag;
    sdt_out_status *out_status;
    struct stu_rule_statistics *statistic;

    out_status = sdtflow_find_or_insert_ipff_outstatus(&flow->sdt_flow, result, &first_flag);

    statistic = &out_status->thread_statistics[flow->thread_id];

    if (statistic->rule_first_match_time <= 0)
        statistic->rule_first_match_time = g_config.g_now_time;
    if (first_flag)
        statistic->rule_match_flows++;
    statistic->rule_last_match_time = g_config.g_now_time;
    statistic->rule_match_hits++;
    statistic->rule_match_pkts++;
    statistic->rule_match_bytes += pkt->pkt_len;
}


/**
 * @brief 会话匹配 命中结果统计
 * 
 */
void sdt_statistic_session_match_result(SdtMatchResult *result, struct flow_info *flow, struct pkt_info *pkt)
{

}

/**
 * @brief 数据链路层(或其它无flow_info的协议)命中结果统计
 */
void sdt_statistic_datalink_match_result(SdtMatchResult *result, sdt_out_status *out_status, uint32_t thread_id, struct pkt_info *pkt)
{
    struct stu_rule_statistics *statistic;

    if (out_status == NULL)
        out_status = sdt_rule_hash_db_lookup(result);

    statistic = &out_status->thread_statistics[thread_id];

    if (statistic->rule_first_match_time <= 0)
        statistic->rule_first_match_time = g_config.g_now_time;
    statistic->rule_last_match_time = g_config.g_now_time;
    statistic->rule_match_hits++;
    statistic->rule_match_pkts++;
    statistic->rule_match_bytes += pkt->pkt_len;
}

static int
_sdt_handle_event_match(struct flow_info *flow,
                                  SdtMatchResult  *sdt_act,
                                  sdt_out_status  *flow_rule_status,
                                  int direction,
                                  uint16_t proto_id,
                                  uint8_t *sdt_flag)
{
    *sdt_flag = 1;
    sdt_event_handle(flow,sdt_act, flow_rule_status, direction);
    return 1;
}


/*处理缓存报文  action 为SAE_packetDump，SAE_event_and_packetDum 调用处理*/
static int
_sdt_handle_cache_packets(struct flow_info *flow,
                                 const struct pkt_info  *pkt,
                                 SdtMatchResult  *sdt_act,
                                 sdt_out_status  *flow_rule_status,
                                 int direction,
                                 uint16_t proto_id,
                                 uint8_t *sdt_flag)
{
    *sdt_flag=1;

    flow->direction                = direction;
    flow->sdt_flow.match_direction = direction;
    uint32_t           start_index = 0;


    switch(sdt_act->dumpOpt)
    {
    case SPO_out_pkt:
        sdt_in_pcap(sdt_act, pkt);
        *sdt_flag=0;  /* out_pkt单包模式，下次继续从这里刷出报文*/
        sdt_act->action= SAE_none;
        break;

    /*SPO_out_pkt_N 和 SPO_out_this 共用代码*/
    case SPO_out_pkt_N:
    case SPO_out_this:
        if(SPO_out_this==sdt_act->dumpOpt){
            start_index=0;
        }
        if(flow->sdt_flow.pkt_stream_num > g_config.sdt_cache_max_pkts){
            sdt_in_pcap(sdt_act, pkt);
        }
        break;

     /*SPO_out_front 和 SPO_default 共用代码*/
    case SPO_out_front:
        if(flow->sdt_flow.pkt_stream_num > (uint32_t)sdt_act->packetDump_args.count)
        {
            start_index=flow->sdt_flow.pkt_stream_num - sdt_act->packetDump_args.count;
        }
    case SPO_default:
        sdt_flush_cache_packets(flow, &flow->sdt_flow.pkt_stream_head, EM_LIST_NODE, sdt_act, flow_rule_status, start_index);
        break;
    default:
        break;
    }

    return 1;
}

int sdt_stream_rules_action_handle_hit
(int action, const struct pkt_info *pkt, struct flow_info *flow, int direction, SdtMatchResult *result, sdt_out_status *status)
{
    int ret = PKT_DROP;

    switch(action){
        case SAE_drop:
            return PKT_DROP;
        case SAE_packetDump:
            if (result->dumpOpt == SPO_default ) {
                sdt_in_pcap(result,  pkt);
            } else
            if (result->dumpOpt == SPO_out_this) {
                if (flow->sdt_flow.match_direction == direction){
                    sdt_in_pcap(result, pkt);
                }
            }else if (result->dumpOpt == SPO_out_pkt) {
            }
            ret = PKT_OK;
            break;
        case SAE_event:
            /*sdt_rule_perthread_flow_statistics(result,
              status,
              flow->thread_id,
              pkt->pkt_len);
              */
            ret = PKT_OK;
            break;
        case SAE_report:
            /*sdt_rule_perthread_flow_statistics(result,
              status,
              flow->thread_id,
              pkt->pkt_len);
              */
            ret=PKT_OK;
            break;
        default:
            break;
    }
    return ret;
}

int _sdt_match_rules_match_handle_hit(int action, const struct pkt_info *pkt, struct flow_info *flow, int direction, SdtMatchResult *result, sdt_out_status *status)
{
    struct packet_stream *pos=NULL;
    struct packet_stream *n  =NULL;
    uint8_t sdt_result_flag = 0;
    switch(action){
    case SAE_none:
        break;
    case SAE_drop:
        /*删掉缓存节点包*/
        return PKT_DROP;
    case SAE_event:
        /*event 输出*/
        _sdt_handle_event_match(flow,
                                result,
                                status,
                                flow->direction,
                                flow->real_protocol_id,
                                &sdt_result_flag);
        break;
    case SAE_report:
        list_for_each_entry_safe(pos, n, &flow->sdt_flow.pkt_stream_head, node)
        {
            if (list_empty(&flow->sdt_flow.pkt_stream_head))
                break;
            if(flow->sdt_flow.sdt_rule_cnt==g_config.sdt_match_max_rules-1)
            {
                if(!list_empty(&pos->node)){
                    list_del(&pos->node);
                    INIT_LIST_HEAD(&pos->node);
                }
                pktstream_free(pos);
            }
        }
        break;
    case SAE_packetDump:
        _sdt_handle_cache_packets(flow, pkt,
                                  result,
                                  status,
                                  flow->direction,
                                  flow->real_protocol_id,
                                  &sdt_result_flag);
        break;
    default:
        break;
    }

    if(result->action & SAE_alert)
    {
        //printf("##################### find alert -----------------------------------------0x%x-----\n", action);
        sdt_in_syslog(flow, result);
    }
    return 0;
}

static int
sdt_stream_rules_action_handle(struct flow_info        *flow,
                                      const struct pkt_info  *pkt,
                                      sdt_out_status   *rule_hash_status)
{
    if(!flow || !pkt || !rule_hash_status){
        return PKT_OK;
    }

    int ret=PKT_DROP;
    int  direction = flow->direction;
    SdtMatchResult   *match_result=rule_hash_status->match_result;

    //////////////////////////////////////////////////////////////////////////
    //////////////// 规则命中之后, FLOW后续报文不会再经过匹配引擎 ///////////////////
    //////////////////////////////////////////////////////////////////////////

    //handle emax/elimit/efreq stream 模式

    for(int idx = 0; idx < CHAR_BIT *(int)sizeof(match_result->action); idx++)
    {
        uint32_t action = 1 << idx;
        if(match_result->action & action)
        {
            ret = sdt_stream_rules_action_handle_hit(action, pkt, flow, direction, match_result, rule_hash_status);
            if(PKT_DROP == ret)
            {
                return PKT_DROP;
            }
        }
    }

    return ret;
}


/**
 * 对于已经命中规则后续的报文处理
 *
 * @flow
 *   流表
 * @pkt
 *   单包报文结构
 * @direction
 *   方向
*/
int _sdt_stream_action_process(struct flow_info *flow,const struct pkt_info  *pkt, int direction)
{
    if(pkt->ethhdr==NULL || pkt->pkt_len<=0){
        DPI_LOG(DPI_LOG_ERROR, "sdt_action_process pkt_len:%d",pkt->pkt_len);
        return 0;
    }
    int ret = PKT_DROP;
    uint32_t i   = 0;

    if(flow->sdt_flow.sdt_rule_cnt==0 ||
       flow->sdt_flow.sdt_rule_cnt<g_config.sdt_rule_max_num){
        ret=PKT_OK;
    }

    int local_ret=PKT_DROP;
    for(i=0;i<flow->sdt_flow.sdt_rule_cnt;i++){

        if(flow->sdt_flow.sdt_rules_rd[i]==0){
            break;
        }

        if(!flow->sdt_flow.sdt_rules_status[i]){
            break;
        }

        local_ret=sdt_stream_rules_action_handle(flow, pkt, flow->sdt_flow.sdt_rules_status[i]);
        /* 已经命中规则中有out_pkt和report则仍需继续往下走*/
        if(PKT_OK==local_ret){
            ret=PKT_OK;
        }
    }

    return ret;
}

static void
sdt_destroy_prec(ProtoRecord *pRec)
{
    if (pRec->record) {
        precord_destroy(pRec->record);
        pRec->record = NULL;
    }
}

static int
sdt_init_prec(const struct pkt_info  *pkt, ProtoRecord *pRec, int dir)
{
    char __str[64] = {0};

    pRec->proto_id=PROTOCOL_IP;
    if (pkt->proto == IPPROTO_TCP){
        pRec->ip_proto=IPPROTO_TCP;
        pRec->proto_id=PROTOCOL_TCP;
    } else if (pkt->proto == IPPROTO_UDP){
        pRec->ip_proto=IPPROTO_UDP;
        pRec->proto_id=PROTOCOL_UDP;
    }
    u8  *l2=(u8 *)pkt->ethhdr;
    u8  *l3=NULL;
    pRec->tuple.ipversion = pkt->ipversion;
    if(pkt->proto == IPPROTO_TCP){
        pRec->tuple.PortDst = pkt->tcph->dest;
        pRec->tuple.PortSrc = pkt->tcph->source;

    } else if(pkt->proto == IPPROTO_UDP){
        pRec->tuple.PortDst = pkt->udph->dest;
        pRec->tuple.PortSrc = pkt->udph->source;
    }else{
        pRec->tuple.PortDst = 0;
        pRec->tuple.PortSrc = 0;
    }
    if(pkt->ipversion == 4){
        l3=(u8 *)pkt->iph4;
        pRec->ip_len=ntohs(pkt->iph4->tot_len);

        pRec->tuple.IpDst.ipv4   = *(int*)pkt->iph4->daddr;
        pRec->tuple.IpSrc.ipv4   = *(int*)pkt->iph4->saddr;
    }else if(pkt->ipversion==6){
        l3=(u8 *)pkt->iph6;
        pRec->ip_len=ntohs((pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen));
        memcpy(pRec->tuple.IpDst.ipv6, (const char *)pkt->iph6->ip6_dst, sizeof(pRec->tuple.IpDst.ipv6));
        memcpy(pRec->tuple.IpSrc.ipv6, (const char *)pkt->iph6->ip6_src, sizeof(pRec->tuple.IpSrc.ipv6));
    }else{
        return PKT_DROP;
    }
    pRec->ip_version=pkt->ipversion;
    pRec->l3h_start=l3-l2;

    u8  *l4=NULL;
    if(pRec->ip_proto==IPPROTO_TCP){
        l4=(u8 *)pkt->tcph;
        pRec->tcp_flags=pkt->tcph->doff<<12|
                        pkt->tcph->res1<<8 |
                        pkt->tcph->cwr<<7  |
                        pkt->tcph->ece<<6  |
                        pkt->tcph->urg<<5  |
                        pkt->tcph->ack<<4  |
                        pkt->tcph->psh<<3  |
                        pkt->tcph->rst<<2  |
                        pkt->tcph->syn<<1  |
                        pkt->tcph->fin;
        pRec->tcp_flags&=0x0FFF;
        pRec->tcp_windowsize=ntohs(pkt->tcph->window);
        pRec->l4payload_start=(uint16_t)(l4-(uint8_t *)pkt->ethhdr+pkt->tcph->doff*4);
        pRec->seq=ntohl(pkt->tcph->seq);
        pRec->l4h_start=l4-l2;
    }else if(pRec->ip_proto==IPPROTO_UDP){
        l4=(u8 *)pkt->udph;
        pRec->l4payload_start=(uint16_t)(l4-(uint8_t *)pkt->ethhdr+sizeof(struct dpi_udphdr));
        pRec->l4h_start=l4-l2;
    }else{

    }

    pRec->pPayload.pBuff=(uint8_t *)pkt->raw_pkt;
    pRec->pPayload.len=pkt->pkt_len;
    pRec->direction = dir;
    pRec->record = NULL;

    return PKT_OK;
}

static int
_sdt_stream_data_move_block(struct stream_cache_st   *stream_cache, const uint8_t *payload, uint16_t  payload_len)
{
    if(NULL==payload || payload_len<=0){
        return -1;
    }

    if(NULL==stream_cache->buff){
        stream_cache->buff=(uint8_t *)dpi_malloc(g_config.sdt_block_buff_size*sizeof(uint8_t));
        if(NULL==stream_cache->buff){
            return -1;
        }
        stream_cache->buff_left = g_config.sdt_block_buff_size;
        stream_cache->buff_use  = 0;
    }

    if(stream_cache->buff_left<payload_len){
        if(stream_cache->buff_use>g_config.sdt_block_buff_keep){
            memmove(&stream_cache->buff[0],
                &stream_cache->buff[stream_cache->buff_use-g_config.sdt_block_buff_keep],
                g_config.sdt_block_buff_keep);
            stream_cache->buff_use=g_config.sdt_block_buff_keep;
            stream_cache->buff_left=g_config.sdt_block_buff_size-stream_cache->buff_use;
        }

        if(stream_cache->buff_left<payload_len){
            memmove(&stream_cache->buff[stream_cache->buff_use],
                    payload,
                    stream_cache->buff_left);
            stream_cache->buff_use  += stream_cache->buff_left;
            stream_cache->buff_left  = 0;
            return 0;
        }
    }

    memmove(&stream_cache->buff[stream_cache->buff_use],
            payload,
            payload_len);
    stream_cache->buff_use  += payload_len;
    stream_cache->buff_left -= payload_len;

    return 0;
}

static int
_sdt_tcp_flow_finish(struct flow_info *flow)
{
    struct tcp_rsm *rsm = flow->sdt_flow.sdt_rsm;
    if(rsm){
        const struct tcp_status *status;
        status = tcp_rsm_status(rsm, 0);
        uint32_t num = status->fail;
        status = tcp_rsm_status(rsm, 1);
        num += status->fail;
        if(num){
          //  rte_atomic64_add(&rsm_fail_get, num);
        }

        tcp_rsm_free(rsm);
        flow->sdt_flow.sdt_rsm = NULL;
    }

    return 0;
}

static void
_sdt_match_rules_match_handle(struct flow_info       *flow,
                                      const struct pkt_info  *pkt,
                                      ProtoRecord      *pRec,
                                      sdt_out_status   *rule_hash_status,
                                      SdtMatchResult   *match_result)
{
    if(!flow || !pRec){
        return;
    }

    int ret = PKT_DROP;
    for(int idx = 0; idx < CHAR_BIT *(int)sizeof(match_result->action); idx++)
    {
        uint32_t action = 1 << idx;
        if(match_result->action & action)
        {
            ret = _sdt_match_rules_match_handle_hit(action, pkt, flow, flow->direction, match_result, rule_hash_status);
            if(PKT_DROP == ret)
            {
                return;
            }
        }
    }

    return;
}

//add by huangzw 20221130
static sdt_out_status *get_group_name_status_by_rule_key(struct flow_info *flow, SdtMatchResult *match_result, struct tbl_log  *log, struct tbl_log  **log_new, bool copy_log_flag)
{
    if(!flow || !match_result || !log)
        return NULL;

    sdt_out_status  *lookup_result=NULL;
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d", match_result->unitID, match_result->taskID, match_result->groupID, match_result->ruleID);
    int pos = rte_hash_lookup_data(g_sdt_hash_db, (const void *)rule_key, (void **)&lookup_result);
    if(pos>=0)
    {
        //JSON 规则添加时, g_sdt_hash_db 内的groupName还没有得到初始化
        lookup_result->flag=1;
    }
    else
    //if not in hash table
    {
        sdt_out_status  *rule_elem=NULL;
        rule_elem=(sdt_out_status  *)malloc(sizeof(sdt_out_status));
        if(!rule_elem)
        {
            DPI_LOG(DPI_LOG_ERROR, "rte_hash_add_key_data %s malloc failed!",
                    rule_key)
            return NULL;
        }

        memset(rule_elem, 0, sizeof(sdt_out_status));
        sdt_init_rules_to_hash_db(match_result, rule_elem);
        rule_elem->flag      = 1;

        int retval = rte_hash_add_key_data(g_sdt_hash_db, (const void *)rule_key, (void *)rule_elem);
        if(retval<0)
        {
            DPI_LOG(DPI_LOG_ERROR, "rte_hash_add_key_data %s insert hash db failed! hash table numbers:%d",
                    rule_key,
                    rte_hash_count(g_sdt_hash_db));
            free(rule_elem);
            rule_elem = NULL;
            return NULL;
        }

        lookup_result = rule_elem;
    }

    return lookup_result;
}

static int
flow_sdt_match_rule(struct flow_info *flow, const struct pkt_info *pkt, int direction,
        ProtoRecord *pRec, SdtMatchResult **match_result, int rule_nums, uint8_t (*cb_write_proto_log)(void *log), void *log, uint8_t *enqueue_flag)
{
    struct mac_packet_header _mac;
    sdt_out_status *o_stat;
    struct tbl_log *log_ptr = (struct tbl_log *)log;
    SdtMatchResult *res = NULL;
    if (log) {
        memcpy(&_mac, &((struct tbl_log *)log)->mac_hdr, sizeof(struct mac_packet_header));
    }

    int i,mi;
    uint32_t  uid_gid_hashcode;
    for(i=0; i<rule_nums;i++)
    {
        res = match_result[i];

        ///////// 更新被命中的规则 //////////////
        SdtMatchResult *ruleAction = match_result[i];
        if(NULL == flow->ghash_link_action)
        {
            flow->ghash_link_action = g_hash_table_new(g_int64_hash, g_int64_equal);
        }
        g_hash_table_insert(flow->ghash_link_action, ruleAction, ruleAction);
        /////////////////////////////////////////

        struct tbl_log *log_new_ptr=NULL;
        bool  copy_log_flag = 0;
        if(i>0) copy_log_flag = 1;
        o_stat = get_group_name_status_by_rule_key(flow, res, (struct tbl_log *)log, &log_new_ptr, copy_log_flag);
        if(o_stat == NULL)
        {
            DPI_LOG(DPI_LOG_ERROR, "WARN: get_group_name_status_by_rule_key code:%p %s:%u", o_stat, __FILE__, __LINE__);
            continue;
        }

        //产生用户规则组唯一识别的hashcode
        uid_gid_hashcode = gen_rule_hash_code(res);
        mi=_sdt_check_rule_exist(flow, uid_gid_hashcode);
        if(mi>=0){
            if(SAE_report & res->action)
            {
                if(cb_write_proto_log){
                    log_ptr->match_info[log_ptr->match_res_cnt] = o_stat;
                    log_ptr->add_task[log_ptr->match_res_cnt] = true;
                    log_ptr->match_res_cnt++;
                    //用于327实体文件是否删除；
                    *enqueue_flag = 1;
                }
            }
            _sdt_handle_rule_statistics(flow, res, flow->sdt_flow.sdt_rules_status[mi], pRec->proto_id, 0);
            if(SPO_out_pkt==res->dumpOpt)
            {
                sdt_in_pcap(res, pkt);
            }
            continue;
        }

        /* 规则第一次命中处理 */
        if(0==g_config.sdt_record_match_rule
                || flow->sdt_flow.sdt_rule_cnt>=g_config.sdt_match_max_rules)
        {   /* 不记录已命中规则模式 或 记录规则数已超过上限 */
            sdt_out_status   *temp_sdt_rules_status=sdt_rule_hash_db_lookup(res);
            if(temp_sdt_rules_status){
                temp_sdt_rules_status->flag=1;
            }

            if(SAE_report & res->action)
            {
                if(cb_write_proto_log){
                    log_ptr->match_info[log_ptr->match_res_cnt] = o_stat;
                    log_ptr->add_task[log_ptr->match_res_cnt] = false;
                    log_ptr->match_res_cnt++;
                    //用于327实体文件是否删除；
                    *enqueue_flag = 1;
                }
            }
            _sdt_handle_rule_statistics(flow, res, temp_sdt_rules_status, pRec->proto_id, 1);
            memcpy(&temp_sdt_rules_status->mac_hdr, &_mac, sizeof(struct mac_packet_header));
            _sdt_match_rules_match_handle(flow, pkt, pRec, temp_sdt_rules_status, res);
        }else{/* 记录已命中规则模式 */
            /* 以下为规则第一次命中 */

            flow->sdt_flow.sdt_rules_rd[flow->sdt_flow.sdt_rule_cnt] =
                uid_gid_hashcode;
            flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]= o_stat;
            if(o_stat){
                o_stat->flag=1;
            }
            if(SAE_report & res->action)
            {
                if(cb_write_proto_log){
                    log_ptr->match_info[log_ptr->match_res_cnt] = o_stat;
                    log_ptr->add_task[log_ptr->match_res_cnt] = true;
                    log_ptr->match_res_cnt++;
                    //用于327实体文件是否删除；
                    *enqueue_flag = 1;
                }
            }
            _sdt_handle_rule_statistics(flow, res, o_stat, pRec->proto_id, 1);
            memcpy(&o_stat->mac_hdr, &_mac, sizeof(struct mac_packet_header));
            _sdt_match_rules_match_handle(flow, pkt, pRec, o_stat, res);
            flow->sdt_flow.sdt_rule_cnt++;
        }
    }

    return PKT_OK;
}

static void _match_action_report(struct flow_info *info, fun_write_trans_log write_log, void *field_info, sdt_out_status *stat, ProtoRecord *prec, struct tbl_log *tbl)
{
    int direction = prec->direction;
    if (tbl->match_res_cnt <= 0 ) {
        write_log(info, field_info, prec, tbl);
    }
    tbl->match_info[tbl->match_res_cnt] = stat;
    tbl->match_res_cnt += 1;
}

int
dpi_sdt_engine_proto_match_rules_with_more_acl(struct flow_info *flow,
                                    const struct pkt_info  *pkt,
                                    SdtEngine              *app_engine,
                                    ProtoRecord            *pRec,
                                    uint32_t                hash,
                                    uint8_t (*cb_write_proto_log)(void *log),
                                    void *log,
                                    uint8_t *enqueue_flag)
{
    SdtMatchResult**match_result    = NULL;
    SdtMatchResult *hit_result      = NULL;
    int            ret              = 0;


    //能够走到这里的流程, 只有这两种可能
    uint32_t rule_type_arr[] = {LINK_ACL_KEYWORD0_RULEBODY1, LINK_ACL_KEYWORD1_RULEBODY1};
    for(int i = 0; i < (int)array_length(rule_type_arr); i++)
    {
        int            rule_nums        = 0;
        match_result = sdtEngine_matchSdtRules_v2(app_engine, rule_type_arr[i], hash, pRec, &rule_nums);
        if(match_result && rule_nums>0){
            sdx_match_status_add(flow->thread_id, pRec->proto_id, 1);
            ret=flow_sdt_match_rule(flow, pkt, flow->direction, pRec, match_result,rule_nums, cb_write_proto_log,log, enqueue_flag);
        }else{
            return PKT_OK;
        }
    }
    return ret;
}


  int
  dpi_sdt_engine_proto_match_rules(struct flow_info        *flow,
                                    const struct pkt_info  *pkt,
                                    SdxMatchThreadCtx      *match_ctx,
                                    ProtoRecord            *pRec,
                                    uint8_t (*cb_write_proto_log)(void *log),
                                    void *log,
                                    uint8_t *enqueue_flag)
  {
      if(!flow){
        return PKT_DROP;
      }

      if(unlikely(1==g_config.stop_rcv_pkts)){
          return PKT_DROP;
      }

      int ret = PKT_OK;
      SdtEngine *app_engine = match_ctx->app_engine;

      /* 该流已经匹配到最大可匹配的规则数，后续不在进行规则匹配*/
      if(flow->sdt_flow.sdt_rule_cnt>=g_config.sdt_match_max_rules){
          /* 已经命中规则中有report或者out_pkt(单包模式) 任然需要继续进行规则匹配 */
          if(!sdt_check_rule_exist_report(flow))
          {
              return PKT_DROP;
          }
      }

      //ACL 多模
      for(int j = 0; j < ACLHASHHITMAX; j++)
      {
          uint32_t hash = flow->aclHashHit[flow->direction][j];
          if(hash)
          {
              ret |= dpi_sdt_engine_proto_match_rules_with_more_acl(flow, pkt, app_engine, pRec, hash, cb_write_proto_log, log, enqueue_flag);
          }
      }

    if (((struct tbl_log *)log)->match_res_cnt > 0) {
        cb_write_proto_log(log);
    }

      return ret;
  }



int  dpi_multi_mode_trans_match(const struct pkt_info *pkt, ProtoRecord *pRec, fun_write_trans_log write_log)
{
  struct flow_info *flow = pkt->flow;
  SdtMatchResult *match_result = NULL;
  int             rule_nums    = 0;
  int             ret          = 0;
  int direction = pRec->direction;

  uint32_t rule_hash = 0;
  int      rule_idx = -1;
  SdtMatchResult *res = NULL;
  sdt_out_status *o_stat = NULL;

  //ACL 多模
  for(int hash_idx = 0; hash_idx < (int)pkt->aclHashCnt; hash_idx++)
  {
      uint32_t hash = pkt->aclHashCode[hash_idx];
      int rule_type_array[] = {IPFF_ACL_KEYWORD0_RULEBODY1, IPFF_ACL_KEYWORD1_RULEBODY1};
      int rule_type_size = (int)array_length(rule_type_array);
      //规则的类别
      for(int rule_type_index = 0; rule_type_index < rule_type_size; rule_type_index++)
      {
          int rule_nums = 0;
          int rule_type = rule_type_array[rule_type_index];
          SdtMatchResult**match_result = sdtEngine_matchSdtRules_v2(flow->workflow->pEngine, rule_type, hash, pRec, &rule_nums);
          if (match_result && rule_nums > 0)
          {
            sdx_match_status_add(flow->thread_id, pRec->proto_id, 1);
          }
      }
  }


//  for (int i = 0; i < rule_nums; i++) {
//    res = match_result + i;
//
//    _dpi_match_single_pkt(flow, res, pkt);
//
//    // handle emax/elimit/efreq
//    if (ACTION_DENIED == sdt_action_options_approve(res)) {
//      continue;
//    }
//
//    if (flow->sdt_flow.sdt_rule_cnt < g_config.sdt_match_max_rules) {
//      rule_hash = gen_rule_hash_code(res);
//      rule_idx  = _sdt_check_rule_exist(flow, rule_hash);
//    }
//
//    if (rule_idx >= 0) {
//      o_stat = flow->sdt_flow.sdt_rules_status[rule_idx];
//    } else {
//      o_stat = sdt_rule_hash_db_lookup(res);
//      if (o_stat) {
//        o_stat->flag = 1;
//      }
//    }
//
//    if (flow->sdt_flow.sdt_rule_cnt == g_config.sdt_match_max_rules || rule_idx >= 0) {
//      if (SAE_report & res->action) {
//        _match_action_report(flow, write_log, field_info, o_stat, pRec, tbl);
//      }
//      /* 对于无body体的规则只在IPP\TCP\UDP层次有输出，其他层次一律不能有输出*/
//      if (0 == match_result[i].matchHintFlag && PROTOCOL_IP != pRec->proto_id && PROTOCOL_TCP != pRec->proto_id &&
//          PROTOCOL_UDP != pRec->proto_id) {
//        continue;
//      }
//      _sdt_handle_rule_statistics(flow, match_result, o_stat, pRec->proto_id, 0);
//      if (SPO_out_pkt == match_result[i].dumpOpt) {
//        sdt_in_pcap(flow, &match_result[i], flow->sdt_flow.sdt_rules_status[rule_idx], pkt);
//      }
//      continue;
//    }
//
//    /* 规则第一次命中处理 */
//    /* 不记录已命中规则模式 或 记录规则数已超过上限 */
//    if (0 == g_config.sdt_record_match_rule || flow->sdt_flow.sdt_rule_cnt >= g_config.sdt_match_max_rules) {
//
//      if (SAE_report & res->action) {
//        _match_action_report(flow, write_log, field_info, o_stat, pRec, tbl);
//      }
//      /* 对于无body体的规则只在IPP\TCP\UDP层次有输出，其他层次一律不能有输出*/
//      if (0 == match_result[i].matchHintFlag && PROTOCOL_IP != pRec->proto_id && PROTOCOL_TCP != pRec->proto_id &&
//          PROTOCOL_UDP != pRec->proto_id) {
//        continue;
//      }
//      _sdt_handle_rule_statistics(flow, &match_result[i], o_stat, pRec->proto_id, 1);
//
//      if (g_config.sdt_mac_forward_flag && flow->pSDTMacHeader[direction]) {
//        memcpy(&o_stat->mac_hdr, flow->pSDTMacHeader[direction], sizeof(struct mac_packet_header));
//      }
//      _sdt_match_rules_match_handle(flow, pkt, pRec, o_stat, &match_result[i]);
//    } else {
//      /* 记录已命中规则模式 */
//      /* 以下为规则第一次命中 */
//      flow->sdt_flow.sdt_rules_rd[flow->sdt_flow.sdt_rule_cnt]     = rule_hash;
//      flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt] = sdt_rule_hash_db_lookup(&match_result[i]);
//      if (flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]) {
//        flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]->flag = 1;
//      }
//      if (SAE_report & res->action) {
//        _match_action_report(flow, write_log, field_info, flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt], pRec, tbl);
//      }
//
//      /* 对于无body体的规则只在IPP\TCP\UDP层次有输出，其他层次一律不能有输出*/
//      if (0 == match_result[i].matchHintFlag && PROTOCOL_IP != pRec->proto_id && PROTOCOL_TCP != pRec->proto_id &&
//          PROTOCOL_UDP != pRec->proto_id) {
//        continue;
//      }
//      _sdt_handle_rule_statistics(flow, &match_result[i], flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt],
//                                  pRec->proto_id, 1);
//
//      if (g_config.sdt_mac_forward_flag && flow->pSDTMacHeader[direction]) {
//        memcpy(&flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt]->mac_hdr, flow->pSDTMacHeader[direction],
//               sizeof(struct mac_packet_header));
//      }
//      _sdt_match_rules_match_handle(flow, pkt, pRec, flow->sdt_flow.sdt_rules_status[flow->sdt_flow.sdt_rule_cnt],
//                                    &match_result[i]);
//      flow->sdt_flow.sdt_rule_cnt++;
//    }
//  }
//  // pthread_mutex_unlock(&flow->mutex);
  return ret;
}


int
dpi_sdt_flow_link_match(struct flow_info *flow)
{
    /* sdt ip通联监测要素 在超时时做规则匹配 */
    if(likely(0==g_config.stop_rcv_pkts)){

        if(IPPROTO_TCP==flow->tuple.inner.proto){
            _sdt_tcp_flow_finish(flow);
        }
    }

    if(flow->msg){
        free(flow->msg);
        flow->msg=NULL;
    }


    return 0;
}

int
dpi_sdt_flow_timeout(struct flow_info *flow)
{
    pkt_stream_free_node(&flow->sdt_flow.pkt_stream_head);

    INIT_LIST_HEAD(&flow->sdt_flow.pkt_stream_head);

    int i=0;
    for(i=0;i<PKT_STREAM_DIR_NUM;i++){
        if(flow->sdt_flow.sdt_ip_cache[i].buff){
            free(flow->sdt_flow.sdt_ip_cache[i].buff);
            flow->sdt_flow.sdt_ip_cache[i].buff=NULL;
        }

        if(flow->sdt_flow.sdt_l4stream_cache[i].buff){
            free(flow->sdt_flow.sdt_l4stream_cache[i].buff);
            flow->sdt_flow.sdt_l4stream_cache[i].buff=NULL;
        }
    }

    return 0;
}


static int app_match_rule(SdxMatchThreadCtx *match_ctx, struct tbl_log  *tbl, struct flow_info *flow)
{
    char __str[256];
    uint8_t enqueue_flag=0;
    int32_t precord_field_num = 0;

    ProtoRecord pRec;
    memset(&pRec, 0, sizeof(ProtoRecord));

    precord_t *record = tbl->record;
    pRec.record = tbl->record;
    pRec.flow   = flow;
    int proto_id = tbl->proto_id;

    pRec.proto_id        = tbl->proto_id;
    pRec.direction       = tbl->flow->direction;

    pRec.tuple.ipversion = tbl->flow->tuple.inner.ip_version;
    if (tbl->flow->tuple.inner.ip_version == 4) {
        get_iparray_to_string(__str, sizeof(__str), tbl->flow->tuple.inner.ip_dst);
        pRec.tuple.IpDst.ipv4   = (int)inet_addr((const char *)__str);
        get_iparray_to_string(__str, sizeof(__str), tbl->flow->tuple.inner.ip_src);
        pRec.tuple.IpSrc.ipv4   = (int)inet_addr((const char *)__str);
    } else if (tbl->flow->tuple.inner.ip_version == 6) {
        memcpy(pRec.tuple.IpDst.ipv6, (const char *)tbl->flow->tuple.inner.ip_dst, sizeof(pRec.tuple.IpDst.ipv6));
        memcpy(pRec.tuple.IpSrc.ipv6, (const char *)tbl->flow->tuple.inner.ip_src,  sizeof(pRec.tuple.IpSrc.ipv6));
    }

    pRec.tuple.PortDst = tbl->flow->tuple.inner.port_dst;
    pRec.tuple.PortSrc = tbl->flow->tuple.inner.port_src;
    strncpy(pRec.proto_name, protocol_name_array[tbl->proto_id], strlen(protocol_name_array[tbl->proto_id]));

#ifdef DPI_FUTURE_MBUF
    pRec.pkt = &tbl->pkt;
    if (tbl->pkt.mbuf)
    {
        pRec.pPayload.pBuff = rte_pktmbuf_mtod(tbl->pkt.mbuf, uint8_t*);
        pRec.pPayload.len = rte_pktmbuf_data_len(tbl->pkt.mbuf);
    }
#else
    struct pkt_info  pkt;
    memset(&pkt, 0, sizeof(struct pkt_info));
    pkt.pkt_len = tbl->pkt_data_len;
    pkt.raw_pkt = tbl->pkt_data;
    pkt.ethhdr = (const struct dpi_ethhdr *)tbl->pkt_data;

    pRec.pkt = &pkt;
    pRec.pPayload.pBuff=(uint8_t *)pkt.raw_pkt;
    pRec.pPayload.len=pkt.pkt_len;
#endif

    pRec.match_data_len = tbl->match_data_len;

    dpi_sdt_engine_proto_match_rules(flow, pRec.pkt, match_ctx, &pRec, dpi_app_match_res_enqueue, (void *)tbl, &enqueue_flag);

#ifdef DPI_SDT_ZDY
    //删除未匹配记录的实体文件
    if(!enqueue_flag)
        del_not_match_tbl_file(tbl->record, tbl->proto_id);
#endif
    return 0;
}

static uint8_t   sdt_match_running = 0;
static pthread_t sdt_match_pthread[MAX_THREAD_NUM] = {0};
/*
* 应用协议解析结果进行规则匹配线程
*/
static void *app_fields_sdt_match_func(void * arg)
{
    SdxMatchThreadCtx *match_ctx = (SdxMatchThreadCtx*)arg;

    int  index;
    int  dequeue_num =0;
    long ring_id = match_ctx->ring_id;
    struct tbl_log  *tbl;
    struct tbl_log  *tbl_burst[TBL_MAX_BURST];
    uint8_t core_id = 0;
    pthread_t thread = pthread_self();
    char thread_name[16];
    int ret = 0;

    core_id = match_ctx->core_id;

    snprintf(thread_name, sizeof(thread_name), "dpi_match_%d", core_id);
    pthread_setname_np(thread, thread_name);

    ret = dpi_pthread_setaffinity(thread, core_id);
    if (!ret) {
        DPI_LOG(DPI_LOG_ERROR, "App fields sdt match thread failed to bind core_id!!!");
    }

    log_info("App fields sdt match thread on core %d", core_id);

    match_ctx->app_engine = sdtEngine_init(match_ctx->engine_id, g_config.sdt_switch_mult_hit);
//    assert(app_engine);

    ATOMIC_ADD_FETCH(&sdt_match_thfunc_signal);
    // DPI_LOG(DPI_LOG_DEBUG, "Running app fields sdt match thread on core %d", core_affinity);

    while (1) {
        // 线程 结束信号
        if(unlikely(sdt_match_running == 0)) {
            if (rte_ring_empty(app_match_ring[ring_id]))
                break;
        }

        // 线程 暂停信号
        if (unlikely(g_sdt_hash_db_clear_flag))
        {
            // 规则即将重置, 匹配线程暂停, 等新规则到来再继续匹配
            ATOMIC_SUB_FETCH(&sdt_match_thfunc_signal);
            while(g_sdt_hash_db_clear_flag)
            {
                usleep(1000*100);
            }
            ATOMIC_ADD_FETCH(&sdt_match_thfunc_signal);
        }

        dequeue_num = rte_ring_sc_dequeue_burst(app_match_ring[ring_id],
                                               (void **)tbl_burst, TBL_MAX_BURST, NULL);
        if (dequeue_num <= 0) {
            usleep(1000*100);
            continue;
        }

        for (index = 0; index < dequeue_num; index++) {
            tbl = tbl_burst[index];
            struct flow_info *flow=tbl->flow;

            if(unlikely(NULL==flow)){
                DPI_LOG(DPI_LOG_ERROR, "find tbl->flow is NULL proto[%s]",protocol_name_array[tbl->proto_id]);
                dpi_tbl_free(tbl);
                continue;
            }
            //规则匹配
            app_match_rule(match_ctx, tbl, flow);

            dpi_tbl_free(tbl);
        }
    }

    sdtEngine_fini(match_ctx->app_engine);
    match_ctx->app_engine = NULL;
    dpi_free(match_ctx);
    ATOMIC_SUB_FETCH(&sdt_match_thfunc_signal);

    return NULL;
}


int init_app_match_ring(void)
{
    for (int i = 0; i < g_config.app_match_thread_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "app_match_ring_%d_%d",g_config.socketid, i);
        app_match_ring[i] = rte_ring_create(ring_name, g_config.tbl_ring_size, g_config.socketid, RING_F_SC_DEQ);
        if (app_match_ring[i] == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "error while create app match ring\n");
            exit(-1);
        }
        if (rte_ring_lookup(ring_name) != app_match_ring[i]) {
            DPI_LOG(DPI_LOG_ERROR, "Cannot lookup app match ring from its name\n");
            exit(-1);
        }
    }
    return 0;
}


int sdt_match_start(void)
{
    int status, i;

    sdt_match_running = 1;

    for(i=0; i < g_config.app_match_thread_num; i++)
    {
        SdxMatchThreadCtx   *match_ctx = dpi_malloc(sizeof(SdxMatchThreadCtx));
        match_ctx->ring_id   = i;
        match_ctx->engine_id = g_config.dissector_thread_num + i;

        if (g_config.app_match_core_id[i] != 0) {
            match_ctx->core_id = g_config.app_match_core_id[i];
        } else {
            match_ctx->core_id = dpi_numa_get_suitable_core(g_config.socketid);
        }

        status = pthread_create(&sdt_match_pthread[i], NULL, app_fields_sdt_match_func, (void*)match_ctx);
        if(status != 0) {
            DPI_SYS_LOG(DPI_LOG_ERROR, "error on create app protocol sdt match thread");
            exit(-1);
        }
        dpi_numa_set_used_core(match_ctx->core_id);
    }

    return 0;
}

void sdt_match_stop()
{
    int i;
    sdt_match_running = 0;

    for(i=0; i < g_config.app_match_thread_num; i++)
    {
        pthread_join(sdt_match_pthread[i], NULL);
    }

    log_trace("匹配线程退出");
}
