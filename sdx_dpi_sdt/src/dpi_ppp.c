/****************************************************************************************
 * 文 件 名 : dpi_ppp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *设计: wangy      2018/07/06
 *编码: wangy      2018/07/06
 *修改: wangch     2020/01/01
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <rte_mbuf.h>
#include "dpi_tbl_log.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"

extern struct rte_mempool *tbl_log_mempool;
enum lcp_innex_em {
	EM_LCP_BASE,
	EM_LCP_TYPE,
	EM_LCP_ID,
	EM_LCP_MRU,
	EM_LCP_AUTH_PROTO,
	EM_LCP_MAGIC,
	EM_LCP_PROTO_COMP,
	EM_LCP_ADDR_CTRL_COMP,
	EM_LCP_CALLBACK,
	EM_LCP_ALGORITHM,
	EM_LCP_MESSAGE,
	EM_LCP_MAX,
};

static dpi_field_table lcp_field_array[] = {
	DPI_FIELD_D(EM_LCP_BASE,           EM_F_TYPE_NULL, "base"),
	DPI_FIELD_D(EM_LCP_TYPE,           EM_F_TYPE_NULL, "type"),
	DPI_FIELD_D(EM_LCP_ID,             YA_FT_STRING,   "id"),
	DPI_FIELD_D(EM_LCP_AUTH_PROTO,     EM_F_TYPE_NULL, "auth_proto"),
	DPI_FIELD_D(EM_LCP_ALGORITHM,      EM_F_TYPE_NULL, "algorithm"),
	DPI_FIELD_D(EM_LCP_MRU,            EM_F_TYPE_NULL, "mru"),
	DPI_FIELD_D(EM_LCP_MAGIC,          EM_F_TYPE_NULL, "magic_number"),
	DPI_FIELD_D(EM_LCP_PROTO_COMP,     EM_F_TYPE_NULL, "proto_comp"),
	DPI_FIELD_D(EM_LCP_ADDR_CTRL_COMP, EM_F_TYPE_NULL, "addr_ctrl_comp"),
	DPI_FIELD_D(EM_LCP_CALLBACK,       EM_F_TYPE_NULL, "callback"),
	DPI_FIELD_D(EM_LCP_MESSAGE,        EM_F_TYPE_NULL, "message"),
};

enum pap_index_em {
	EM_PAP_BASE,
	EM_PAP_TYPE,
	EM_PAP_ID,
	EM_PAP_IDENTIFIER,
	EM_PAP_PASSWORD,
	EM_PAP_LOGIN_STATUS,
	EM_PAP_MAX,
};

static dpi_field_table pap_field_array[] = {
	DPI_FIELD_D(EM_PAP_BASE,          EM_F_TYPE_NULL, "base"),
	DPI_FIELD_D(EM_PAP_TYPE,          EM_F_TYPE_NULL, "type"),
	DPI_FIELD_D(EM_PAP_ID,            EM_F_TYPE_NULL, "id"),
	DPI_FIELD_D(EM_PAP_IDENTIFIER,    EM_F_TYPE_NULL, "identifier"),
	DPI_FIELD_D(EM_PAP_PASSWORD,      EM_F_TYPE_NULL, "password"),
	DPI_FIELD_D(EM_PAP_LOGIN_STATUS,  EM_F_TYPE_NULL, "LoginStatus"),

};

enum chap_index_em {
	EM_CHAP_BASE,
	EM_CHAP_TYPE,
	EM_CHAP_ID,
	EM_CHAP_RANDOM,
	EM_CHAP_NAME,
	EM_CHAP_MESSAGE,
	EM_CHAP_LOGIN_STATUS,
	EM_CHAP_MAX,
};

static dpi_field_table chap_field_array[] = {
	DPI_FIELD_D(EM_CHAP_BASE,         EM_F_TYPE_NULL, "base"),
	DPI_FIELD_D(EM_CHAP_TYPE,         EM_F_TYPE_NULL, "type"),
	DPI_FIELD_D(EM_CHAP_ID,           EM_F_TYPE_NULL, "id"),
	DPI_FIELD_D(EM_CHAP_RANDOM,       EM_F_TYPE_NULL, "random"),
	DPI_FIELD_D(EM_CHAP_NAME,         EM_F_TYPE_NULL, "name"),
	DPI_FIELD_D(EM_CHAP_MESSAGE,      EM_F_TYPE_NULL, "message"),
	DPI_FIELD_D(EM_CHAP_LOGIN_STATUS, EM_F_TYPE_NULL, "LoginStatus"),
};

struct value_string{
	int value;
	const char *string;
};

static struct value_string lcp_type[]={
	{0, NULL},
	{1, "configue-request"},
	{2, "configue-ACK"},
	{3, "configue-NACK"},
	{4, "configue-reject"},
	{5, "terminate-request"},
	{6, "terminate-ACK"},
	{7, "code-reject"},
	{8, "protocol-reject"},
	{9, "echo-request"},
	{10, "echo-replay"},
	{11, "discard-request"},
	{12, "identification"},
	{13, "time_remaining"}
};

struct lcp_info{
	uint16_t length;
	uint8_t type;
	uint8_t id;
	uint16_t mru;
	uint16_t auth_proto;
	uint8_t algorithm;
	const uint8_t *magic;
	uint8_t proto_comp;
	uint8_t addr_ctrl_comp;
	uint8_t callback;
	const uint8_t *message;
	uint16_t message_len;
};

void write_lcp_log(struct flow_info *flow, int direction, struct lcp_info *info, int proto_type);
void write_lcp_log(struct flow_info *flow, int direction, struct lcp_info *info, int proto_type)
{
	int i, idx = 0;
	struct tbl_log *log_ptr;

	if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
		DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
		return;
	}
    init_log_ptr_data(log_ptr, flow,PROTOCOL_LCP);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
	write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "lcp");

	if(proto_type == PROTOCOL_L2TP)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "l2tp", 4);
	else if(proto_type == PROTOCOL_GRE)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "gre", 3);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(info->type && info->type < sizeof(lcp_type) / sizeof(lcp_type[0]))
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, lcp_type[info->type].string, strlen(lcp_type[info->type].string));
	else
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "Unknown", 7);

	write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->id);

	if(info->auth_proto == PPP_CHAP)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "CHAP", 4);
	else if(info->auth_proto == PPP_PAP)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "PAP", 3);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(info->algorithm == 5)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "CHAP with MD5", 13);
	else if(info->algorithm == 6)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "CHAP with SHA1", 14);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(info->mru)
		write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->mru);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(info->magic && (*((const int*)info->magic)))
		write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->magic, 4);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(info->proto_comp)
		write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(info->addr_ctrl_comp)
		write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->message, info->message_len);

	if(info->callback)
		write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->callback);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);


	log_ptr->log_type = TBL_LOG_LCP;
	log_ptr->log_len  = idx;
  log_ptr->proto_id = PROTOCOL_LCP;

	if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
		rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
	return;

}

void dissect_lcp(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type);
void dissect_lcp(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type)
{
	int i, offset = 0;
	int direction = 1;
	struct lcp_info info;
	bzero(&info, sizeof(struct lcp_info));

	if(payload_len < 4)
		return;

	info.type = payload[0];
	info.id   = payload[1];
	info.length = get_uint16_ntohs(payload, 2);
	offset += 4;

	if(info.length > payload_len)
		return;

	//类型参考lcp_type;
	switch(info.type){
	case 9: case 10: case 12:
		info.magic       = payload + 4;
		if(info.length > 8){
			info.message     = payload + 8;
			info.message_len = info.length - 8;
		}
		break;
	case 1: case 2: case 3: case 4:
		//option
		while(offset < info.length){
			switch(payload[offset]){
			case 1:
				if(payload[offset+1] == 4)
					info.mru = get_uint16_ntohs(payload, offset+2);
				break;
			case 3:
				if(payload[offset+1] == 4)
					info.auth_proto = get_uint16_ntohs(payload, offset+2);
				else if(payload[offset+1] == 5){
					info.auth_proto = get_uint16_ntohs(payload, offset+2);
					info.algorithm = payload[offset+4];
				}
				break;
			case 5:
				info.magic     = payload + offset + 2;
				break;
			case 7:
				info.proto_comp = 1;
				break;
			case 8:
				info.addr_ctrl_comp = 1;
				break;
			case 13:
				if(payload[offset+1] == 3)
					info.callback = payload[offset+2];
				break;
			default:break;

			}

			offset += payload[offset+1];
		}
		break;
	default:break;

	}

	if(info.type == 1 || info.type == 5 || info.type == 9 || info.type == 11)
		direction = 0;

	write_lcp_log(flow, direction, &info, proto_type);
	return;
}

void dissect_chap(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type);
void dissect_chap(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type)
{
	if(payload_len < 4)
		return;

	int offset = 0;
	int direction;
	uint8_t type = payload[0];
	uint8_t id   = payload[1];
	uint16_t length = get_uint16_ntohs(payload, 2);

	if(length > payload_len)
		return;

	offset += 4;
	struct value_string random   = {0, 0};
	struct value_string name     = {0, 0};
	struct value_string message  = {0, 0};

	// 1 request, 2 response
	if(type == 1 || type == 2){
		random.value  = payload[offset];
		random.string = (const char*)payload + offset + 1;
		offset += payload[offset] + 1;
		name.string = (const char*)payload + offset;
		name.value  = payload_len > offset ? payload_len - offset : 0;
	}
	else if(type == 3 || type == 4){
		message.string = (const char*)payload + offset;
		message.value  = length > 4 ? length - 4 : 0;
	}

	if(type == 1)
		direction = 0;
	else
		direction = 1;

	int idx = 0;
	struct tbl_log *log_ptr;
	if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
		DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
		return;
	}
    init_log_ptr_data(log_ptr, flow,PROTOCOL_CHAP);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
	write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "chap");

	if(proto_type == PROTOCOL_L2TP)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "l2tp", 4);
	else if(proto_type == PROTOCOL_GRE)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "gre", 3);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(type == 1)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "challenge", 9);
	else if(type == 2)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "response", 8);
	else if(type == 3)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "success", 7);
	else if(type == 4)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "failture", 8);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, id);
	write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const uint8_t*)random.string, random.value);
	write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, name.string,    name.value);
	write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, message.string, message.value);

    if(type == 4)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "NO", 2);
    else
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "YES", 3);


	log_ptr->log_type = TBL_LOG_CHAP;
	log_ptr->log_len  = idx;

	if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
		rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
	return;
}

void dissect_pap(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type);
void dissect_pap(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type)
{
	if(payload_len < 4)
		return;
	int offset = 0;
	int direction = 1;
	uint8_t type = payload[0];
	uint8_t id   = payload[1];
	uint16_t length = get_uint16_ntohs(payload, 2);

	if(length > payload_len)
		return;

	offset += 4;
	struct value_string identifier = {0, 0};
	struct value_string password   = {0, 0};

	//request
	if(type == 1){
		identifier.value  = payload[offset];
		identifier.string = (const char*)payload + offset + 1;
		offset += payload[offset] + 1;
		password.value  = payload[offset];
		password.string = (const char*)payload + offset + 1;
		direction = 0;
	}

	int idx = 0;
	struct tbl_log *log_ptr;
	if(rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0){
		DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
		return;
	}
    init_log_ptr_data(log_ptr, flow,PROTOCOL_PAP);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
	write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "pap");

	if(proto_type == PROTOCOL_L2TP)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "l2tp", 4);
	else if(proto_type == PROTOCOL_GRE)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "gre", 3);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

	if(type == 1)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "authenticate-request", 20);
	else if(type == 2)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "authenticate-ACK", 16);
	else if(type == 3)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "authenticate-NAK", 16);
	else
		write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN,1);

	write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, id);
	write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, identifier.string, identifier.value);
	write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, password.string,   password.value);

    if(type == 3)
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "NO", 2);
    else
		write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, "YES", 3);

	log_ptr->log_type = TBL_LOG_PAP;
	log_ptr->log_len  = idx;

	if(write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
		rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
    }
	return;
}

void dissect_ppp_family(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type);
void dissect_ppp_family(struct flow_info *flow, const uint8_t *payload, uint16_t payload_len, int proto_type)
{
	int offset = 0;
	uint16_t ppp_type;
	if(payload_len < 4)
		return;

	if(payload[0] == 0xff && payload[1] == 0x03)
		offset += 2;

	ppp_type = get_uint16_ntohs(payload, offset);
	offset += 2;

	switch(ppp_type){

	case PPP_LCP:
		if(g_config.protocol_switch[PROTOCOL_LCP])
			dissect_lcp(flow, payload+offset, payload_len-offset, proto_type);
		break;
	case PPP_PAP:
		if(g_config.protocol_switch[PROTOCOL_PAP])
			dissect_pap(flow, payload+offset, payload_len-offset, proto_type);
		break;
	case PPP_CHAP:
		if(g_config.protocol_switch[PROTOCOL_CHAP])
			dissect_chap(flow, payload+offset, payload_len-offset, proto_type);
		break;
	default: break;
	}

}
static void init_chap_dissector(void){
    dpi_register_proto_schema(chap_field_array,EM_CHAP_MAX,"chap");
    map_fields_info_register(chap_field_array,PROTOCOL_CHAP, EM_CHAP_MAX, "chap");
}

static __attribute((constructor)) void    before_init_chap(void){
    register_tbl_array(TBL_LOG_CHAP, 0, "chap", init_chap_dissector);
}

static void init_lcp_dissector(void){
    dpi_register_proto_schema(lcp_field_array,EM_LCP_MAX,"lcp");
    map_fields_info_register(lcp_field_array,PROTOCOL_LCP, EM_LCP_MAX, "lcp");
}

static __attribute((constructor)) void    before_init_lcp(void){
        register_tbl_array(TBL_LOG_LCP, 0, "lcp", init_lcp_dissector);
}

static void init_pap_dissector(void){
    dpi_register_proto_schema(pap_field_array,EM_PAP_MAX,"pap");
    map_fields_info_register(pap_field_array,PROTOCOL_PAP, EM_PAP_MAX, "pap");
}

static __attribute((constructor)) void    before_init_pap(void){
        register_tbl_array(TBL_LOG_PAP, 0, "pap", init_pap_dissector);
}
