/****************************************************************************************
 * 文 件 名 : dpi_http.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy          2018/07/06
编码: wangy            2018/07/06
修改: xuxn          2019/03/11
修改: chunli         2020/07/10  输出HTTP BODY解析错误的报文
修改: chunli         2020/07/13  采用新版TCP重组
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <string.h>
#include <sys/time.h>
#include <openssl/aes.h>

#include "dpi_common.h"

#include "list.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_ocsp.h"
#include "post.h"
#include "dpi_high_app_protos.h"
#include "dpi_utils.h"
#include "dpi_http.h"
#include <yaProtoRecord/precord_schema.h>
#include "dpi_pschema.h"
#include "openssl/md5.h"

#define HTTP_VALUE_DUP(flow, from, len, to, size)       \
do                                                      \
{                                                       \
    if(to || len==0)                                    \
    {   if(len == 0) {                                  \
            /* http中只有key没有value   */              \
            (to) = (typeof(to))strdup(" ");             \
            (size) = strlen((const char*)(to));         \
        }                                               \
        break;                                          \
    }                                                   \
    (to) = (typeof(to))alloc_memdup((flow->memAc), (from), (len));    \
    (size) =(typeof(size))(len);                                     \
} while(0);                                             \

uint64_t http_new_flow = 0;
uint64_t http_free_flow = 0;

extern struct rte_mempool *tbl_log_content_mempool_256k;
extern rte_atomic64_t log_256k_fail;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
size_t ws_base64_decode_inplace(char *s);
static dpi_field_table  http_field_array[] = {
    DPI_FIELD_D(EM_HTTP_METHOD,                      YA_FT_STRING,               "Method"),
    DPI_FIELD_D(EM_HTTP_URI,                         YA_FT_STRING,               "URI"),
    DPI_FIELD_D(EM_HTTP_URI_COUNT,                   YA_FT_UINT32,               "URI-Count"),
    DPI_FIELD_D(EM_HTTP_URI_KEYS,                    YA_FT_STRING,               "URI-Keys"),
    DPI_FIELD_D(EM_HTTP_URI_KEYS_COUNT,              YA_FT_UINT32,               "URI-Keys-Count"),
    DPI_FIELD_D(EM_HTTP_URI_PATH,                    YA_FT_STRING,               "URI-Path"),
    DPI_FIELD_D(EM_HTTP_URI_PATH_COUNT,              YA_FT_UINT32,               "URI-Path-Count"),
    DPI_FIELD_D(EM_HTTP_VERSION,                     YA_FT_STRING,               "Version"),
    DPI_FIELD_D(EM_HTTP_STATUS,                      YA_FT_STRING,               "Status"),
    DPI_FIELD_D(EM_HTTP_RESPONSESTATUS,              YA_FT_STRING,               "ResponseStatus"),
    DPI_FIELD_D(EM_HTTP_CACHE_CONTROL,               YA_FT_STRING,               "Cache-Control"),
    DPI_FIELD_D(EM_HTTP_CONNECTION,                  YA_FT_STRING,               "Connection"),
    DPI_FIELD_D(EM_HTTP_COOKIE,                      YA_FT_STRING,               "Cookie"),
    DPI_FIELD_D(EM_HTTP_COOKIE2,                     YA_FT_STRING,               "Cookie2"),
    DPI_FIELD_D(EM_HTTP_COOKIE_KEYS,                 YA_FT_STRING,               "CookieKeys"),
    DPI_FIELD_D(EM_HTTP_DATE,                        YA_FT_STRING,               "Date"),
    DPI_FIELD_D(EM_HTTP_PRAGMA,                      YA_FT_STRING,               "Pragma"),
    DPI_FIELD_D(EM_HTTP_TRAILER,                     YA_FT_STRING,               "Trailer"),
    DPI_FIELD_D(EM_HTTP_TRANSFER_ENCODING,           YA_FT_STRING,               "Transfer-Encoding"),
    DPI_FIELD_D(EM_HTTP_UPGRADE,                     YA_FT_STRING,               "Upgrade"),
    DPI_FIELD_D(EM_HTTP_VIA,                         YA_FT_STRING,               "Via"),
    DPI_FIELD_D(EM_HTTP_VIA_COUNT,                   YA_FT_UINT32,               "Via-Count"),
    DPI_FIELD_D(EM_HTTP_WARNING,                     YA_FT_STRING,               "Warning"),
    DPI_FIELD_D(EM_HTTP_ACCEPT,                      YA_FT_STRING,               "Accept"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_CHARSET,              YA_FT_STRING,               "Accept-Charset"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_ENCODING,             YA_FT_STRING,               "Accept-Encoding"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_LANGUAGE,             YA_FT_STRING,               "Accept-Language"),
    DPI_FIELD_D(EM_HTTP_AUTHORIZATION,               YA_FT_STRING,               "Authorization"),
    DPI_FIELD_D(EM_HTTP_AUTH_USERNAME,               YA_FT_STRING,               "Username"),
    DPI_FIELD_D(EM_HTTP_EXPECT,                      YA_FT_STRING,               "Expect"),
    DPI_FIELD_D(EM_HTTP_FROM,                        YA_FT_STRING,               "From"),
    DPI_FIELD_D(EM_HTTP_HOST,                        YA_FT_STRING,               "Host"),
    DPI_FIELD_D(EM_HTTP_HOST_COUNT,                  YA_FT_UINT32,               "Host-Count"),
    DPI_FIELD_D(EM_HTTP_IF_MATCH,                    YA_FT_STRING,               "If-Match"),
    DPI_FIELD_D(EM_HTTP_IF_MODIFIED_SINCE,           YA_FT_STRING,               "If-Modified-Since"),
    DPI_FIELD_D(EM_HTTP_IF_NONE_MATCH,               YA_FT_STRING,               "If-None-Match"),
    DPI_FIELD_D(EM_HTTP_IF_RANGE,                    YA_FT_STRING,               "If-Range"),
    DPI_FIELD_D(EM_HTTP_IF_UNMODIFIED_SINCE,         YA_FT_STRING,               "If-Unmodified-Since"),
    DPI_FIELD_D(EM_HTTP_MAX_FORWARDS,                YA_FT_UINT32,               "Max-Forwards"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHORIZATION,         YA_FT_STRING,               "Proxy-Authorization"),
    DPI_FIELD_D(EM_HTTP_PROXY_TYPE,                  YA_FT_STRING,               "Proxy-Type"),
    DPI_FIELD_D(EM_HTTP_PROXY_LOGIN,                 YA_FT_STRING,               "Proxy-Login"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHORIZATION_INFO,    YA_FT_STRING,               "Proxy-Authorization-Info"),
    DPI_FIELD_D(EM_HTTP_RANGE,                       YA_FT_STRING,               "Range"),
    DPI_FIELD_D(EM_HTTP_REFERER,                     YA_FT_STRING,               "Referer"),
    DPI_FIELD_D(EM_HTTP_TE,                          YA_FT_STRING,               "TE"),
    DPI_FIELD_D(EM_HTTP_USER_AGENT,                  YA_FT_STRING,               "User-Agent"),
    DPI_FIELD_D(EM_HTTP_USER_AGENT_NUM,              YA_FT_UINT32,               "UserAgentCount"),
    DPI_FIELD_D(EM_HTTP_PROXY_PROXYAGENT,            YA_FT_STRING,               "Proxy-Agent"),
    DPI_FIELD_D(EM_HTTP_USER_CPU,                    YA_FT_STRING,               "UA-CPU"),
    DPI_FIELD_D(EM_HTTP_USER_OS,                     YA_FT_STRING,               "UA-OS"),
    DPI_FIELD_D(EM_HTTP_ACCEPT_RANGES,               YA_FT_STRING,               "Accept-Ranges"),
    DPI_FIELD_D(EM_HTTP_AGE,                         YA_FT_STRING,               "Age"),
    DPI_FIELD_D(EM_HTTP_ETAG,                        YA_FT_STRING,               "ETag"),
    DPI_FIELD_D(EM_HTTP_LOCATION,                    YA_FT_STRING,               "Location"),
    DPI_FIELD_D(EM_HTTP_PROXY_AUTHENTICATE,          YA_FT_STRING,               "Proxy-Authenticate"),
    DPI_FIELD_D(EM_HTTP_INQUIRY_TYPE,                YA_FT_STRING,               "Inquiry_Type"),
    DPI_FIELD_D(EM_HTTP_PROXY_CONNECT_PORT,          YA_FT_STRING,               "Proxy-Connect_Port"),
    DPI_FIELD_D(EM_HTTP_PROXY_CONNECT_HOST,          YA_FT_STRING,               "Proxy-Connect_Host"),
    DPI_FIELD_D(EM_HTTP_RETRY_AFTER,                 YA_FT_STRING,               "Retry-After"),
    DPI_FIELD_D(EM_HTTP_SERVER,                      YA_FT_STRING,               "Server"),
    DPI_FIELD_D(EM_HTTP_VARY,                        YA_FT_STRING,               "Vary"),
    DPI_FIELD_D(EM_HTTP_WWW_AUTHENTICATE,            YA_FT_STRING,               "WWW-Authenticate"),
    DPI_FIELD_D(EM_HTTP_ALLOW,                       YA_FT_STRING,               "Allow"),
    DPI_FIELD_D(EM_HTTP_CONTENT_ENCODING_S,          YA_FT_UINT32,               "Content-Encoding-s"),
    DPI_FIELD_D(EM_HTTP_CONTENT_ENCODING_C,          YA_FT_STRING,               "Content-Encoding-c"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LANGUAGE,            YA_FT_STRING,               "Content-Language"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LENGTH,              YA_FT_UINT32,               "Content-Length"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LENGTH_REQ,          YA_FT_UINT32,               "reqContent-Length"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LENGTH_RSP,          YA_FT_UINT32,               "rspContent-Length"),
    DPI_FIELD_D(EM_HTTP_CONTENT_LOCATION,            YA_FT_STRING,               "Content-Location"),
    DPI_FIELD_D(EM_HTTP_CONTENT_MD5,                 YA_FT_STRING,               "Content-MD5"),
    DPI_FIELD_D(EM_HTTP_CONTENT_RANGE,               YA_FT_STRING,               "Content-Range"),
    DPI_FIELD_D(EM_HTTP_CONTENT_TYPE,                YA_FT_STRING,               "Content-Type"),
    DPI_FIELD_D(EM_HTTP_EXPIRES,                     YA_FT_STRING,               "Expires"),
    DPI_FIELD_D(EM_HTTP_REFRESH,                     YA_FT_STRING,               "Refresh"),
    DPI_FIELD_D(EM_HTTP_LAST_MODIFIED,               YA_FT_STRING,               "Last-Modified"),
    DPI_FIELD_D(EM_HTTP_X_FORWARDED_FOR,             YA_FT_STRING,               "X-Forwarded-For"),
    DPI_FIELD_D(EM_HTTP_SET_COOKIE,                  YA_FT_STRING,               "Set-Cookie"),
    DPI_FIELD_D(EM_HTTP_SET_COOKIE2,                 YA_FT_STRING,               "Set-Cookie2"),
    DPI_FIELD_D(EM_HTTP_DNT,                         YA_FT_STRING,               "DNT"),
    DPI_FIELD_D(EM_HTTP_X_POWERED_BY,                YA_FT_STRING,               "X-Powered-By"),
    DPI_FIELD_D(EM_HTTP_P3P,                         YA_FT_STRING,               "P3P"),
    DPI_FIELD_D(EM_HTTP_CLIENT_HEAD,                 YA_FT_STRING,               "Client_Head"),
    DPI_FIELD_D(EM_HTTP_CLIENT_BODY,                 YA_FT_STRING,               "Client_Body"),
    DPI_FIELD_D(EM_HTTP_GET_SVR_REQ_URL,             YA_FT_STRING,               "Get_Svr_Req_Url"),
    DPI_FIELD_D(EM_HTTP_ENTITY_TITLE_HEAD,           YA_FT_STRING,               "Entity_Title_Head"),
    DPI_FIELD_D(EM_HTTP_H_A_NUMBER,                  YA_FT_STRING,               "H_A_NUMBER"),
    DPI_FIELD_D(EM_HTTP_H_X_NUMBER,                  YA_FT_STRING,               "H_X_NUMBER"),
    DPI_FIELD_D(EM_HTTP_APPTYPE,                     YA_FT_STRING,               "App_Type"),
    DPI_FIELD_D(EM_HTTP_X_HTTP_TOKEN,                YA_FT_STRING,               "X_http_token"),
    DPI_FIELD_D(EM_HTTP_X_COOKIE_TOKEN,              YA_FT_STRING,               "X_cookie_token"),
    DPI_FIELD_D(EM_HTTP_X_SINKHOLE,                  YA_FT_STRING,               "X_Sinkhole"),
    DPI_FIELD_D(EM_HTTP_REQ_HEAD_FIELDS,             YA_FT_UINT32,               "reqHeadFields"),
    DPI_FIELD_D(EM_HTTP_REQ_HEAD_FIELDS_MD5,         YA_FT_UINT32,               "reqHeadFieldsMD5"),
    DPI_FIELD_D(EM_HTTP_REQ_HEAD_FIELDS_NUM,         YA_FT_UINT32,               "reqHeadFieldsCount"),
    DPI_FIELD_D(EM_HTTP_REQ_METHOD_NUM,              YA_FT_UINT32,               "reqMethodCount"),
    DPI_FIELD_D(EM_HTTP_REQ_VERSION_NUM,             YA_FT_UINT32,               "reqVersionCount"),
    DPI_FIELD_D(EM_HTTP_REQ_BODY,                    YA_FT_STRING,               "reqBody"),
    DPI_FIELD_D(EM_HTTP_RSP_HEAD_FIELDS,             YA_FT_STRING,               "rspHeadFields"),
    DPI_FIELD_D(EM_HTTP_RSP_HEAD_FIELDS_MD5,         YA_FT_STRING,               "rspHeadFieldsMD5"),
    DPI_FIELD_D(EM_HTTP_RSP_CACHE_CONTROL,           YA_FT_STRING,               "rspCache-Control"),
    DPI_FIELD_D(EM_HTTP_RSP_CONNECTION,              YA_FT_STRING,               "rspConnection"),
    DPI_FIELD_D(EM_HTTP_RSP_PRAGMA,                  YA_FT_STRING,               "rspPragma"),
    DPI_FIELD_D(EM_HTTP_RSP_ACCEPT_RANGES,           YA_FT_STRING,               "rspAccept-Ranges"),
    DPI_FIELD_D(EM_HTTP_RSP_ACCEPT_CHARSET,          YA_FT_STRING,               "rspAccept-Charset"),
    DPI_FIELD_D(EM_HTTP_RSP_CONTENT_TYPE,            YA_FT_STRING,               "rspContent-Type"),
    DPI_FIELD_D(EM_HTTP_RSP_VERSION,                 YA_FT_STRING,               "rspVersion"),
    DPI_FIELD_D(EM_HTTP_RSP_BODY,                    YA_FT_STRING,               "rspBody"),
    DPI_FIELD_D(EM_HTTP_RSP_FULLTEXT_LEN,            YA_FT_UINT32,               "rspFullTextLen"),
    DPI_FIELD_D(EM_HTTP_URL,                         YA_FT_STRING,               "Url"),
    DPI_FIELD_D(EM_HTTP_CONDISPUP,                   YA_FT_STRING,               "conDispUp"),
    DPI_FIELD_D(EM_HTTP_CONDISPDOWN,                 YA_FT_STRING,               "conDispDown"),
    DPI_FIELD_D(EM_HTTP_URISEARCH,                   YA_FT_STRING,               "uriSearch"),
    DPI_FIELD_D(EM_HTTP_IMSI,                        YA_FT_STRING,               "imsi"),
    DPI_FIELD_D(EM_HTTP_IMEI,                        YA_FT_STRING,               "imei"),
#ifdef DPI_SDT_ZDY
    DPI_FIELD_D(EM_HTTP_V51,                         YA_FT_STRING,               "V51"),
    DPI_FIELD_D(EM_HTTP_LOCAL_FILENAME,              YA_FT_STRING,               "localFilename"),
    DPI_FIELD_D(EM_HTTP_CONDISP,                     YA_FT_STRING,               "conDisp"),
    DPI_FIELD_D(EM_HTTP_REMAINDERLINE,               YA_FT_STRING,               "remainderLine"),
#endif
};




static struct {const char *method; int len;} HTTP_METHOD[] =
{
    {"GET ",     4},
    {"POST ",    5},
    {"OPTIONS ", 8},
    {"HEAD ",    5},
    {"PUT ",     4},
    {"DELETE ",  7},
    {"CONNECT ", 8},
    {"PROPFIND ",9},
    {"REPORT ",  7},
    {NULL,       0}
};

static const char* searchHost[] = {
  "bing",
  "baidu",
  "google",
  "sougou",
  "jinan",
  NULL
};
static const char* searchKey[] = {
  "wb",
  "q",
  "query",
  "word",
  NULL
};

typedef struct{
    struct http_session * session;
    struct http_info *http_value;
}USER;
static inline void _free_header(gpointer data)
{
    free(data);
}

static inline void _free_key_value(gpointer data)
{
    struct header_tmp_value *_value = (struct header_tmp_value *)data;

    if (_value->need_free)
        dpi_free((void *)_value->ptr);
    dpi_free(data);
}

static void _find_hash_write_log_delete(struct http_info *http_value, const char *header_name, precord_t *record, int *idx)
{
    gpointer _value;
    struct header_value *value;

    if(http_value->table==NULL){
         write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
         return ;
    }

    _value = g_hash_table_lookup(http_value->table, header_name);
    value = (struct header_value *)_value;
    if (!value)
        write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
    else
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)value->ptr, value->len);
    g_hash_table_remove(http_value->table, header_name);
}

/* 0-没命中规则，1-命中规则，是想要的数据写tbl */
static int _http_content_type_filter(char *buf)
{
    if(g_config.http.num<=0){
        return 1;
    }
    regmatch_t pmatch[1];
    const size_t nmatch = 1;
    int status=0;
    int i=0;
    for(i=0;i<g_config.http.num;i++){
        status=regexec(&g_config.http.reg[i],buf,nmatch,pmatch,0);
        if(0==status){
            return 1;
        }
    }

    return 0;
}

static int _recreate_http_tbl_dir(int thread_id)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);

    bzero(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH);
    bzero(flow_thread_info[thread_id].file_tbl_name,COMMON_FILE_PATH);
    flow_thread_info[thread_id].file_count=0;
    flow_thread_info[thread_id].timeout_sec=g_config.g_now_time;

    if(g_config.show_task_id){
        snprintf(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH,
                                  "%s/%s/%s_%06ld_http_n_%03d_%s.writing",
                                  g_config.tbl_out_dir,
                                  tbl_log_array[TBL_LOG_HTTP].protoname,
                                  time_to_datetime(g_config.g_now_time),
                                  tv.tv_usec,thread_id,
                                  g_config.task_id);
    }
    else{
        snprintf(flow_thread_info[thread_id].file_tbl_dir,COMMON_FILE_PATH,
                                  "%s/%s/%s_%06ld_http_n_%03d.writing",
                                  g_config.tbl_out_dir,
                                  tbl_log_array[TBL_LOG_HTTP].protoname,
                                  time_to_datetime(g_config.g_now_time),
                                  tv.tv_usec,thread_id);
    }

    mkdirs(flow_thread_info[thread_id].file_tbl_dir);

    if(g_config.show_task_id){
        snprintf(flow_thread_info[thread_id].file_tbl_name, COMMON_FILE_PATH,
                                           "%s/%s_%06ld_http_n_%03d_%s.tbl",
                                           flow_thread_info[thread_id].file_tbl_dir,
                                           time_to_datetime(g_config.g_now_time),
                                           tv.tv_usec,thread_id,
                                           g_config.task_id);
    }
    else{
        snprintf(flow_thread_info[thread_id].file_tbl_name, COMMON_FILE_PATH,
                                           "%s/%s_%06ld_http_n_%03d.tbl",
                                           flow_thread_info[thread_id].file_tbl_dir,
                                           time_to_datetime(g_config.g_now_time),
                                           tv.tv_usec,thread_id);
    }

    flow_thread_info[thread_id].tbl_fp=fopen(flow_thread_info[thread_id].file_tbl_name, "w");
    if(NULL==flow_thread_info[thread_id].tbl_fp){
        printf("http tbl file:[%s] create error!\n",flow_thread_info[thread_id].file_tbl_name);
        return 0;
    }

    return 1;
}



static int _get_http_file_name(char *file_name, int thread_id, int diretion)
{
    if(NULL==file_name){
        return 0;
    }
    int ret=0;

    if(flow_thread_info[thread_id].tbl_fp==NULL){
        ret=_recreate_http_tbl_dir(thread_id);
        if(0==ret){
            return 0;
        }
    }else{
        if(flow_thread_info[thread_id].file_count>= g_config.log_max_num ||
           (flow_thread_info[thread_id].timeout_sec + g_config.write_tbl_maxtime < g_config.g_now_time)){
            if(flow_thread_info[thread_id].tbl_fp!=NULL){
                fclose(flow_thread_info[thread_id].tbl_fp);
                flow_thread_info[thread_id].tbl_fp=NULL;
            }
            char http_tbl_file[COMMON_FILE_PATH]={0};
            memcpy(http_tbl_file,flow_thread_info[thread_id].file_tbl_dir,strlen(flow_thread_info[thread_id].file_tbl_dir)-8);
            rename(flow_thread_info[thread_id].file_tbl_dir,http_tbl_file);
            ret=_recreate_http_tbl_dir(thread_id);
            if(0==ret){
                return 0;
            }
        }
    }

    if(g_config.show_task_id){
        snprintf(file_name, COMMON_FILE_PATH, "%s/%s_%llu_%ld_%03d_%s.http",
                               flow_thread_info[thread_id].file_tbl_dir,
                               diretion==FLOW_DIR_SRC2DST?"request":"response",
                               (unsigned long long)g_config.g_now_time_usec,
                               random(),
                               thread_id,
                               g_config.task_id);
    }
    else{
        snprintf(file_name, COMMON_FILE_PATH, "%s/%s_%llu_%ld_%03d.http",
                               flow_thread_info[thread_id].file_tbl_dir,
                               diretion==FLOW_DIR_SRC2DST?"request":"response",
                               (unsigned long long)g_config.g_now_time_usec,
                               random(),
                               thread_id);
    }
    return 1;
}


static void get_cookie_key(const uint8_t *key_start, int len, struct http_info *http_value) {

    while (key_start && isspace(*key_start) && len)
    {
        key_start++;
        len --;
    }

    if (!key_start || !len)
        return;

    const uint8_t *key = memchr(key_start, '=', len);
    if (key) {
        // key found
        int key_len = key - key_start;
        int min = ((int)sizeof(http_value->cookie_keys) - 1 > key_len ? key_len : (int)sizeof(http_value->cookie_keys) - 1);
        if ((int)(sizeof(http_value->cookie_keys) - strlen(http_value->cookie_keys)) > key_len) {
            strncat(http_value->cookie_keys, (char *)key_start, min);
            strcat(http_value->cookie_keys, " ");

            http_value->cookie_keys_num++;
        }
    }
}

static void
http_parse_cookies(const char * key, const struct header_value * _value,
                   struct http_info * http_value)
{
  if (key == NULL || _value == NULL)
    return;

  int k;
  for (k = 0; k < _value->len; ) {
    const uint8_t *pair_end = memchr(_value->ptr + k, ';', _value->len - k);
    int pair_len = 0;
    if (pair_end) {
      pair_len = pair_end - (_value->ptr + k);
      get_cookie_key(_value->ptr + k, pair_len, http_value);
    }
    else {
      get_cookie_key(_value->ptr + k, _value->len - k, http_value);
      break;
    }

    k += pair_len + 1;
  }
}
static void
http_collect_head_fields(const char * header, uint16_t header_len,
                 struct http_info *http_value)
{
  uint16_t max_len = sizeof(http_value->rsp_heads);
  size_t headers_len = 0;
  char * headers = NULL;

  if (http_value == NULL)
    return;

   if (http_value->is_request) {
    headers_len = strlen(http_value->req_heads);
    headers = &http_value->req_heads[0];
    if (header_len + headers_len + 1 > max_len)
      return;
    http_value->req_heads_num++;
  } else {
    headers_len = strlen(http_value->rsp_heads);
    headers = &http_value->rsp_heads[0];
    if (header_len + headers_len + 1 > max_len)
      return;
  }

  if (headers_len != 0) {
    strcat(headers, ",");
  }
  strncat(headers, header, header_len);

  gchar *h_md5 = g_compute_checksum_for_string(G_CHECKSUM_MD5, headers, strlen(headers));
  if (http_value->is_request) {
    memcpy(http_value->req_heads_md5, h_md5, strlen(h_md5));
  } else {
    memcpy(http_value->rsp_heads_md5, h_md5, strlen(h_md5));
  }
}
/*
* 解析http头部的每一行头域，寻找空行位置
*/
static void parse_http_line_info(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len, struct http_info *http_value)
{
    uint32_t a;
    uint32_t i = 0;
    int             line_len    = 0;
    int             header_len  = 0;
    uint8_t        parsed_lines = 0;
    uint8_t cookie = 0;
    uint32_t                end = payload_len - 1;
    const uint8_t     *line_ptr = payload;
    char               *_header = NULL;
    struct header_value *_value = NULL;
    char header_tmp[1024];
    int len;


    if ((payload_len == 0) || (payload == NULL) || (end == 0))
        return;

    http_value->table = g_hash_table_new_full(g_str_hash, g_str_equal, _free_header, _free_key_value);
    if (http_value->table == NULL) {
        DPI_LOG(DPI_LOG_DEBUG, "ghash table create error");
        return;
    }

    for (a = 0; a < end; a++) {
        if (get_uint16_t(payload, a) == ntohs(0x0d0a)) {
            line_len = (uint32_t)(((unsigned long)&payload[a]) - ((unsigned long)line_ptr));

            if (line_len == 0) {
                http_value->empty_line_position = a;
                break;
            }

            _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
            if (!_value) {
                DPI_LOG(DPI_LOG_WARNING, "malloc failed");
                goto next_line;
            }

            memset(_value, 0, sizeof(*_value));
            _value->need_free = 0;

            if (parsed_lines == 0) {
                _header = strdup("head_line");
                if (!_header) {
                    DPI_LOG(DPI_LOG_WARNING, "malloc failed");
                    dpi_free(_value);
                    goto next_line;
                }
                HTTP_VALUE_DUP(flow, line_ptr, line_len, _value->ptr, _value->len);
                _value->need_free = 0; //超时会自动删除， 无需手动管理
                //printf("%u [%s]:[%s]\n", __LINE__, _header, _value->ptr);
                g_hash_table_insert(http_value->table, _header, _value);
            } else {
                header_len = find_special_char(line_ptr, line_len, ':');
                if (header_len <= 0) {
                    dpi_free(_value);
                    goto next_line;
                }
                if (line_ptr[header_len - 1] == ' ')
                    _header = strndup((const char *)line_ptr, header_len - 1);
                else
                    _header = strndup((const char *)line_ptr, header_len);

                strdown_inplace(_header);

                len = DPI_MIN(header_len, (int)sizeof(header_tmp)-1);
                if(len)
                {
                    memset(header_tmp, 0, sizeof(header_tmp));
                    strncpy(header_tmp, _header, len);
                }

                if (header_len + 1 >= line_len) {
                    free(_header);
                    dpi_free(_value);
                    goto next_line;
                }
                //跳过冒号右边的空格
                for(i = 0; ' ' == line_ptr[header_len + 1 + i];i++);
                const char *p_value = (const char *)(line_ptr + header_len + 1 + i);
                int        len_value=0;
                //跳过Value右边的 空格\r\n
                for(i = 0; p_value[i] && i < (uint32_t)(line_len - header_len); i++)
                {
                    int end = 0;
                    switch(p_value[i])
                    {
                        //UA里面存在空格
                        case '\r':
                        case '\n':
                            len_value = i;
                            end=1;
                            break;
                    }
                    if(end) break;
                }
                HTTP_VALUE_DUP(flow, p_value, len_value, _value->ptr, _value->len);
                _value->need_free = 0; //超时会自动删除， 无需手动管理

                //HTTP/1.1 200 OK
                //Date: Mon, 14 Jul 2025 09:34:21 GMT
                //Server: Apache
                //Expires: Thu, 19 Nov 1981 08:52:00 GMT
                //Cache-Control: private
                //Pragma: no-cache
                //Set-Cookie: lang=zh-cn; expires=Wed, 13-Aug-2025 09:34:21 GMT; Max-Age=2592000; path=/zentao/
                //Set-Cookie: device=desktop; expires=Wed, 13-Aug-2025 09:34:21 GMT; Max-Age=2592000; path=/zentao/; HttpOnly
                //Set-Cookie: theme=default; expires=Wed, 13-Aug-2025 09:34:21 GMT; Max-Age=2592000; path=/zentao/
                //X-Frame-Options: SAMEORIGIN
                //Set-Cookie: lastProduct=43; expires=Wed, 13-Aug-2025 09:34:21 GMT; Max-Age=2592000; path=/zentao/; HttpOnly
                //Vary: Accept-Encoding
                //Content-Encoding: gzip
                //Content-Length: 18628
                //Keep-Alive: timeout=5, max=99
                //Connection: Keep-Alive
                //Content-Type: text/html; Language=UTF-8;charset=UTF-8
                //HASH的特性 决定了 只能存储一个 “Set-Cookie”，为了避免干扰仅保留第一个.
                gpointer pointer = g_hash_table_lookup(http_value->table, _header);
                if(!pointer)
                {
                    //printf("INSERT:[%s]:[%.*s]\n", _header, _value->len, _value->ptr);
                    g_hash_table_insert(http_value->table, _header, _value);
                    if(g_config.http.http_exquisite_switch){
                        http_collect_head_fields(_header, header_len, http_value);
                    }
                }
                else
                {
                    //printf("DROP  :[%s]:[%.*s]\n", _header, _value->len, _value->ptr);
                }

                if (!strcmp(header_tmp, "host")) {
                  http_value->host_num++;
                } // add

                // 解析cookie的keys
                if (cookie == 0 && !strcmp(header_tmp, "cookie")) {
                  http_parse_cookies(header_tmp, _value, http_value);
                  cookie = 1;
                }
            }

next_line:
            parsed_lines++;
            line_ptr = &payload[a + 2];
            line_len = 0;

            if((a + 2) >= payload_len)
                break;

            a++; /* next char in the payload */
        }
    }
}

struct write_position
{
    char *ptr;
    int *index;
    int unknow_line_num;
};

struct merge_unknown_line
{
    char unknown_line[1500];
    int index;
    int unknow_line_num;
};

static int write_http_unknown_reconds(char *log, int *idx, int max_len, const char *key, unsigned int key_len,
        const char *val, unsigned int val_len)
{
    unsigned int i;
    int index = *idx;

    for (i = 0; i < key_len; i++) {
        if (index >= max_len)
            break;

        if (key[i] == '|')
            log[index++] = '_';
        else if (key[i] == '\n' || key[i] == '\r' || key[i] == '&' || key[i] == '=')
            log[index++] = ' ';
        else
            log[index++] = key[i];
    }

    if (index < max_len)
        log[index++] = '=';

    for (i = 0; i < val_len; i++) {
        if (index >= max_len)
            break;

        if (val[i] == '|')
            log[index++] = '_';
        else if (val[i] == '\n' || val[i] == '\r' || val[i] == '&' || val[i] == '=')
            log[index++] = ' ';
        else
            log[index++] = val[i];
    }

    if (index + 1 < max_len) {
        log[index++] = '&';
    }

    *idx = index;
    return 0;
}

static void _merge_unknown_line(gpointer key, gpointer value, gpointer user_data)
{
    struct merge_unknown_line *position = (struct merge_unknown_line *)user_data;
    char *_key = (char *)key;
    struct header_value *_value = (struct header_value *)value;

    if (strlen(_key) > 0 || _value->len > 0) {
        write_http_unknown_reconds(position->unknown_line, &position->index, sizeof(position->unknown_line),
                    _key, strlen(_key), (const char *)_value->ptr, _value->len);
    }
    position->unknow_line_num++;
}

static void _get_http_client_server_port(struct flow_info *flow, const uint8_t *payload, uint32_t payload_len)
{
    if(payload==NULL || payload_len<7){
        return;
    }
    if (payload_len >= 7 && strncasecmp((const char *)payload, "HTTP/1.", 7) == 0) {
        //s2c
        flow->drt_port_src[FLOW_DIR_DST2SRC]=flow->port_src;
        flow->drt_port_dst[FLOW_DIR_DST2SRC]=flow->port_dst;
    }
    else if (payload_len >= 10) {
        for(int i = 0; HTTP_METHOD[i].method; i++){
            if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len)){
                //c2s
                flow->drt_port_src[FLOW_DIR_SRC2DST]=flow->port_src;
                flow->drt_port_dst[FLOW_DIR_SRC2DST]=flow->port_dst;
                break;
            }
        }
    }
}

static int http_change_and_account_gbk(uint8_t *pData, int len)
{
    int loop = len;
    uint8_t *p = pData;
    while(loop > 0)
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            *p++ = ' ';
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        return p - pData;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}
//add by xuxn 统计http解析post的总次数、成功率、失败率
extern volatile uint64_t post_total;    //http 解析.POST文件 总记录条数
extern volatile uint64_t post_success;    //http 解析.POST文件 解析成功的记录条数

static void gen_content_file_name(struct flow_info *flow, struct http_info *http_value, char *filename, int filename_len);
static int http_field_element(struct tbl_log *log_ptr, struct flow_info *flow, int direction, struct http_info *http_value, int *idx, int i)
{
    char local_filename[COMMON_FILE_PATH]={0};
    precord_t *record = log_ptr->record;
    struct http_session *session = (struct http_session *)flow->app_session;
    switch(i){
    case EM_HTTP_METHOD:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(method), http_value->PROTOCOL_VAL_LEN(method));
        break;
    case EM_HTTP_URI:
        if(session->uri!=NULL){
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->uri, strlen(session->uri));
        }else{
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        }
        break;
    case EM_HTTP_URI_COUNT:
        if (http_value->uri_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->uri_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_KEYS:
        if (session->uri_keys_num > 0) {
            char _tmp[4096];
            int j;
            memset(_tmp, 0, sizeof(_tmp));
            for (j = 0; j < session->uri_keys_num; j++) {
                if ((int)(sizeof(_tmp) - strlen(_tmp)) < (int)strlen(session->uri_keys[j]))
                    break;

                strcat(_tmp, session->uri_keys[j]);
                strcat(_tmp, ",");
            }

            if (_tmp[strlen(_tmp)] == ',')
                _tmp[strlen(_tmp)] = '\0';
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, _tmp, strlen(_tmp));
        }
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_KEYS_COUNT:
        if (session->uri_keys_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)session->uri_keys_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_PATH:
        if (strlen(session->uri_path) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->uri_path, strlen(session->uri_path));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URI_PATH_COUNT:
        if (session->uri_path_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)session->uri_path_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_VERSION:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(version), http_value->PROTOCOL_VAL_LEN(version));
        break;
    case EM_HTTP_STATUS:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(code), http_value->PROTOCOL_VAL_LEN(code));
        break;
    case EM_HTTP_RESPONSESTATUS:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)http_value->PROTOCOL_VAL_PTR(response_code), http_value->PROTOCOL_VAL_LEN(response_code));
        break;
    case EM_HTTP_CACHE_CONTROL:
        _find_hash_write_log_delete(http_value, "cache-control", record, idx);
        break;
    case EM_HTTP_CONNECTION:
        _find_hash_write_log_delete(http_value, "connection", record, idx);
        break;
    case EM_HTTP_COOKIE:
        _find_hash_write_log_delete(http_value, "cookie", record, idx);
        break;
    case EM_HTTP_COOKIE2:
        _find_hash_write_log_delete(http_value, "cookie2", record, idx);
        break;
    case EM_HTTP_COOKIE_KEYS:
        if (http_value->cookie_keys_num > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->cookie_keys, strlen(http_value->cookie_keys));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_DATE:
        _find_hash_write_log_delete(http_value, "date", record, idx);
        break;
    case EM_HTTP_PRAGMA:
        _find_hash_write_log_delete(http_value, "pragma", record, idx);
        break;
    case EM_HTTP_TRAILER:
        _find_hash_write_log_delete(http_value, "trailer", record, idx);
        break;
    case EM_HTTP_TRANSFER_ENCODING:
        _find_hash_write_log_delete(http_value, "transfer-encoding", record, idx);
        break;
    case EM_HTTP_UPGRADE:
        _find_hash_write_log_delete(http_value, "upgrade", record, idx);
        break;
    case EM_HTTP_VIA:
        _find_hash_write_log_delete(http_value, "via", record, idx);
        break;
    case EM_HTTP_VIA_COUNT:
        if (http_value->via_num != 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->via_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_WARNING:
        _find_hash_write_log_delete(http_value, "warning", record, idx);
        break;
    case EM_HTTP_ACCEPT:
        _find_hash_write_log_delete(http_value, "accept", record, idx);
        break;
    case EM_HTTP_ACCEPT_CHARSET:
        _find_hash_write_log_delete(http_value, "accept-charset", record, idx);
        break;
    case EM_HTTP_ACCEPT_ENCODING:
        _find_hash_write_log_delete(http_value, "accept-encoding", record, idx);
        break;
    case EM_HTTP_ACCEPT_LANGUAGE:
        _find_hash_write_log_delete(http_value, "accept-language", record, idx);
        break;
    case EM_HTTP_AUTHORIZATION:
//      _find_hash_write_log_delete(http_value, "authorization", record, idx);
        if ((int)strlen(session->auth) > 0) {
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->auth, strlen(session->auth));
            session->auth[0] = 0;
        }
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_AUTH_USERNAME:
        if (http_value->PROTOCOL_VAL_LEN(username) && http_value->PROTOCOL_VAL_PTR(username))
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (char *)http_value->PROTOCOL_VAL_PTR(username), http_value->PROTOCOL_VAL_LEN(username));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_EXPECT:
        _find_hash_write_log_delete(http_value, "expect", record, idx);
        break;
    case EM_HTTP_FROM:
        _find_hash_write_log_delete(http_value, "from", record, idx);
        break;
    case EM_HTTP_HOST:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
        break;
    case EM_HTTP_HOST_COUNT:
        if (http_value->host_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->host_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_IF_MATCH:
        _find_hash_write_log_delete(http_value, "if-match", record, idx);
        break;
    case EM_HTTP_IF_MODIFIED_SINCE:
        _find_hash_write_log_delete(http_value, "if-modified-since", record, idx);
        break;
    case EM_HTTP_IF_NONE_MATCH:
        _find_hash_write_log_delete(http_value, "if-none-match", record, idx);
        break;
    case EM_HTTP_IF_RANGE:
        _find_hash_write_log_delete(http_value, "if-range", record, idx);
        break;
    case EM_HTTP_IF_UNMODIFIED_SINCE:
        _find_hash_write_log_delete(http_value, "if-unmodified-since", record, idx);
        break;
    case EM_HTTP_MAX_FORWARDS:
        _find_hash_write_log_delete(http_value, "max-forwards", record, idx);
        break;
    case EM_HTTP_PROXY_AUTHORIZATION:
        _find_hash_write_log_delete(http_value, "proxy-authorization", record, idx);
        break;
    case EM_HTTP_PROXY_TYPE:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->proxy_type, strlen(http_value->proxy_type));
        break;
    case EM_HTTP_PROXY_LOGIN:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->proxy_login, strlen(http_value->proxy_login));
        break;
    case EM_HTTP_PROXY_AUTHORIZATION_INFO:
        _find_hash_write_log_delete(http_value, "proxy-authentication-info", record, idx); //这个字段名称写错了,应该是 proxy-authentication-info 而不是 proxy-authorization-info
        break;
    case EM_HTTP_RANGE:
        _find_hash_write_log_delete(http_value, "range", record, idx);
        break;
    case EM_HTTP_REFERER:
        _find_hash_write_log_delete(http_value, "referer", record, idx);
        break;
    case EM_HTTP_TE:
        _find_hash_write_log_delete(http_value, "te", record, idx);
        break;
    case EM_HTTP_USER_AGENT:
        {
            if(http_value->table==NULL){
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
                break;
            }
            gpointer _value;
            struct header_value *value;
            _value = g_hash_table_lookup(http_value->table, "user-agent");
            value = (struct header_value *)_value;
            if (!value)
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            else
            {
                int i = 0, j = 0;
                char tmp[value->len*2 + 1];
                memset(tmp, 0, value->len*2 + 1);
                for(i=0; i < value->len && j < value->len*2 + 1; i++)
                {
                    if(!isprint(value->ptr[i]))
                    {
                        sprintf(tmp+j, "%2x", value->ptr[i]);
                        j++;
                    }
                    else
                        tmp[j] = value->ptr[i];
                    j++;
                }
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char *)tmp, strlen(tmp));
            }
            g_hash_table_remove(http_value->table, "user-agent");
        }
        break;
    case EM_HTTP_USER_AGENT_NUM:
        if (http_value->user_agent_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->user_agent_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_PROXY_PROXYAGENT:
        _find_hash_write_log_delete(http_value, "proxy-agent", record, idx);
        break;
    case EM_HTTP_USER_CPU:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->uaCPU, strlen(http_value->uaCPU));
        break;
    case EM_HTTP_USER_OS:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->uaOS, strlen(http_value->uaOS));
        break;

    case EM_HTTP_ACCEPT_RANGES:
        _find_hash_write_log_delete(http_value, "accept-ranges", record, idx);
        break;
    case EM_HTTP_AGE:
        _find_hash_write_log_delete(http_value, "age", record, idx);
        break;
    case EM_HTTP_ETAG:
        _find_hash_write_log_delete(http_value, "etag", record, idx);
        break;
    case EM_HTTP_LOCATION:
        _find_hash_write_log_delete(http_value, "location", record, idx);
        break;
    case EM_HTTP_PROXY_AUTHENTICATE:
        _find_hash_write_log_delete(http_value, "proxy-authenticate", record, idx);
        break;
    case EM_HTTP_INQUIRY_TYPE:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->inquiry_type, strlen(http_value->inquiry_type));
        break;
    case EM_HTTP_PROXY_CONNECT_HOST:
        _find_hash_write_log_delete(http_value, "proxy_connect_host", record, idx);
        break;
    case EM_HTTP_PROXY_CONNECT_PORT:
        _find_hash_write_log_delete(http_value, "proxy_connect_port", record, idx);
        break;
    case EM_HTTP_RETRY_AFTER:
        _find_hash_write_log_delete(http_value, "retry-after", record, idx);
        break;
    case EM_HTTP_SERVER:
        _find_hash_write_log_delete(http_value, "server", record, idx);
        break;
    case EM_HTTP_VARY:
        _find_hash_write_log_delete(http_value, "vary", record, idx);
        break;
    case EM_HTTP_WWW_AUTHENTICATE:
        _find_hash_write_log_delete(http_value, "www-authenticate", record, idx);
        break;
    case EM_HTTP_ALLOW:
        _find_hash_write_log_delete(http_value, "allow", record, idx);
        break;
    case EM_HTTP_CONTENT_ENCODING_S:
        _find_hash_write_log_delete(http_value, "content-encoding", record, idx);
        break;
    case EM_HTTP_CONTENT_LANGUAGE:
        _find_hash_write_log_delete(http_value, "content-language", record, idx);
        break;
    //不清除链表中的“content-length”，在后面删除
    case EM_HTTP_CONTENT_LENGTH:
        if (http_value->req_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->req_len);
        else if (http_value->rsp_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_CONTENT_LENGTH_REQ:
        if (http_value->req_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->req_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);

        if (g_hash_table_lookup(http_value->table, "content-length"))
        {
            g_hash_table_remove(http_value->table, "content-length");
        }
        break;
    case EM_HTTP_CONTENT_LENGTH_RSP:
        if (http_value->rsp_len)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);

        if (g_hash_table_lookup(http_value->table, "content-length"))
        {
            g_hash_table_remove(http_value->table, "content-length");
        }
        break;
    case EM_HTTP_CONTENT_LOCATION:
        _find_hash_write_log_delete(http_value, "content-location", record, idx);
        break;
    case EM_HTTP_CONTENT_MD5:
        _find_hash_write_log_delete(http_value, "content-md5", record, idx);
        break;
    case EM_HTTP_CONTENT_RANGE:
        _find_hash_write_log_delete(http_value, "content-range", record, idx);
        break;
    case EM_HTTP_CONTENT_TYPE:
        if (http_value->is_request)
            _find_hash_write_log_delete(http_value, "content-type", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_EXPIRES:
        _find_hash_write_log_delete(http_value, "expires", record, idx);
        break;
    case EM_HTTP_REFRESH:
        _find_hash_write_log_delete(http_value, "refresh", record, idx);
        break;
    case EM_HTTP_LAST_MODIFIED:
        _find_hash_write_log_delete(http_value, "last-modified", record, idx);
        break;
    case EM_HTTP_X_FORWARDED_FOR:
        _find_hash_write_log_delete(http_value, "x-forwarded-for", record, idx);
        break;
    case EM_HTTP_SET_COOKIE:
        _find_hash_write_log_delete(http_value, "set-cookie", record, idx);
        break;
    case EM_HTTP_SET_COOKIE2:
        _find_hash_write_log_delete(http_value, "set-cookie2", record, idx);
        break;
    case EM_HTTP_X_POWERED_BY:
        _find_hash_write_log_delete(http_value, "x-powered-by", record, idx);
        break;
    case EM_HTTP_APPTYPE:
        //app_type
        if (http_value->app_type[0])
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->app_type, strlen(http_value->app_type));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;

    case EM_HTTP_X_HTTP_TOKEN:
        _find_hash_write_log_delete(http_value, "x-http-token", record, idx);
        break;
    case EM_HTTP_X_COOKIE_TOKEN:
        _find_hash_write_log_delete(http_value, "x-cookie-token", record, idx);
        break;
    case EM_HTTP_X_SINKHOLE:
        _find_hash_write_log_delete(http_value, "x-sinkhole", record, idx);
        break;
    case EM_HTTP_REQ_HEAD_FIELDS:
        if (strlen(http_value->req_heads) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->req_heads, strlen(http_value->req_heads));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_HEAD_FIELDS_MD5:
        if (strlen(http_value->req_heads_md5) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->req_heads_md5, strlen(http_value->req_heads_md5));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_HEAD_FIELDS_NUM:
        if (http_value->req_heads_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->req_heads_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_METHOD_NUM:
        if (http_value->req_method_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->req_method_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_VERSION_NUM:
        if (http_value->req_version_num > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->req_version_num);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_REQ_BODY:
        if (http_value->req_len)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->req_body, http_value->req_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_HEAD_FIELDS:
        if (strlen(http_value->rsp_heads) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_heads, strlen(http_value->rsp_heads));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_HEAD_FIELDS_MD5:
        if (strlen(http_value->rsp_heads_md5) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_heads_md5, strlen(http_value->rsp_heads_md5));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_CACHE_CONTROL:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "cache-control", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_CONNECTION:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "connection", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_PRAGMA:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "pragma", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_ACCEPT_RANGES:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "accept-ranges", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_ACCEPT_CHARSET:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "accept-charset", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_CONTENT_TYPE:
        if (!http_value->is_request)
            _find_hash_write_log_delete(http_value, "content-type", record, idx);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_VERSION:
        if (http_value->PROTOCOL_VAL_LEN(rsp_version) > 0)
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, http_field_array[i].type, http_value->PROTOCOL_VAL_PTR(rsp_version), http_value->PROTOCOL_VAL_LEN(rsp_version));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_BODY:
        if (http_value->rsp_len > 0 && http_value->rsp_body[0] != '\0') {
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_body, http_value->rsp_len);
        }
        else if (strlen(http_value->rsp_body) > 0) {//有http body 没有content-length
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->rsp_body, strlen(http_value->rsp_body));
        }
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_RSP_FULLTEXT_LEN:
        if (!http_value->is_request && http_value->rsp_fulltext_len > 0)
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, (uint32_t)http_value->rsp_fulltext_len);
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_URL:
        if (strlen(http_value->url) > 0)
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->url, strlen(http_value->url));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
#ifdef DPI_SDT_ZDY
    case EM_HTTP_V51:
        if (log_ptr->content_ptr && strlen((const char *)log_ptr->content_ptr)>0 && memcmp(log_ptr->content_ptr,"HTTP_BODY_FORMAT=", strlen("HTTP_BODY_FORMAT=")) != 0) {
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char*)log_ptr->content_ptr, strlen((const char*)log_ptr->content_ptr));
        } else if (http_value->PROTOCOL_VAL_LEN(content))
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char*)http_value->PROTOCOL_VAL_PTR(content), strlen((const char*)http_value->PROTOCOL_VAL_PTR(content)));
        else
            write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    case EM_HTTP_LOCAL_FILENAME:
        memset(local_filename, 0, sizeof(local_filename));
        if (g_config.http_content_limite) { //是否输出实体
            gen_content_file_name(flow, http_value, local_filename, sizeof(local_filename));
            char *suffix = strrchr(local_filename, '.');
            if(suffix && memcmp(suffix, ".bin", 4) == 0) { //uri中没有文件后缀的才按内容识别文件后缀
                if (log_ptr->content_ptr && strlen((const char *)log_ptr->content_ptr)>0 && memcmp(log_ptr->content_ptr,"HTTP_BODY_FORMAT=", strlen("HTTP_BODY_FORMAT=")) != 0) {
                    discern_filetype(log_ptr->content_ptr, strlen((char *)log_ptr->content_ptr), local_filename, sizeof(local_filename));
                } else {
                    discern_filetype(http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content), local_filename, sizeof(local_filename));
                }
            }
        }
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, local_filename, strlen(local_filename));

        if(strlen(local_filename)>0){
            FILE *fp = fopen(local_filename, "w");
            if(fp){
                if (log_ptr->content_ptr && strlen((const char *)log_ptr->content_ptr)>0 && memcmp(log_ptr->content_ptr,"HTTP_BODY_FORMAT=", strlen("HTTP_BODY_FORMAT=")) != 0) {
                    fwrite((const char *)log_ptr->content_ptr, strlen((char *)log_ptr->content_ptr), 1,fp);
                } else {
                    fwrite((const char *)http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content), 1,fp);
                }
                fclose(fp);
                fp = NULL;
            }
        }
        break;
    case EM_HTTP_CONDISP:
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, http_value->conDisp, strlen(http_value->conDisp));
        break;
    case EM_HTTP_REMAINDERLINE:
        //避免在unknown_line中再次出现
        g_hash_table_remove(http_value->table, "head_line");
        //K00  merged unknown line
        struct merge_unknown_line unknown_line;
        memset(&unknown_line, 0, sizeof(unknown_line));
        g_hash_table_foreach(http_value->table, _merge_unknown_line, &unknown_line);
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, unknown_line.unknown_line, unknown_line.index>0?(unknown_line.index - 1):0);
        break;
#endif
    default:
        write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    }

    return 0;
}

int decode_http_v51(struct flow_info *flow, struct http_info *http_value, struct tbl_log *log_ptr)
{
    const char *str = NULL;
    if (http_value->PROTOCOL_VAL_LEN(content) <= 0)
    {
        return 0;
    }

    int content_size = 1024*1024*10; //限定10M
    log_ptr->content_ptr = malloc(content_size);
    memset(log_ptr->content_ptr, 0, content_size);

    str = filetype((const char *)http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
    if(str)
    {
        snprintf((char *)log_ptr->content_ptr, content_size, "HTTP_BODY_FORMAT=%s&LEN=%d", str, http_value->PROTOCOL_VAL_LEN(content));
    }
    else if(http_value->chunk_flag == 1)//避免post数据经YV_HttpPostParse_ParseRAW处理数据量减少
    {
        int content_ptr_len = YV_HttpPostParse_ParseRAW(NULL, (const char *)http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content),
                (char *)log_ptr->content_ptr, content_size);
         //解析后可识别文件格式且是非字符串
         str = filetype((const char *)log_ptr->content_ptr, content_ptr_len);
         if(content_ptr_len>0 && str) {
            http_value->PROTOCOL_VAL_PTR(content) = NULL; //触发HTTP_VALUE_DUP重新申请空间
            HTTP_VALUE_DUP(flow, log_ptr->content_ptr, content_ptr_len, http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
            snprintf((char *)log_ptr->content_ptr, content_size, "HTTP_BODY_FORMAT=%s&LEN=%d", str, content_ptr_len);
         }
    }
    return 0;
}

static int write_http_log(struct flow_info *flow, int direction,  struct http_info *http_value, SdtMatchResult *match_result)
{
    int idx = 0,i=0;
    struct tbl_log *log_ptr=NULL;
    const char *str= NULL;

    if(!http_value){
        return PKT_DROP;
    }

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);

    log_ptr->content_ptr = NULL;

#ifdef DPI_SDT_ZDY
    decode_http_v51(flow, http_value, log_ptr);
#endif

    idx = 0;
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "http");
    for(i=0; i<EM_HTTP_MAX;i++){
        http_field_element(log_ptr, flow, direction, http_value, &idx, i);
    }

#ifndef DPI_SDT_ZDY
    //新增筛选非必须字段
    precord_put_to_layer(log_ptr->record, "http", "uriSearch", string, http_value->uriSearch);
    if (http_value->is_request == 1) {
        precord_put_to_layer(log_ptr->record, "http", "conDispUp", string, http_value->conDisp);
    } else {
        precord_put_to_layer(log_ptr->record, "http", "conDispDown", string, http_value->conDisp);
    }
    precord_put_to_layer(log_ptr->record, "http", "imsi", string, http_value->imsi);

    precord_put_to_layer(log_ptr->record, "http", "imei", string, http_value->imei);
#endif
    //record_show(log_ptr->record);
    log_ptr->log_type    = TBL_LOG_HTTP;
    log_ptr->log_len     = idx;
    log_ptr->thread_id   = flow->thread_id;
    log_ptr->flow        = flow;

    if(log_ptr->content_ptr)
    {
        free(log_ptr->content_ptr);
        log_ptr->content_ptr = NULL;
    }

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

/*************************************************************************************************/
static uint8_t *find_next_boundary(
    struct http_info *http_value, const uint8_t *boundary_content, int boundary_content_len) {
    if (boundary_content_len < 0 || boundary_content == NULL) {
        return NULL;
    }
    // 从每一行开始 找到下一个 --$(boundary)
    int      payload_len = boundary_content_len;
    int      left_len    = boundary_content_len;
    uint8_t *line_start = (uint8_t *)boundary_content;
    //针对每段开头处理 找到 boundary 的那一行
    while (line_start != NULL) {
        // 此行开头是 --$(boundary)
        if (*line_start == '-' && *(line_start + 1) == '-')
        {
            left_len = payload_len - (line_start - boundary_content);
            if (left_len >= http_value->boundary_key_len &&
                0 == memcmp(line_start, http_value->boundary, http_value->boundary_key_len))
                return line_start;
            if (left_len - 2 >= http_value->boundary_key_len &&
                0 == memcmp(line_start + 2, http_value->boundary, http_value->boundary_key_len))
                return line_start;
        }
        int line_offset = _find_single_line_end(line_start, payload_len - (line_start - boundary_content));
        // 还有下一行
        if (line_offset >= 0 && line_offset + 2 < payload_len - (line_start - boundary_content)) {
          line_offset += 2;
          line_start += line_offset;
        } else {
          //这一行是最后一行了!
          line_start = NULL;
        }
    }
    return NULL;
}

static void http_parser_content_disposition_header(
    struct http_info *http_value, const uint8_t *boundary_header, int header_len) {
  //解析boundary头
  // ---------------------------************
  // Content-Disposition: form-data; name="uuid";
  // fd2356c3-12a3-4b56-86c7-89012d345678
  char *content_disposition = NULL;
  int   content_disposition_len = 0;
  // 解析Content-Disposition
  get_data_key_value(
      (const char *)boundary_header, header_len, "Content-Disposition: ", "\r\n", &content_disposition, &content_disposition_len);
  if (content_disposition_len > 0 && (content_disposition_len + strlen(http_value->conDisp) + 1) < sizeof(http_value->conDisp)) {
    if (strlen(http_value->conDisp) > 0) {
      strcat(http_value->conDisp, ",");
    }
    memcpy(http_value->conDisp + strlen(http_value->conDisp), content_disposition, content_disposition_len);
  }
}

void dissect_uri_form_urlencoded(void* user,char* key,int key_len,char* value,int val_len){
  USER * user_ = user;

  struct http_info *http_value = user_->http_value;
  int copy_len = val_len > (int)sizeof(http_value->imsi) ? (int)(sizeof(http_value->imsi) - 1) : val_len;
  if (strncmp(key, "imsi", 4) == 0) {
      memcpy(http_value->imsi, value, copy_len);
  } else if (strncmp(key, "imei", 4) == 0) {
      memcpy(http_value->imei, value, copy_len);
  }
}

static void http_parse_form_urlencoded(struct http_info *http_value,const uint8_t *content, int content_len){
    char *p_param = (char*)content;
    if (!p_param){
        return;
  }
  USER user;
  user.session = NULL;
  user.http_value = http_value;
  get_parameters_keys_values(p_param,content_len,&user,dissect_uri_form_urlencoded);
}

void set_boundary_key_info(struct http_info *http_value,char* bounday_ptr,int boundary_len){
    memcpy(http_value->boundary,bounday_ptr,boundary_len);
    http_value->boundary_key_len = boundary_len;
}

static uint8_t * http_parser_boundary_content(struct http_info *http_value,const uint8_t *payload, int payload_len,uint32_t *content_len){
    // 以--$(boundary_key)为开头
    *content_len = 0;
    uint32_t offset = 0;
    uint8_t * boundary_p = (uint8_t *)payload;
    int total_len  = payload_len;
    int Content_Disposition_len = 0;
    uint8_t * boundary_file_start = NULL;
    uint8_t * boundary_file_end = NULL;
    int remaining = payload_len;
    while (remaining) {
        // 以--$(boundary_key)为开头
        // 以--$(boundary_key)--为结尾
        if(!boundary_p){
          //上一次处理是最后一行
          break;
        }
        // 找 boundary_key 后的第一个双换行符
        Content_Disposition_len = _find_empty_line(boundary_p, remaining);
        if (Content_Disposition_len >= 0) {
          http_parser_content_disposition_header(http_value, boundary_p, Content_Disposition_len);
          Content_Disposition_len += 4;
          //每次更新file_start 当处理到最后一个content_disposition时 为真实的 file_start
          boundary_file_start = boundary_p + Content_Disposition_len;
        } else {
          //最后一行
          if ((boundary_p + 2 + http_value->boundary_key_len + 1) >= payload + payload_len)
            break;

          if (*boundary_p == '-' && *(boundary_p + 1) == '-' && *(boundary_p  + 2 + http_value->boundary_key_len) == '-' &&
              *(boundary_p + 2 + http_value->boundary_key_len + 1) == '-')
                boundary_file_end = boundary_p;
          break;
        }
        remaining -= Content_Disposition_len;
        boundary_p = find_next_boundary(http_value, boundary_file_start, remaining);
        if(boundary_p &&boundary_file_start){
            remaining -= (boundary_p - boundary_file_start);
        }
    }
    if (boundary_file_end != NULL && boundary_file_start != NULL && boundary_file_end > boundary_file_start) {
      *content_len = boundary_file_end - boundary_file_start;
    }
    return boundary_file_start;
}

#define CHUNK_SIZE_MAX 32

static void http_parser_chunked_content(const uint8_t *chunked_content, uint32_t chunked_content_len,
        uint8_t *content, uint32_t *content_len)
{
    uint32_t offset = 0;
    int linelen;
    uint32_t max_len = *content_len;
    char chunk_string[CHUNK_SIZE_MAX];
    uint32_t chunk_size;

    *content_len = 0;

    while (offset < chunked_content_len) {
        linelen = find_packet_line_end(chunked_content + offset, chunked_content_len - offset);
        if (linelen <= 0 || linelen >= CHUNK_SIZE_MAX) {
            /* Can't get the chunk size line */
            break;
        }

        strncpy(chunk_string, (const char *)chunked_content + offset, linelen);
        chunk_string[linelen] = 0;
        chunk_size = strtol(chunk_string, NULL, 16);

        offset += linelen + 2;
        if (chunk_size > chunked_content_len - offset) {
            chunk_size = chunked_content_len - offset;
        }

        if (*content_len + chunk_size < max_len) {
            memcpy(content + *content_len, chunked_content + offset, chunk_size);
            *content_len += chunk_size;
        }

        offset += chunk_size + 2;
        if (offset >= chunked_content_len)
            break;

        if (chunk_size == 0)
            break;
    }

    return;
}

#define GET_DEV_INFO(devArr, arrlen, devFlag)    do{                                    \
    char* tmp0 = strcasestr((char *)value->ptr, (devFlag));                            \
    if(NULL != tmp0)                                                                \
    {                                                                                \
        char* tmp1 = strstr(tmp0, ";");                                                \
        char* tmp2 = strstr(tmp0, ")");                                                \
        if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2))                            \
            memcpy((devArr), tmp0, (tmp2-tmp0<(arrlen) ? tmp2-tmp0:(arrlen)-1));    \
        else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2)                        \
            memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));    \
        else if(NULL == tmp1 && NULL == tmp2)                                        \
        {                                                                            \
            tmp1 = strstr(tmp0,"\r\n");                                                \
            if(NULL != tmp1)                                                        \
                memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));\
        }                                                                            \
    }                                                                                \
}while(0)

size_t ws_base64_decode_inplace(char *s)
{
    static const char b64[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\r\n";
    int bit_offset, byte_offset, idx, i;
    unsigned char *d = (unsigned char *)s;
    char *p;
    int  cr_idx;
    // we will allow CR and LF - but ignore them
    cr_idx = (int)(strchr(b64, '\r') - b64);
    i = 0;
    while (*s && (p = strchr(b64, *s))) {
        idx = (int)(p - b64);
        if (idx < cr_idx) {
            byte_offset = (i * 6) / 8;
            bit_offset = (i * 6) % 8;
            d[byte_offset] &= ~((1 << (8 - bit_offset)) - 1);
            if (bit_offset < 3) {
                d[byte_offset] |= (idx << (2 - bit_offset));
            } else {
                d[byte_offset] |= (idx >> (bit_offset - 2));
                d[byte_offset + 1] = 0;
                d[byte_offset + 1] |= (idx << (8 - (bit_offset - 2))) & 0xFF;
            }
            i++;
        }
        s++;
    }
    d[i*3/4] = 0;
    return i*3/4;
}

static void dissect_http_authorization(struct http_info *http_value, struct http_session *session)
{
    struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "proxy-authorization");
    if(!value)
        value = (struct header_value *)g_hash_table_lookup(http_value->table, "authorization");
    if(value)
    {
        int copy_len = value->len >= sizeof(session->auth) ? sizeof(session->auth) - 1 : value->len;
        memcpy(session->auth, value->ptr, copy_len);
        session->auth[copy_len] = 0;
        const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
        if(tmp)
        {
            int len = tmp - value->ptr;
            const uint8_t *head;
            size_t login_len;
            memcpy(http_value->proxy_type,  value->ptr, DPI_MIN(len, TYPE_LEN-1));
            if(strncasecmp(http_value->proxy_type, "Basic", 5)  == 0){
                memcpy(http_value->proxy_login, tmp + 1,    DPI_MIN(value->len - len - 1, LOGIN_LEN-1));
                login_len = ws_base64_decode_inplace(http_value->proxy_login);
                head = memchr(http_value->proxy_login, ':', login_len);
                if (head) {
                    http_value->username_val_ptr = (const uint8_t*)http_value->proxy_login;
                    http_value->username_val_len = head - (const uint8_t*)http_value->proxy_login;
                }
            }
            else if (strncasecmp(http_value->proxy_type, "Digest", 6) == 0) {
                if ((head = memstr(tmp, " username=\"", 11))) {
                    tmp += 11;
                    if ((head = memstr(tmp, "\"", value->len - (tmp - value->ptr)))) {
                        http_value->username_val_ptr = tmp;
                        http_value->username_val_len = head - tmp;
                    }
                }
            }
        }
    }
}

static void dissect_http_authenticate(struct http_info *http_value)
{
   //质询认证方案："basic" 的截取
   struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "www-authenticate");
   if(!value)
           value = g_hash_table_lookup(http_value->table, "proxy-authenticate");
   if(value)
   {
       const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
       if(tmp)
       {
           int len = tmp - value->ptr;
           memcpy(http_value->inquiry_type, value->ptr, len >= TYPE_LEN ? TYPE_LEN - 1 : len);
       }
   }
}

static void dissect_http_user_agent(struct http_info *http_value)
{
    struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "user-agent");
    if(!value)
    {
        return;
    }

    http_value->user_agent_num++;

    //GET_DEV_INFO(http_value->uaCPU, CPU_LEN, "cpu");
    char* tmp0 = g_strstr_len((const char *)value->ptr, value->len, "cpu");
    if(NULL != tmp0)
    {
        char* tmp1 = dpi_strnstr_kmp(tmp0, ((uint8_t *)tmp0 - value->ptr), ";");
        char* tmp2 = dpi_strnstr_kmp(tmp0, ((uint8_t *)tmp0 - value->ptr), ")");
        if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2))
            memcpy(http_value->uaCPU, tmp0, (tmp2-tmp0<CPU_LEN ? tmp2-tmp0:CPU_LEN-1));
        else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2)
            memcpy(http_value->uaCPU, tmp0, (tmp1-tmp0<CPU_LEN ? tmp1-tmp0:CPU_LEN-1));
        else if(NULL == tmp1 && NULL == tmp2)
        {
            tmp1 = dpi_strnstr_kmp(tmp0, ((uint8_t *)tmp0 - value->ptr), "\r\n");
            if(NULL != tmp1)
                memcpy(http_value->uaCPU, tmp0, (tmp1-tmp0<CPU_LEN ? tmp1-tmp0:CPU_LEN-1));
        }
    }

    //该数组元素为设备系统标识,最后一个元素必须是NULL, 如果新增系统标识,则直接在后面的NULL位置上依次添加
    //注意：此处如果有os标识，则os后面必须加一个空格
    const char *devFlags[] = {"linux", "windows ", "windows/", "iphone ", "android", "mac", NULL, NULL, NULL, NULL};
    int i = 0;
    for(i=0; devFlags[i]; i++)
    {
        //GET_DEV_INFO(http_value->uaOS, OS_LEN, devFlags[i]);
        char* tmp0 = g_strstr_len((const char *)value->ptr, value->len, devFlags[i]);
        if(tmp0)
        {
            char* tmp1 = dpi_strnstr_kmp(tmp0, ((uint8_t *)tmp0 - value->ptr), ";");
            char* tmp2 = dpi_strnstr_kmp(tmp0, ((uint8_t *)tmp0 - value->ptr), ")");
            if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2)){
                memcpy(http_value->uaOS, tmp0, (tmp2-tmp0<OS_LEN ? tmp2-tmp0:OS_LEN-1));
            }
            else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2){
                memcpy(http_value->uaOS, tmp0, (tmp1-tmp0<OS_LEN ? tmp1-tmp0:OS_LEN-1));
            }
            else if(NULL == tmp1 && NULL == tmp2){
                tmp1 = dpi_strnstr_kmp(tmp0, ((uint8_t *)tmp0 - value->ptr), "\r\n");
                if(NULL != tmp1)
                    memcpy(http_value->uaOS, tmp0, (tmp1-tmp0<OS_LEN ? tmp1-tmp0:OS_LEN-1));
            }
            if(http_value->uaOS[0])
                break;
        }
    }
}

static void get_http_host(struct http_info *http_value, struct http_session *session)
{
    //缓存host字段到session中
    struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "host");
    if (value)
    {
        size_t copy_len = value->len >= sizeof(session->host) ? sizeof(session->host) - 1 : value->len;
        memcpy(session->host, value->ptr, copy_len);
        session->host[copy_len] = 0;
        g_hash_table_remove(http_value->table, "host");

        //雅虎认证
        if(copy_len > 9 && strcmp(session->host + copy_len - 9, "yahoo.com") == 0){
            value = g_hash_table_lookup(http_value->table, "head_line");
            if(!memchr(value->ptr, '?', value->len))
                return;
            const uint8_t *login  = memstr(value->ptr, "login=", value->len);
            const uint8_t *passwd = memstr(value->ptr, "passwd=", value->len);
            if(login && passwd)
            {
                uint8_t *login_end  = memchr(login, '&', value->len);
                uint8_t *passwd_end = memchr(passwd, '&', value->len);
                if(login_end && passwd_end){ //FORMAT   login:passwd
                    strcpy(http_value->proxy_type, "Yahoo");
                    uint8_t off = DPI_MIN(login_end - login - 6, LOGIN_LEN - 1);
                    memcpy(http_value->proxy_login, login+6, off);
                    if(off < LOGIN_LEN - 1)
                        http_value->proxy_login[off++] = ':';
                    if(off + passwd_end - passwd - 7 < LOGIN_LEN - 1)
                        memcpy(http_value->proxy_login + off, passwd + 7, passwd_end - passwd - 7);
                }
            }
        }
    }
}

void uri_decode(char *src, char *dst, int src_len, int dst_len) {
    char a, b;
    int len = src_len;
    if (src_len > dst_len)
        return;
    while (len>=0) {
        if(dst_len == 1){
          // 1 for '\0'
            break;
        }
        if ((*src == '%') && ((a = src[1]) && (b = src[2])) && (isxdigit(a) && isxdigit(b))) {
            if (a >= 'a')
                a -= 'a' - 'A';
            if (a >= 'A')
                a -= 'A' - 10;
            else
                a -= '0';
            if (b >= 'a')
                b -= 'a' - 'A';
            if (b >= 'A')
                b -= 'A' - 10;
            else
                b -= '0';
            *dst++ = 16 * a + b;
            src += 3;
            len -= 3;
            dst_len--;
        } else {
            *dst++ = *src++;
            len--;
            dst_len--;
        }
    }
    *dst++ = '\0';
}

static void get_http_uri(struct http_info *http_value, struct http_session *session)
{
    if(session==NULL){
        return;
    }
    if(http_value->PROTOCOL_VAL_LEN(uri)==0){
        return;
    }

    size_t copy_len=http_value->PROTOCOL_VAL_LEN(uri);

    if(session->uri!=NULL){
        free(session->uri);
        session->uri=NULL;
    }

    session->uri = malloc(copy_len + 1);
    if (session->uri == NULL) {
        DPI_LOG(DPI_LOG_ERROR, "malloc failed!");
        return;
    }
  uri_decode((char*)http_value->PROTOCOL_VAL_PTR(uri),session->uri,copy_len-1,copy_len + 1);
  if(session->uri[0] == '0'){
      strncpy(session->uri, (const char *)http_value->PROTOCOL_VAL_PTR(uri), copy_len+1);
      return;
  }
    session->uri[copy_len] = 0;

    http_value->uri_num++;  // uri 数量

    char *p_param = memchr(session->uri, '?', copy_len);

    // 获取uri path
    {
        //先初始化，避免因历史数据产生脏数据
        memset(session->uri_path, 0, sizeof(session->uri_path));
        if (p_param) {
            memcpy(session->uri_path, session->uri, p_param - session->uri);
        }
        else {
            memcpy(session->uri_path, session->uri, copy_len);
        }

        session->uri_path_num++;
    }
}



static void geturiSearch_v2(struct http_info *http_value,struct http_session * session,uint8_t is_http_request,char* key,int key_len,char* value ,int value_len){
  //只取请求中的key
  if(!http_value->is_request && http_value->is_search >= 0)
      return;
  //如果是搜索引擎
  if(http_value->is_search == 0){
    for(int i = 0; searchHost[i]!= NULL; i++){
        if(NULL!= strstr(session->host , searchHost[i])){
          http_value->is_search = 1;
          break;
        }
        if(NULL == searchHost[i+1]){
          http_value->is_search = -1;
          // 只对host进行一次匹配
          return;
        }
    }
  }
  if(http_value->is_search == 2){
    // 已经找到
    // 暂时只进行一次赋值，如有要求再取消该条件
    return;
  }
  for(int i =0; searchKey[i]!= NULL; i++){
    char*str = strstr(key, searchKey[i]);
    if (str!= NULL && (str-key)< key_len) {

      if(0 == strlen(http_value->uriSearch )){
        value_len = DPI_MIN(value_len, (int)sizeof(http_value->uriSearch)-1);
        memcpy(http_value->uriSearch , value , value_len);
      }else {
        http_value->is_search = 2;
        value_len = DPI_MIN(value_len, (int)(sizeof(http_value->uriSearch) - (int)strlen(http_value->uriSearch) - 2));
        if (value_len <= 0) break;
        strcat(http_value->uriSearch, ",");
        strncat(http_value->uriSearch, value, value_len);
      }
    }
  }
}

static void geturiSearch(struct http_info *http_value,struct http_session * session,uint8_t is_http_request,char* uri_key,char* p_start,char*p_next,int copy_len){
  //只取请求中的key
  if(!is_http_request && http_value->is_search >= 0)
      return;
  //如果是搜索引擎
  if(http_value->is_search == 0){
    for(int i = 0; searchHost[i]!= NULL; i++){
        if(NULL!= strstr(session->host , searchHost[i])){
          http_value->is_search = 1;
          break;
        }
        if(NULL == searchHost[i+1]){
          http_value->is_search = -1;
          // 只对host进行一次匹配
          return;
        }
    }
  }
  if(http_value->is_search == 2){
    // 已经找到
    // 暂时只进行一次赋值，如有要求再取消该条件
    return;
  }
  for(int i =0; searchKey[i]!= NULL; i++){
    char*str = strstr(uri_key, searchKey[i]);
    if (str!= NULL) {
      size_t val_len = 0;
      if(NULL != p_next){
        val_len = p_next - p_start - copy_len;
      }else {
        val_len = strlen(p_start) - copy_len;
      }
      val_len = val_len > (sizeof(http_value->uriSearch) - strlen(http_value->uriSearch))
                    ? (sizeof(http_value->uriSearch) - strlen(http_value->uriSearch) - 1)
                    : val_len;
      if(0 == strlen(http_value->uriSearch )){
        memcpy(http_value->uriSearch , p_start + copy_len , val_len);
      }else {
        strcat(http_value->uriSearch ,",");
        strncat(http_value->uriSearch, p_start+copy_len, val_len);
        http_value->is_search = 2;
      }
    }
  }
}


void dissect_uri_keys(void* user,char* key,int key_len,char* value,int val_len){
  USER * user_ = user;

  struct http_info *http_value = user_->http_value;
  struct http_session *session = user_->session;
  geturiSearch_v2(http_value,session,http_value->is_request,key,key_len,value,val_len);
  if(session->uri_keys_num >= URI_KEY_MAX){
    return;
  }
  memcpy(session->uri_keys[session->uri_keys_num],key,key_len);
  session->uri_keys_num ++;
}

static void get_uri_keys_v2(struct http_info *http_value,struct http_session * session,uint8_t is_http_request){
      //  获取uri key
    // ex: /WebResource.axd?d=pynGkmcFUV13He1Qd6_TZLRDLpL6gmauvyvofGdAzgtAYfxCeGItmqtnuL8g29HqNiBfEA2&t=635802961220000000
    memset(session->uri_keys, 0, sizeof(session->uri_keys));
    session->uri_keys_num = 0;

    char *p_param = memchr(session->uri, '?', strlen(session->uri));

    if (!p_param)
        return;

    int i = 0;
    char *p_start = p_param + 1;
  USER user;
  user.session = session;
  user.http_value = http_value;
  get_parameters_keys_values(p_start,strlen(p_start),&user,dissect_uri_keys);
}
static void get_uri_keys(struct http_info *http_value,struct http_session * session,uint8_t is_http_request) {
    //  获取uri key
    // ex: /WebResource.axd?d=pynGkmcFUV13He1Qd6_TZLRDLpL6gmauvyvofGdAzgtAYfxCeGItmqtnuL8g29HqNiBfEA2&t=635802961220000000
    memset(session->uri_keys, 0, sizeof(session->uri_keys));
    session->uri_keys_num = 0;

    char *p_param = memchr(session->uri, '?', strlen(session->uri));

    if (!p_param)
        return;

    int i = 0;
    char *p_start = p_param + 1;
    char *p_eq;
    //        int len = copy_len - (p_start - session->uri);
  // p_start 始终为&后的一个字节
  while (p_start) {
    char    *p_next = memchr(p_start, '&', strlen(p_start));
    uint64_t copy_len = 0;
    if (p_next) {
        p_eq = memchr(p_start, '=', p_next - p_start);
        copy_len = p_eq - p_start;
    } else {
        //最后一个key
        p_eq = memchr(p_start, '=', strlen(p_start));
        copy_len = p_eq - p_start;
    }
    if (copy_len > 0) {
        memcpy(session->uri_keys[i++], p_start,
            copy_len > sizeof(session->uri_keys[0]) ? sizeof(session->uri_keys[0]) - 1 : copy_len);
        geturiSearch(http_value,session,is_http_request,session->uri_keys[i-1],p_start,p_next,copy_len);
    }

    if (p_next) {
        p_start = p_next + 1;
    } else {
        break;
    }

    // 最多装载50个key
    if (i >= URI_KEY_MAX)
            break;
  }

    session->uri_keys_num = i;
}

static void get_via_num(struct http_info *http_value) {

    http_value->via_num = 0;

    struct header_value *value = (struct header_value *)g_hash_table_lookup(http_value->table, "via");
    if (value)
    {
        char tmp[2048];

        int len = DPI_MIN(sizeof(tmp)-1, value->len);
        memset(tmp, 0, sizeof(tmp));
        strncpy(tmp, (const char *)value->ptr, len);

        char *end = NULL;
        const char *token = ",";
        char *p_next = NULL;

        p_next = strtok(tmp, token);
        while (p_next) {

            http_value->via_num++;
            if (p_next[0] == '\0')
                break;

            p_next = strtok(NULL, token);
        }
    }
}

const char *strrchr_until(const char *pBegin, char key, char untilKey)
{
    const char *pLastKey = NULL;
    for (const char *p = pBegin;
        *p && *p != untilKey;
        p++)
    {
        if (*p == key)
        {
            pLastKey = p;
        }
    }

    return pLastKey ? pLastKey : NULL;
}

#define MD5_DIGEST_LENGTH 16
static int http_get_method_file_suffix(const char *uri,char *child_dir,char *file_raw_name, char *suffix, char *md5HEX, int *question_mark)
{
    if(uri==NULL || strlen(uri)==0 || suffix==NULL){
        return -1;
    }
    int i   = 0;
    int j   = 0;
    char    *p=NULL;
    int     check_len=0;
    char    http_getname[COMMON_FILE_NAME]={0};
    int     len=0;

    p=strstr(uri,"?");
    if(p!=NULL){
        check_len = p-uri;
        *question_mark=1;
    }else{
        #if 0
        char *q=memmem(uri,strlen(uri),"http://",strlen("http://"));
        if(q!=NULL){
            check_len=q-uri;
        }else{
            check_len = strlen(uri);
        }
        #else
        check_len = strlen(uri);
        #endif
    }

    char    md5sum[COMMON_FILE_NAME]={0};

    for(i=check_len-1;i>=0;i--){
        if(uri[i]=='/'){
            if(i+1>COMMON_FILE_NAME){
                return -1;
            }else{
                strncpy(child_dir, uri, i+1);
            }

            len=check_len-i-1;
            if(len>=COMMON_FILE_NAME)
                len=COMMON_FILE_NAME;
            strncpy(http_getname, &uri[i+1],len);

            break;
        }
    }

    /*
    if(strlen(child_dir)>strlen("http\:/")){
        char *p=NULL;
        p=strstr(child_dir,"http\:/");
        if(p!=NULL){
            int tmp_len=p-child_dir;
            memcpy(child_dir,&child_dir[strlen("http\:/")+tmp_len],strlen(child_dir)-strlen("http\:/")-tmp_len);
        }
    }
    */

    len=0;
    // 获取需要计算md5的字符串
    // char path_compo[1024] = {0};
    const gchar *path_compo = strrchr_until(uri, '/', '?');
    if (path_compo) {
        size_t len_path_compo = strlen(path_compo);
        if (path_compo[len_path_compo - 1] == '/') {
            static const gchar INDEX[] = "index";
            path_compo = INDEX;
        }
        MD5((unsigned char*)path_compo, strlen(path_compo), (uint8_t *)md5sum);
        bintohex((const unsigned char *)md5sum, MD5_DIGEST_LENGTH, md5HEX, sizeof(md5HEX));
        //printf("[debug by liugh test uri and md5]uri:%s, path_compo:%s, md5:%s\n",uri,path_compo,md5HEX);
    }

    //printf("\n--1.uri:[%s] name:[%s]\n",uri,http_getname);
    if(0==strlen(http_getname)){
        strcpy(suffix, ".html");
        strcpy(file_raw_name,"index");
        return 1;
    }

    int start=0;
    int end=0;

    uint8_t flag=0;
    for(i=strlen(http_getname)-1;i>=0;i--){
        if(http_getname[i]=='.'){
            start=i;
            flag=1;
            strncpy(file_raw_name, http_getname, (i >= COMMON_FILE_NAME)?COMMON_FILE_NAME - 1 :i);
            break;
        }
    }

    if(1==flag){
        for(j=start+1;j<(int)strlen(http_getname);j++){
            if(!isalpha(http_getname[j])){
                if(!isdigit(http_getname[j])){
                    end=j;
                    break;
                }else{
                    if(j==(int)strlen(http_getname)-1){
                        end=j+1;
                    }
                    continue;
                }
            }
            end=j+1;
        }
    }else{
        strncpy(file_raw_name, http_getname, strlen(http_getname));
    }

    len=end-start;
    //printf("\t--2.start=%d,end=%d\n",start, end);
    if(start>0 && len>0){
        if(len>COMMON_FILE_NAME)
            len=COMMON_FILE_NAME;
        strncpy(suffix, &http_getname[start],len);
        suffix[len] = 0;
    }else{
        strcpy(suffix, ".bin");
    }

    return 1;
}

static void gen_content_file_name(struct flow_info *flow, struct http_info *http_value, char *filename, int filename_len)
{
    if(!flow || !flow->app_session)
        return;
    if(!http_value)
        return;
    if(!filename || filename_len<=0)
        return;
    if(http_value->PROTOCOL_VAL_LEN(content) <= 0)
        return;

    char    md5HEX[48]={0};
    char    suffix[COMMON_FILE_NAME]={0};
    char    child_dir[COMMON_FILE_PATH]={0};
    char    file_writing_dir[COMMON_FILE_PATH]={0};
    char    file_raw_name[COMMON_FILE_NAME]={0};
    int     question_mark=0;
    int     ret;
    struct http_session *session = (struct http_session *)flow->app_session;
    //使用默认后缀，避免依据特征修改后缀有问题；
    snprintf(suffix,sizeof(suffix),".bin");
    if(session->uri){
        ret=http_get_method_file_suffix(session->uri, child_dir,file_raw_name, suffix, md5HEX, &question_mark);
        if(ret<0){
            return;
        }
     }
     bzero(filename, filename_len);
     snprintf(file_writing_dir, COMMON_FILE_PATH, "%s/%s/",
                                 g_config.tbl_out_dir, tbl_log_array[TBL_LOG_HTTP].protoname);
     if (access(file_writing_dir, F_OK))
         mkdirs(file_writing_dir);

     struct timeval tv;
     gettimeofday(&tv, NULL);
     snprintf(filename, filename_len, "%s/http_%s_%06ld_%03d", file_writing_dir, time_to_datetime(g_config.g_now_time), tv.tv_usec, flow->thread_id);
     if (g_config.show_task_id) {
         strcat(filename, "_");
         strcat(filename, g_config.task_id);
     }
     strcat(filename, suffix);
}

static int
dissect_http(struct flow_info *flow, const uint8_t *payload, const uint32_t payload_len)
{
    if(payload == NULL || payload_len < 3)
        return 0;

    int     ret=0;
    uint8_t chunked = 0;
    uint8_t boundary = 0;
    uint8_t formdata = 0;
    uint8_t form_urlencoded = 0;
    uint8_t is_http_request = 0;
    uint8_t rsp_fulltext_len = 0;

    uint32_t uri_start = 0;
    uint32_t http_content_len;
    uint32_t http_header_content_len;
    gpointer _value;
    struct http_session *session;
    struct header_value *value;
    struct http_info *http_value = NULL;

    flow->match_data_len = payload_len;

    session = (struct http_session *)flow->app_session;

    //判断是请求 还是 响应
    if (payload_len >= 7 && strncasecmp((const char *)payload, "HTTP/1.", 7) == 0)
    {
        is_http_request = 0;
        session->direction[FLOW_DIR_DST2SRC] += 1;
    }
    else
    if (payload_len >= 10)
    {
        for(int i = 0; HTTP_METHOD[i].method; i++)
        {
            if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len))
            {
                uri_start = HTTP_METHOD[i].len;
                is_http_request = 1;
                break;
            }
        }
        if(!is_http_request)
        {
            return PKT_DROP;
        }
        session->direction[FLOW_DIR_SRC2DST] += 1;
    }
    else{
        return PKT_DROP;
    }

    //获取 对应方向的 数据缓存区
    http_value = session->http_value + is_http_request;

    //判断会话是不是已经缓存了 HTTP的请求数据(HTTP是 基于RESP输出的)
    if(http_value->table && is_http_request)
    {
        high_app_proto_http_identify(flow, http_value);
        write_http_log(flow, FLOW_DIR_SRC2DST, http_value, NULL);
        g_hash_table_destroy(http_value->table);
        http_value->table = NULL;
    }

    memset(http_value, 0, sizeof(struct http_info));

    http_value->is_request = is_http_request;

    //解析头部行，并找空行
    parse_http_line_info(flow, payload, payload_len, http_value);
    if (!http_value->table) {
        DPI_LOG(DPI_LOG_DEBUG, "hash table is null");
        return PKT_OK;
    }

    //查看头部行的transfer-encoding是否是chunked
    _value = g_hash_table_lookup(http_value->table, "transfer-encoding");
    if (_value) {
        char transfer[64] = {0};
        value = (struct header_value *)_value;
        memcpy(transfer, value->ptr, value->len >= sizeof(transfer) ? sizeof(transfer) - 1 : value->len);
        if (strcasestr(transfer, "chunked")) {
            chunked = 1;
        }
    }

    if(is_http_request)//只属于http请求报文的字段
    {
        get_http_host(http_value, session);
        if(g_config.http.http_exquisite_switch){
          dissect_http_user_agent(http_value);
        }
        dissect_http_authorization(http_value, session);
    }
    else               //只属于http响应报文的字段
    {
        dissect_http_authenticate(http_value);
    }
    if(g_config.http.http_exquisite_switch){
        get_via_num(http_value);    // 获取via个数
    }

    /* =============== add by liugh =============== */
    _value = g_hash_table_lookup(http_value->table, "content-type");
    if (_value) {
        char transfer[512] = {0};
        value = (struct header_value *)_value;
        memcpy(transfer, value->ptr, value->len >= sizeof(transfer) ? sizeof(transfer) - 1 : value->len);
        if(strcasestr(transfer, "multipart/form-data; boundary")){
            int   boundary_len = 0;
            char *boundary_ptr = NULL;
            get_data_key_value(transfer, strlen(transfer), "boundary=", 0, &boundary_ptr, &boundary_len);
            if (0 != boundary_len && NULL != boundary_ptr) {
                set_boundary_key_info(http_value, boundary_ptr, boundary_len);
              boundary = 1;
              formdata = 1;
            }
        }else if (strcasestr(transfer, "application/x-www-form-urlencoded")) {
            form_urlencoded = 1;
        } else if ((!strcmp(transfer, "image") || !strcmp(transfer, "video"))) {
            rsp_fulltext_len = 1;
        }
        if( 0==is_http_request && 0==_http_content_type_filter(transfer)){  /* 只对S2C生效 */
            g_hash_table_destroy(http_value->table);
            http_value->table = NULL;
            return PKT_DROP;
        }
    }else if(0==is_http_request && 1==g_config.http.drop_no_content_type){  /* no content-type丢弃与否也只对S2C生效 */
        g_hash_table_destroy(http_value->table);
        http_value->table = NULL;
        return PKT_DROP;
    }

    //获取实际的内容长度
    if (http_value->empty_line_position > 0 && http_value->empty_line_position < payload_len - 2) {
        http_content_len = payload_len - http_value->empty_line_position - 2;
    } else
        http_content_len = 0;

    _value = g_hash_table_lookup(http_value->table, "head_line");
    if (!_value) {
        g_hash_table_destroy(http_value->table);
        http_value->table = NULL;
        return PKT_OK;
    }

    value = (struct header_value *)_value;
    if (is_http_request && value->len > (9 + uri_start)) {
        HTTP_VALUE_DUP(flow, value->ptr, uri_start - 1, http_value->PROTOCOL_VAL_PTR(method), http_value->PROTOCOL_VAL_LEN(method));
        http_value->req_method_num++;

        HTTP_VALUE_DUP(flow, value->ptr+uri_start, value->len - (uri_start + 9), http_value->PROTOCOL_VAL_PTR(uri), http_value->PROTOCOL_VAL_LEN(uri));

        //从 PKT 中取值
        const unsigned char *http_uri= value->ptr+uri_start + http_value->PROTOCOL_VAL_LEN(uri) + 1;
        HTTP_VALUE_DUP(flow, http_uri, 8, http_value->PROTOCOL_VAL_PTR(version), http_value->PROTOCOL_VAL_LEN(version));

        http_value->req_version_num++;

        if(memcmp(http_value->PROTOCOL_VAL_PTR(version), "HTTP/1.", 7)){
             session->direction[FLOW_DIR_SRC2DST] -= 1;
            //  DPI_LOG(DPI_LOG_DEBUG, "not http expect header");
             goto exit;
        }
        if(g_config.http.http_exquisite_switch){
            get_http_uri(http_value, session); /* 获取http请求uri，回填到响应tbl中 */
            get_uri_keys_v2(http_value,session,is_http_request); // 获取http uri中的key的数据和数量
            if (strlen(session->uri) > 0 && strlen(session->host) > 0) {
              snprintf(http_value->url, sizeof(http_value->url), "%s%s", session->host, session->uri);
            }
        }
    } else if (0 == is_http_request && value->len > 12) {

        int version_len = strlen("HTTP/1.1");
        int status_code_len = strlen("HTTP/1.1 200");
        HTTP_VALUE_DUP(flow, value->ptr, version_len, http_value->PROTOCOL_VAL_PTR(rsp_version), http_value->PROTOCOL_VAL_LEN(rsp_version));
        HTTP_VALUE_DUP(flow, value->ptr + version_len +1, 3, http_value->PROTOCOL_VAL_PTR(code), http_value->PROTOCOL_VAL_LEN(code));
        HTTP_VALUE_DUP(flow, value->ptr + status_code_len, value->len - status_code_len, http_value->PROTOCOL_VAL_PTR(response_code), http_value->PROTOCOL_VAL_LEN(response_code));
        http_value->status = atoi((const char *)http_value->PROTOCOL_VAL_PTR(code));

        //HTTP response status
        if(g_config.http_status_whitelist[0] != '*'){
            char status[4];
            memcpy(status, http_value->PROTOCOL_VAL_PTR(code), 3);
            status[3] = 0;
            if(!strstr(g_config.http_status_whitelist, status))
                goto exit;
        }
    }

    ////////////////////////////
    // "content-length" 基于报文数据解析, 不是实际HTTP Body长度
    int http_content_length = 0;
    struct header_value *content_length_value = (struct header_value *)g_hash_table_lookup(http_value->table, "content-length");
    if(content_length_value)
    {
        http_content_length = strtol((const char*)content_length_value->ptr, NULL, 10);
    }
    if (http_value->PROTOCOL_VAL_LEN(method) > 0)
    {
        http_value->req_len = http_content_length; //记录下请求的长度
    }
    else if (http_value->PROTOCOL_VAL_LEN(code) > 0)
    {
        http_value->rsp_len = http_content_length; //记录下请求的长度
    }
    ///////////////////////////


    if (http_value->PROTOCOL_VAL_LEN(method) > 0 || http_value->PROTOCOL_VAL_LEN(code) > 0) {
        uint8_t chunked_content[g_config.http.http_file_size];
        uint32_t chunked_content_len = g_config.http.http_file_size;

        if (http_value->empty_line_position > 0 && http_value->empty_line_position < payload_len - 2) {
            int http_content_len = payload_len - http_value->empty_line_position - 2;
            if (http_content_len >g_config.http.http_file_size - 8)
                http_content_len = g_config.http.http_file_size - 8;

            HTTP_VALUE_DUP(flow, payload+http_value->empty_line_position + 2, http_content_len, http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
            //内容是chunked的报文，做chunked解析
            if (chunked) {
                http_parser_chunked_content(http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content),
                        chunked_content, &chunked_content_len);
                if(chunked_content_len>0 && chunked_content_len < http_value->PROTOCOL_VAL_LEN(content))
                    http_value->PROTOCOL_VAL_PTR(content) = NULL; //触发HTTP_VALUE_DUP重新申请空间
                HTTP_VALUE_DUP(flow, chunked_content, chunked_content_len, http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
                http_value->chunk_flag=1;
            }
            if(boundary){
                uint8_t  boundary_content[g_config.http.http_file_size];
                uint32_t boundary_content_len = g_config.http.http_file_size;
                const uint8_t *content_p = http_value->PROTOCOL_VAL_PTR(content);
                int32_t content_l = http_value->PROTOCOL_VAL_LEN(content);
                const uint8_t *file_data_start =  http_parser_boundary_content(http_value, content_p, content_l, &boundary_content_len);
                boundary_content_len = DPI_MIN(((uint32_t)g_config.http.http_file_size-1), ((uint32_t)g_config.http.http_file_size));
                if(boundary_content_len>0 && boundary_content_len < http_value->PROTOCOL_VAL_LEN(content))
                    http_value->PROTOCOL_VAL_PTR(content) = NULL;
                HTTP_VALUE_DUP(flow, file_data_start, boundary_content_len, http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
            }
            if (form_urlencoded) {
              http_parse_form_urlencoded(http_value,http_value->PROTOCOL_VAL_PTR(content), http_value->PROTOCOL_VAL_LEN(content));
            }
            if (http_value->PROTOCOL_VAL_LEN(method) > 0) {
              int body_req_len = (sizeof(http_value->req_body)-1 > http_value->PROTOCOL_VAL_LEN(content) ? http_value->PROTOCOL_VAL_LEN(content) : sizeof(http_value->req_body) - 1);
              strncpy(http_value->req_body, (char *)http_value->PROTOCOL_VAL_PTR(content), body_req_len);
            } else if (http_value->PROTOCOL_VAL_LEN(code) > 0) {
                      int body_rsp_len = (sizeof(http_value->rsp_body)-1 > http_value->PROTOCOL_VAL_LEN(content) ? http_value->PROTOCOL_VAL_LEN(content) : sizeof(http_value->rsp_body) - 1);
              strncpy(http_value->rsp_body, (char *)http_value->PROTOCOL_VAL_PTR(content), body_rsp_len);
            }
            if(rsp_fulltext_len){
                http_value->rsp_fulltext_len = http_value->PROTOCOL_VAL_LEN(content);
            }

        }

        //创造子flow_id关联上下行
        if(session->direction[FLOW_DIR_DST2SRC] >= session->direction[FLOW_DIR_SRC2DST])
            session->direction[FLOW_DIR_SRC2DST] = session->direction[FLOW_DIR_DST2SRC];
        else
            session->direction[FLOW_DIR_DST2SRC] = session->direction[FLOW_DIR_SRC2DST] - 1;
        flow->sub_flow_id = session->direction[FLOW_DIR_SRC2DST];

        // direction: FLOW_DIR_SRC2DST -- > C2S
        // direction: FLOW_DIR_DST2SRC -- > S2C
        // is_http_request 的 与 系统内部的 C2S 枚举值 一致
        if(1==g_config.http.http_switch_store_file){
        }else{
            if(0==http_value->chunk_flag){
                _value=NULL;
                _value = g_hash_table_lookup(http_value->table, "content-encoding");
                if (_value) {
                    http_value->chunk_flag=1;
                }
            }
        }
    }

    //响应中的数据
    _value = g_hash_table_lookup(http_value->table, "content-disposition");
    if (_value) {
        value = (struct header_value *)_value;
        if (value->len > 0 &&
          (value->len + strlen(http_value->conDisp) + 1) < sizeof(http_value->conDisp)) {
            if (strlen(http_value->conDisp) > 0) {
                strcat(http_value->conDisp, ",");
            }
            memcpy(http_value->conDisp + strlen(http_value->conDisp), value->ptr, value->len);
        }
        //避免在unknown_line中再次出现
        g_hash_table_remove(http_value->table, "content-disposition");
    }

    //请求与响应同时输出(为了 LINK的包数统计)
    if(0 == is_http_request)
    {
        struct http_info *http_request  = session->http_value + 1;
        if(http_request->table)
        {
            if (!g_config._327_common_switch) {
                high_app_proto_http_identify(flow, http_request);
            }
            write_http_log(flow, FLOW_DIR_SRC2DST, http_request, NULL);
            g_hash_table_destroy(http_request->table);
            http_request->table = NULL;
        }

        struct http_info *http_response = session->http_value + 0;
        if(http_response->table)
        {
            if (!g_config._327_common_switch) {
              high_app_proto_http_identify(flow, http_response);
            }
            write_http_log(flow, FLOW_DIR_DST2SRC, http_response, NULL);
            g_hash_table_destroy(http_response->table);
            http_response->table = NULL;
        }
    }

exit:
    if(!is_http_request){
        if(session->uri!=NULL){
            free(session->uri);
            session->uri=NULL;
        }
    }

    return PKT_OK;
}

static int dissect_http_sctp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    if (!flow->app_session)
    {
        flow->app_session = dpi_malloc(sizeof(struct http_session));
        if (!flow->app_session)
            return PKT_DROP;
        memset(flow->app_session, 0, sizeof(struct http_session));
    }
    dissect_http(flow, payload, payload_len);
    return 0;
}

#define HTTP_NEED_MORE  0
#define HTTP_ERROR     -1
static int has_chunked(const char*p, int l)
{
    const char *find_str[] = {"\x0D\x0A\x0D\x0A", "chunked", "Transfer-Encoding", "HTTP/1.", NULL};
    const char *find       = p + l;
    int         i;

    for(i = 0; NULL != find_str[i]; i++)
    {
        find = memmem(p, find -p, find_str[i],  strlen(find_str[i]));
        if(NULL == find)
        {
            return 0;
        }
    }
    return 1;
}

static int64_t get_chunkedlen(const char *http, int http_len)
{
    int64_t      offset      = 0;
    int64_t      chunk_size  = 0;
    int64_t      chunk_len   = 0;
    const char   *str_split   = "\x0D\x0A\x0D\x0A";
    const char   *p           = NULL;

    if(0 == has_chunked(http, http_len))
    {
        return -1;  // no chunk
    }

    // skip http head
    p = memmem(http, http_len, str_split,  strlen(str_split));
    if(NULL == p)
    {
        return 0;
    }

    offset    = p - http + strlen(str_split);
    http     += offset;
    http_len -= offset;
    offset    = 0;

    while (offset < http_len)
    {
        chunk_len = find_packet_line_end((const uint8_t*)http + offset, (uint32_t)http_len - offset);
        if (chunk_len <= 0)
        {
            break;
        }

        chunk_size = strtol(http + offset, NULL, 16);
        if(chunk_size > (1024*1024*10) || chunk_size < 0) // 限定每个chunk_size
        {
            return -1;  // no chunk
        }

        offset    += (chunk_len + 2 + chunk_size + 2);  // point next chunked
        if (offset  > http_len)
        {
            break;
        }
        else
        if (0 == chunk_size && offset == http_len)
        {
            return http_len;
        }
    }
    return HTTP_NEED_MORE;
}

// 返回 Content-Length 数值类型
static int64_t get_contentlen(const char *p, int l)
{
#define CONTENT_STR_LEN 16
    const char *str_start   = "\r\nContent-Length";
    const char *str_end     = "\r\n";

    if(l < CONTENT_STR_LEN)
        return 0;

    const char *find_start = memmem(p, l, str_start,  CONTENT_STR_LEN);
    if(find_start)
        find_start += CONTENT_STR_LEN;
    else
        return 0;

    const char *find_end = memmem(find_start, l-(find_start-p), str_end,  strlen(str_end));
    if(find_end == NULL || find_start + 15 < find_end)  //0xffffffff = 4294967295 = 4G
        return 0;

    int  i = 0;
    char buff[16];
    while(find_start < find_end)
    {
        if(isdigit(*find_start))
            buff[i++] =  *find_start;
        else if(*find_start != ':' && *find_start != ' ')
            return 0;
        find_start++;
    }
    buff[i] = 0;

    return atol(buff);
}

/*
*  0: not http
*  1: is  http
*/
static int is_http(const char *p, int l)
{
    int ST     ;
    int offset ;
    int i      ;
    int find   ;

    enum
    {
        HTTP_ST_DEFAULT,
        HTTP_ST_REQUEST,
        HTTP_ST_REQUEST_METHOD,
        HTTP_ST_REQUEST_URI,
        HTTP_ST_REQUEST_VERSION,
        HTTP_ST_RESPONSE,
        HTTP_ST_RESPONSE_VERSION,
        HTTP_ST_RESPONSE_NUM,
        HTTP_ST_END,
    };

    if(l <= 8)
    {
        return 0;
    }

    ST     = HTTP_ST_DEFAULT;
    offset = 0;
    while(offset < l)
    {
        switch(ST)
        {
            case HTTP_ST_DEFAULT:
                if(0 == memcmp(p, "HTTP/1.", 7))
                {
                    ST = HTTP_ST_RESPONSE;
                }
                else
                {
                    ST = HTTP_ST_REQUEST;
                }
                break;

            case HTTP_ST_REQUEST:
                ST = HTTP_ST_REQUEST_METHOD;
                break;

            case HTTP_ST_REQUEST_METHOD:
                find = 0;
                if(l - offset > 10){
                    for(i = 0; HTTP_METHOD[i].method; i++)
                    {
                        if(0 == strncasecmp(p + offset, HTTP_METHOD[i].method, HTTP_METHOD[i].len))
                        {
                            offset += HTTP_METHOD[i].len;
                            ST = HTTP_ST_END;
                            find = 1;
                            break;
                        }
                    }
                }
                if(0 == find)
                {
                    return 0;
                }
                break;

            case HTTP_ST_REQUEST_VERSION:
                if(0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1.")))
                {
                    offset += strlen("HTTP/1.");
                    ST = HTTP_ST_END;
                }
                else
                {
                    return 0;
                }
                break;

            case HTTP_ST_RESPONSE:
                ST = HTTP_ST_RESPONSE_VERSION;
                break;

            case HTTP_ST_RESPONSE_VERSION:
                if(0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1.")))
                {
                    offset += strlen("HTTP/1.");
                    offset ++;
                    offset ++;
                    ST = HTTP_ST_RESPONSE_NUM;
                }
                else
                {
                    return 0;
                }
                break;

            case HTTP_ST_RESPONSE_NUM:
                for(i = 0; i < 3; i++)
                {
                    if(0 == isdigit(p[offset + i]))
                    {
                        return 0;
                    }
                }
                ST = HTTP_ST_END;
                break;

            case HTTP_ST_END:
                return 1;
                break;
        }

    }
    return 0;
}


// 返回 HTTP head 长度范围
// 负数: 没有找到
static int64_t get_header_len(const char *p, int l)
{
    if(0 == is_http(p,l))
    {
        return HTTP_ERROR;
    }

    const char *find = memmem(p, l, "\x0D\x0A\x0D\x0A", 4);
    if(NULL == find)
    {
        return HTTP_NEED_MORE;
    }

    return  find - p + 4;
}


// 返回负数, 代表不存在一个完整HTTP 长度
// 返回正数, 代表  存在一个完整HTTP 长度
static int64_t get_http_len(const char *p, int len)
{
    const uint8_t *pstart   = NULL;
    const uint8_t *pend     = NULL;
    const uint8_t *data     = NULL;
    int64_t        remain   = 0;
    int64_t        success  = 0;
    int64_t        hl       = 0;  // header  len
    int64_t        cl       = 0;  // content len

    if(len < 0)
    {
        return -1;
    }

    hl = get_header_len(p, len);
    if(HTTP_NEED_MORE == hl)
    {
        return HTTP_NEED_MORE;
    }
    else if(HTTP_ERROR == hl)
    {
        return HTTP_ERROR;
    }

    cl = get_contentlen(p, hl);
    if(cl > 0)
    {
        return hl + cl;
    }

    cl = get_chunkedlen(p, len);
    if(cl > 0)
    {
        return hl + cl;
    }
    else if(cl == HTTP_NEED_MORE)
    {
        return HTTP_NEED_MORE;
    }

    return hl;
}

// 已缓存的数据直接输出
static int http_tcp_over(struct flow_info *flow)
{
    // 重组结束
    return 0;
}


// 已缓存的数据直接输出
static int http_miss(struct flow_info *flow, uint8_t C2S, uint32_t len)
{
    struct   flow_info    *f     = NULL;
    struct   http_session *s     = NULL;
    struct   http_cache   *c     = NULL;
    int      paddinglen  = 0;
    int      safelen     = 0;
    int      miss_len    = len;

    f = flow;
    if(NULL == f->app_session)
    {
        return 0;
    }

    s = (struct http_session *)f->app_session;
    c = s->cache + C2S;
    if (len) {
        f->intflag = 0;
    }
    // 如果允许 TCP PADDING
    if(c->addr && miss_len && g_config.http.tcp_padding_len > 0 && miss_len <= g_config.http.tcp_padding_len && c->cache_size - c->cache_hold > miss_len)
    {
        paddinglen = strlen(g_config.http.tcp_padding_str);
        while(miss_len > 0)
        {
            safelen = (paddinglen < miss_len) ? paddinglen : miss_len;
            memcpy(c->addr + c->cache_hold, g_config.http.tcp_padding_str, safelen);
            c->cache_hold += safelen;
            miss_len      -= safelen;
        }
    }
    else
        if(c->addr)
        {
            dissect_http(f, (const uint8_t*)c->addr, (uint32_t)c->cache_hold);
            free(c->addr);
            c->addr      = NULL;
            c->cache_hold = 0;
        }
    return 0;
}

// HTTP 专业切割机(解决 pipeline)
// 切出一个完整的 HTTP给解析接口(HTTP_HEAD + content)
static int dissect_http_pipeline(struct flow_info *flow, uint8_t C2S, const uint8_t *p, uint32_t  pl)
{
    char                 t     = 0;
    int64_t              hl    = 0;
    int64_t              offset= 0;
    int64_t              l     = pl;
    struct flow_info    *f     = flow;
    struct http_session *s     = NULL;
    struct http_cache   *c     = NULL;

    if (NULL == f->app_session)
    {
        ATOMIC_FETCH_ADD(&http_new_flow);
        f->app_session = dpi_malloc(sizeof(struct http_session));
        if (NULL == f->app_session)
        {
            goto HTTP_NEED_MORE_PKT;
        }
        memset(f->app_session, 0, sizeof(struct http_session));
    }
    s = (struct http_session *)f->app_session;
    c = s->cache + C2S;

    // 是否开启缓存
    if(c->addr)
    {
        if(l >= (c->cache_size - c->cache_hold))
        {
            // 缓存撑爆前,  解析数据, 释放
            dissect_http(f, (const uint8_t*)c->addr, (uint32_t)c->cache_hold);
            goto HTTP_DROP;
        }

        //正常 拼装
        memcpy(c->addr + c->cache_hold, p, l);
        c->cache_hold  += l;
        c->addr[c->cache_hold] = '\0';   //末尾填充0
        p = (const uint8_t*)c->addr;
        l = c->cache_hold;
    }

    // 专业切割机
    int64_t (*get_len_func)(const char *p, int l) = g_config.http.http_skip_body ? get_header_len : get_http_len;
    while(offset < l)
    {
        hl = get_len_func((const char*)p + offset, l - offset);
        if(hl > 0 && l - offset >= hl)
        {
            dissect_http(f, (const uint8_t*)p + offset, (uint32_t)hl);
            offset += hl;
        }
        else if(hl == HTTP_ERROR)
        {
            goto HTTP_DROP;
        }
        else if(hl == HTTP_NEED_MORE)
        {
            break;
        }
        else if(hl > l - offset)
        {
            break;
        }
    }

    // 有没有剩料?
    if(offset >=0 && offset < l)
    {
        if(NULL != c->addr && offset >0)  //已开启缓存, 直接将剩料挪到前面
        {
            memmove(c->addr, c->addr + offset, c->cache_hold - offset);
            c->cache_hold  -= offset;
        }
        else if(NULL == c->addr)          //未开启缓存, 创建缓存, 把剩料放在前面
        {
            c->cache_size = g_config.http.http_strip_cache_size;
            c->cache_hold = l - offset;
            c->addr      = dpi_malloc(c->cache_size);
            memcpy(c->addr, p + offset, l - offset);
        }
        goto HTTP_NEED_MORE_PKT;
    }
    else
    {
        if(NULL != c->addr)
        {
            free(c->addr);
            c->addr      = NULL;
            c->cache_hold = 0;
        }
        goto HTTP_NEED_MORE_PKT;
    }


// HTTP  太长, 只解析被缓存的部分.
HTTP_DROP:
    if(NULL != c->addr)
    {
        free(c->addr);
        c->addr = NULL;
        c->cache_hold = 0;
    }

// HTTP 解析,需要更多报文
HTTP_NEED_MORE_PKT:
    return 0;
}


static void flow_http_finish(struct flow_info *flow)
{
    ATOMIC_FETCH_ADD(&http_free_flow);

    struct http_info *http_value= NULL;
    if(flow->app_session)
    {
        struct http_session *session = (struct http_session *)flow->app_session;

        //将缓存在CACHE中的数据 输出 --
        struct http_session *s     = NULL;
        struct http_cache   *c     = NULL;
        s = (struct http_session *)flow->app_session;
        c = s->cache + 0;
        if(c->addr)
        {
            dissect_http(flow, (const uint8_t*)c->addr, (uint32_t)c->cache_hold);
            c->cache_hold = 0;
        }

        c = s->cache + 1;
        if(c->addr)
        {
            dissect_http(flow, (const uint8_t*)c->addr, (uint32_t)c->cache_hold);
            c->cache_hold = 0;
        }

        //判断会话是不是已经缓存了 HTTP的请求数据(HTTP是 基于RESP输出的)

        //超时 - 输出HTTP请求数据缓存区
        http_value = session->http_value + 1;
        if(http_value->table)
        {
            high_app_proto_http_identify(flow, http_value);
            write_http_log(flow, FLOW_DIR_SRC2DST, http_value, NULL);
            g_hash_table_destroy(http_value->table);
            http_value->table = NULL;
        }

        //超时 - 输出HTTP响应数据缓存区
        http_value = session->http_value + 0;
        if(http_value->table)
        {
            high_app_proto_http_identify(flow, http_value);
            write_http_log(flow, FLOW_DIR_DST2SRC, http_value, NULL);
            g_hash_table_destroy(http_value->table);
            http_value->table = NULL;
        }

        if(session->uri)
        {
            free(session->uri);
            session->uri = NULL;
        }

        if(session->cache[0].addr)
        {
            free(session->cache[0].addr);
            session->cache[0].addr = NULL;
        }

        if(session->cache[1].addr)
        {
            free(session->cache[1].addr);
            session->cache[1].addr = NULL;
        }

        free(flow->app_session);
        flow->app_session = NULL;
    }
    //printf("http_new_flow  %zu  ", http_new_flow);
    //printf("http_free_flow %zu\n", http_free_flow);
}

static int identify_http(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_HTTP] == 0)
        return PROTOCOL_UNKNOWN;

    if (payload_len < 9)
    {
        return 0;
    }

    // is response ?
    if (strncasecmp((const char *)payload, "HTTP/1.", 7) == 0)
    {
        flow->err_pcap_dump = g_config.http.error_pcap_dump; // 设定ERROR 转储 默认的开关
        flow->real_protocol_id = PROTOCOL_HTTP;
        return PROTOCOL_HTTP;
    }

    // is request ?
    for(int i = 0; HTTP_METHOD[i].method; i++)
    {
        if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len))
        {
            flow->err_pcap_dump = g_config.http.error_pcap_dump; // 设定ERROR 转储 默认的开关
            flow->real_protocol_id = PROTOCOL_HTTP;
            return PROTOCOL_HTTP;
        }
    }
    return PROTOCOL_UNKNOWN;
}

extern struct decode_t decode_http;
static int http_initial(struct decode_t *decode)
{
    decode_on_port_tcp(80,   &decode_http);
    decode_on_port_tcp(8080, &decode_http);
    decode_on_port_tcp(8000, &decode_http);
    map_fields_info_register(http_field_array, PROTOCOL_HTTP, EM_HTTP_MAX, "http");
    dpi_register_proto_schema(http_field_array,EM_HTTP_MAX,"http");
    register_tbl_array(TBL_LOG_HTTP, 1, "http", NULL);

    pschema_t * schema = dpi_pschema_get_proto("http");
    high_app_proto_http_init();
    return 0;
}

static int http_destroy(struct decode_t *decode)
{
    return 0;
}

struct decode_t decode_http = {
    .name           =   "http",
#ifdef DPI_SDT_ZDY
    .identify_type  =   DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial =   http_initial,
    .pkt_identify   =   identify_http,
    .pkt_dissect    =   dissect_http_pipeline,
    .pkt_miss       =   http_miss,
    .flow_finish    =   flow_http_finish,
    .decode_destroy =   http_destroy,
};


