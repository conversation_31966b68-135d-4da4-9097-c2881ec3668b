#!/bin/bash

# rm build -rf ; cmake3 -B build -DCMAKE_VERBOSE_MAKEFILE:BOOL=ON;cmake3 --build build -j
# rm build -rf ; cmake3 -B build;cmake3 --build build -j

dpi_name="yaDpiSdt"

function step {
  if [ $? == 0 ]; then
    echo "-- excuting: $*"
    eval $*
  else
    echo "-- error: previous operation failed, exit"
    exit 1
  fi
}

function build_after()
{
  # build_dir=$1
  version=$(cat VERSION)
  find_flag=$(find ./run -type f -iname "yaDpiSdt_${version}")
  # if [ -z "$find_flag" ]; then
    echo "build yaDpiSdt_${version}"
  cd run || { echo "cd run failure"; exit 1; }
    # ln -fs yaDpi_${version} yaDpi
  cd ..
  # else
  #   echo "yaDpi_${version} already exists"
  # fi

  # file=$(find "${build_dir}"/bin -type f -iname "yaDpi*")
  # if [ -z "${file}" ];then
  #   echo "build failed!!"
  #   exit
  # fi
  # name=${file##*/}
  # echo "$file"
  # echo "$name"

  # /bin/cp -a "${build_dir}"/bin/"${name}" run/
  # cd run || { echo "cd run failure"; exit 1; }
  # ln -fs "${name}" yaDpi
  # cd ..
}


function build_dpi() {
  type="build"
  generator="Unix Makefiles"
  # if [ -x "$(command -v ninja)" ];then
  #   generator="Ninja"
  # fi
  if [ ${1:-""} == "debug" ]; then
    type="${type}_debug"
    cmake3 -B ${type} -DCMAKE_BUILD_TYPE=Debug -G "$generator"
  else
    type="${type}_release"
    cmake3 -B ${type} -DCMAKE_BUILD_TYPE=Release -G "$generator"
  fi
  echo ${type}

  # cmake3 -B build -DCMAKE_VERBOSE_MAKEFILE:BOOL=ON
  cmake3 --build ${type} --target vtysh
  cmake3 --build ${type}  -j 6

  build_after ${type}
}

#git submodule init
#git submodule update

build_cmd="build_dpi $*"
clean_cmd="rm -rf build               \
          && rm -rf build_release     \
          && rm -rf build_debug       \
           && make -C vtysh $*"

# do clean
if [ ${1:-""} == "clean" ]; then
  step "${clean_cmd}"
  exit
fi

if [ ${1:-""} == "release" ]; then
step "${build_cmd}"
  exit
fi

if [ ${1:-""} == "debug" ]; then
  step "${build_cmd}"
  exit
fi

if [ ${1:-""} == "add_proto" ]; then
  ./src/write_protocol.sh
  exit
fi

step "${build_cmd}"
