# Configuration for WISE and the data sources are contained in this file.
# See https://arkime.com/wise 


# Configuration for the wiseService itself.
[wiseService]
port=8081
# Exclude common DNSBL style lookups
excludeDomains=*.bl.barracudabrts.com;*.zen.spamhaus.org;*.in-addr.arpa;*.avts.mcafee.com;*.avqs.mcafee.com;*.bl.barracuda.com;*.lbl8.mailshell.net;*.dnsbl.sorbs.net;*.s.sophosxl.net

# OpenDNS Umbrella Integration - https://www.opendns.com/enterprise-security/
[opendns]
# Example, exclude office lookups to prevent DNS leakage
# excludeDomains=*.office.example.com
#key=

# Threatstream OPTIC - http://threatstream.com/
[threatstream]
#user=
#key=

# Emerging Threats Pro - http://emergingthreats.net/
[emergingthreats]
#key=

# ThreatQ - http://http://www.threatquotient.com/
[threatq]
#key=
#host=

# Alien Vault - https://www.alienvault.com/
[alienvault]
#key=

# ReverseDNS
[reversedns]
#ips=10.0.0.0/8;***********/16
#field=asset

# To load local files, set a unique section title starting with file:
# Type should be ip, domain, md5, email
# Files are assumed to be CSV or use format=tagger
[file:blah]
file=/tmp/test.ips
type=ip
tags=TAG1,TAG2
#column=1

# To load URLs, set a unique section title starting with url:
# Type should be ip, domain, md5, email
# URLs are assumed to be CSV or use format=tagger
[url:zeus.ips]
url=https://zeustracker.abuse.ch/blocklist.php?download=ipblocklist
tags=zeustracker,botnet
type=ip
reload=60

[url:zeus.domain]
url=https://zeustracker.abuse.ch/blocklist.php?download=domainblocklist
tags=zeustracker,botnet
type=domain
reload=60

[right-click]
file=/data/moloch/wiseService/right_click.ini.sample
