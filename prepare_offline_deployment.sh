#!/bin/bash
# Arkime 离线部署准备脚本
# 一键收集所有离线安装包并打包

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OFFLINE_DIR="${SCRIPT_DIR}/arkime_offline_packages"
PACKAGE_FILE="${SCRIPT_DIR}/arkime_offline_packages.tar.gz"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示横幅
show_banner() {
    cat << 'EOF'
    
    ╔═══════════════════════════════════════════════════════════╗
    ║                                                           ║
    ║              Arkime 离线部署准备工具                      ║
    ║                                                           ║
    ║    此工具将收集Arkime在CentOS 7.4上部署所需的所有包      ║
    ║                                                           ║
    ╚═══════════════════════════════════════════════════════════╝
    
EOF
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查是否为root用户
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
    
    # 检查系统版本
    if [[ ! -f /etc/redhat-release ]]; then
        log_error "此脚本仅支持CentOS/RHEL系统"
        exit 1
    fi
    
    local version=$(cat /etc/redhat-release)
    log_info "当前系统: $version"
    
    # 检查磁盘空间 (至少需要10GB)
    local available_space=$(df / | awk 'NR==2 {print $4}')
    local required_space=$((10 * 1024 * 1024))  # 10GB in KB
    
    if [[ $available_space -lt $required_space ]]; then
        log_error "磁盘空间不足，至少需要10GB可用空间"
        exit 1
    fi
    
    # 检查网络连接
    if ! ping -c 1 8.8.8.8 &>/dev/null; then
        log_error "网络连接失败，无法下载依赖包"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 显示收集计划
show_collection_plan() {
    cat << EOF

=== 离线包收集计划 ===

将收集以下内容:
1. 系统依赖包 (RPM)
   - 开发工具链 (gcc, make, autotools等)
   - 系统库 (zlib, openssl, pcre等)
   - Perl模块
   - Java运行环境

2. Node.js运行环境
   - Node.js 20.19.2 二进制包
   - npm依赖包

3. 第三方源码包
   - libmaxminddb
   - yara
   - zstd
   - nghttp2
   - curl
   - lua

4. 安装脚本和文档
   - 离线安装脚本
   - 部署文档
   - 配置模板

预计下载大小: 约2-3GB
预计耗时: 20-40分钟 (取决于网络速度)

EOF
}

# 确认继续
confirm_proceed() {
    echo -n "是否继续收集离线安装包? [y/N]: "
    read -r response
    
    case "$response" in
        [yY]|[yY][eE][sS])
            log_info "开始收集离线安装包..."
            ;;
        *)
            log_info "操作已取消"
            exit 0
            ;;
    esac
}

# 清理旧的离线包
cleanup_old_packages() {
    if [[ -d "$OFFLINE_DIR" ]]; then
        log_info "清理旧的离线包目录..."
        rm -rf "$OFFLINE_DIR"
    fi
    
    if [[ -f "$PACKAGE_FILE" ]]; then
        log_info "删除旧的离线包文件..."
        rm -f "$PACKAGE_FILE"
    fi
}

# 运行收集脚本
run_collection() {
    log_info "运行离线包收集脚本..."
    
    if [[ ! -f "${SCRIPT_DIR}/collect_offline_packages.sh" ]]; then
        log_error "收集脚本不存在: ${SCRIPT_DIR}/collect_offline_packages.sh"
        exit 1
    fi
    
    chmod +x "${SCRIPT_DIR}/collect_offline_packages.sh"
    "${SCRIPT_DIR}/collect_offline_packages.sh"
}

# 添加额外文件
add_extra_files() {
    log_info "添加额外的部署文件..."
    
    # 复制部署脚本
    if [[ -f "${SCRIPT_DIR}/deploy_arkime_centos7.sh" ]]; then
        cp "${SCRIPT_DIR}/deploy_arkime_centos7.sh" "${OFFLINE_DIR}/"
    fi
    
    # 复制远程部署脚本
    if [[ -f "${SCRIPT_DIR}/remote_deploy.sh" ]]; then
        cp "${SCRIPT_DIR}/remote_deploy.sh" "${OFFLINE_DIR}/"
    fi
    
    # 复制文档
    if [[ -f "${SCRIPT_DIR}/ARKIME_DEPLOYMENT_GUIDE.md" ]]; then
        cp "${SCRIPT_DIR}/ARKIME_DEPLOYMENT_GUIDE.md" "${OFFLINE_DIR}/docs/"
    fi
    
    # 创建版本信息文件
    cat > "${OFFLINE_DIR}/VERSION_INFO.txt" << EOF
Arkime 离线安装包版本信息
生成时间: $(date)
生成系统: $(cat /etc/redhat-release)
Node.js版本: 20.19.2
Elasticsearch建议版本: 7.17.15

包含内容:
- 系统依赖RPM包
- Node.js运行环境
- 第三方源码包
- 安装脚本和文档

使用方法:
1. 将整个目录复制到目标CentOS 7.4服务器
2. 运行 install_arkime_offline.sh 进行安装
3. 参考 docs/ARKIME_DEPLOYMENT_GUIDE.md 完成配置

EOF
    
    log_success "额外文件添加完成"
}

# 创建校验文件
create_checksums() {
    log_info "创建文件校验和..."
    
    cd "$OFFLINE_DIR"
    find . -type f -exec md5sum {} \; > checksums.md5
    
    log_success "校验文件创建完成"
}

# 打包离线安装包
package_offline_files() {
    log_info "打包离线安装包..."
    
    cd "$SCRIPT_DIR"
    tar -czf "$PACKAGE_FILE" arkime_offline_packages/
    
    local package_size=$(du -h "$PACKAGE_FILE" | cut -f1)
    log_success "离线安装包创建完成: $PACKAGE_FILE ($package_size)"
}

# 验证离线包
verify_package() {
    log_info "验证离线安装包..."
    
    # 检查包文件是否存在
    if [[ ! -f "$PACKAGE_FILE" ]]; then
        log_error "离线安装包文件不存在"
        return 1
    fi
    
    # 检查包文件大小
    local file_size=$(stat -c%s "$PACKAGE_FILE")
    local min_size=$((100 * 1024 * 1024))  # 100MB
    
    if [[ $file_size -lt $min_size ]]; then
        log_error "离线安装包文件过小，可能不完整"
        return 1
    fi
    
    # 测试解压
    local test_dir="/tmp/arkime_package_test"
    mkdir -p "$test_dir"
    
    if tar -tzf "$PACKAGE_FILE" >/dev/null 2>&1; then
        log_success "离线安装包验证通过"
    else
        log_error "离线安装包损坏"
        return 1
    fi
    
    rm -rf "$test_dir"
}

# 显示完成信息
show_completion_info() {
    local package_size=$(du -h "$PACKAGE_FILE" | cut -f1)
    
    cat << EOF

╔═══════════════════════════════════════════════════════════╗
║                    收集完成!                              ║
╚═══════════════════════════════════════════════════════════╝

离线安装包信息:
- 文件位置: $PACKAGE_FILE
- 文件大小: $package_size
- 包含目录: arkime_offline_packages/

下一步操作:
1. 将 $PACKAGE_FILE 传输到目标CentOS 7.4服务器
2. 在目标服务器上解压: tar -xzf arkime_offline_packages.tar.gz
3. 进入目录: cd arkime_offline_packages
4. 运行安装: ./install_arkime_offline.sh

或者使用远程部署工具:
./remote_deploy.sh --mode offline --server 192.168.31.129

详细说明请参考: ARKIME_DEPLOYMENT_GUIDE.md

EOF
}

# 主函数
main() {
    show_banner
    check_requirements
    show_collection_plan
    confirm_proceed
    
    cleanup_old_packages
    run_collection
    add_extra_files
    create_checksums
    package_offline_files
    verify_package
    
    show_completion_info
    
    log_success "离线部署准备完成!"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
