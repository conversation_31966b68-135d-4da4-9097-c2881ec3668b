[{"_index": "arkime_fields_v30", "_type": "_doc", "_id": "session.segments", "_score": 1, "_source": {"friendlyName": "Session Segments", "group": "general", "help": "Number of segments in session so far", "dbField2": "segmentCnt", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "session.length", "_score": 1, "_source": {"friendlyName": "Session Length", "group": "general", "help": "Session Length in milliseconds so far", "dbField2": "length", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "user", "_score": 1, "_source": {"friendlyName": "User", "group": "general", "help": "External user set for session", "dbField2": "user", "type": "termfield", "category": "user"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tags", "_score": 1, "_source": {"friendlyName": "Tags", "group": "general", "help": "Tags set for session", "dbField2": "tags", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "asset", "_score": 1, "_source": {"friendlyName": "<PERSON><PERSON>", "group": "general", "help": "Asset name", "dbField2": "asset", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "icmp.type", "_score": 1, "_source": {"friendlyName": "ICMP Type", "group": "general", "help": "ICMP type field values", "dbField2": "icmp.type", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "icmp.code", "_score": 1, "_source": {"friendlyName": "ICMP Code", "group": "general", "help": "ICMP code field values", "dbField2": "icmp.code", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tls.sessionid", "_score": 1, "_source": {"friendlyName": "Src or Dst Session Id", "group": "general", "help": "Shorthand for tls.sessionid.src or tls.sessionid.dst", "dbField2": "tlsidall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mac.src", "_score": 1, "_source": {"friendlyName": "Src MAC", "group": "general", "help": "Source ethernet mac addresses set for session", "dbField2": "source.mac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mac.dst", "_score": 1, "_source": {"friendlyName": "Dst MAC", "group": "general", "help": "Destination ethernet mac addresses set for session", "dbField2": "destination.mac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outermac.src", "_score": 1, "_source": {"friendlyName": "Src Outer MAC", "group": "general", "help": "Source ethernet outer mac addresses set for session", "dbField2": "srcOuterMac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outermac.dst", "_score": 1, "_source": {"friendlyName": "Dst Outer MAC", "group": "general", "help": "Destination ethernet outer mac addresses set for session", "dbField2": "dstOuterMac", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dscp.src", "_score": 1, "_source": {"friendlyName": "Src DSCP", "group": "general", "help": "Source non zero differentiated services class selector set for session", "dbField2": "srcDscp", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "dscp.dst", "_score": 1, "_source": {"friendlyName": "Dst DSCP", "group": "general", "help": "Destination non zero differentiated services class selector set for session", "dbField2": "dstDscp", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ttl.src", "_score": 1, "_source": {"friendlyName": "TTL Src", "group": "general", "help": "Source IP TTL for first few packets", "dbField2": "srcTTL", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ttl.dst", "_score": 1, "_source": {"friendlyName": "TTL Dst", "group": "general", "help": "Destination IP TTL for first few packets", "dbField2": "dstTTL", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "mac", "_score": 1, "_source": {"friendlyName": "Src or Dst MAC", "group": "general", "help": "Shorthand for mac.src or mac.dst", "dbField2": "macall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outermac", "_score": 1, "_source": {"friendlyName": "Src or Dst Outer MAC", "group": "general", "help": "Shorthand for outermac.src or outermac.dst", "dbField2": "outermacall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oui.src", "_score": 1, "_source": {"friendlyName": "Src OUI", "group": "general", "help": "Source ethernet oui for session", "dbField2": "srcOui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "oui.dst", "_score": 1, "_source": {"friendlyName": "Dst OUI", "group": "general", "help": "Destination ethernet oui for session", "dbField2": "dst<PERSON>ui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outeroui.src", "_score": 1, "_source": {"friendlyName": "Src Outer OUI", "group": "general", "help": "Source ethernet outer oui for session", "dbField2": "srcOuterOui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outeroui.dst", "_score": 1, "_source": {"friendlyName": "Dst Outer OUI", "group": "general", "help": "Destination ethernet outer oui for session", "dbField2": "dstOuterOui", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "vlan", "_score": 1, "_source": {"friendlyName": "VLan", "group": "general", "help": "vlan value", "dbField2": "network.vlan.id", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "vni", "_score": 1, "_source": {"friendlyName": "VNI", "group": "general", "help": "vni value", "dbField2": "vni", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outerip.src", "_score": 1, "_source": {"friendlyName": "Src Outer IP", "group": "general", "help": "Source ethernet outer ip for session", "dbField2": "srcOuterIp", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outerip.dst", "_score": 1, "_source": {"friendlyName": "Dst Outer IP", "group": "general", "help": "Destination outer ip for session", "dbField2": "dstOuterIp", "type": "ip"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "outerip", "_score": 1, "_source": {"friendlyName": "Src or Dst Outer IP", "group": "general", "help": "Shorthand for outerip.src or outerip.dst", "dbField2": "outeripall", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.syn", "_score": 1, "_source": {"friendlyName": "TCP Flag SYN", "group": "general", "help": "Count of packets with SYN and no ACK flag set", "dbField2": "tcpflags.syn", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.syn-ack", "_score": 1, "_source": {"friendlyName": "TCP Flag SYN-ACK", "group": "general", "help": "Count of packets with SYN and ACK flag set", "dbField2": "tcpflags.syn-ack", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.ack", "_score": 1, "_source": {"friendlyName": "TCP Flag ACK", "group": "general", "help": "Count of packets with only the ACK flag set", "dbField2": "tcpflags.ack", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.psh", "_score": 1, "_source": {"friendlyName": "TCP Flag PSH", "group": "general", "help": "Count of packets with PSH flag set", "dbField2": "tcpflags.psh", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.fin", "_score": 1, "_source": {"friendlyName": "TCP Flag FIN", "group": "general", "help": "Count of packets with FIN flag set", "dbField2": "tcpflags.fin", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.rst", "_score": 1, "_source": {"friendlyName": "TCP Flag RST", "group": "general", "help": "Count of packets with RST flag set", "dbField2": "tcpflags.rst", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpflags.urg", "_score": 1, "_source": {"friendlyName": "TCP Flag URG", "group": "general", "help": "Count of packets with URG flag set", "dbField2": "tcpflags.urg", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "packets.src", "_score": 1, "_source": {"friendlyName": "Src Packets", "group": "general", "help": "Total number of packets sent by source in a session", "dbField2": "srcPackets", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "packets.dst", "_score": 1, "_source": {"friendlyName": "Dst Packets", "group": "general", "help": "Total number of packets sent by destination in a session", "dbField2": "dstPackets", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "initRTT", "_score": 1, "_source": {"friendlyName": "Initial RTT", "group": "general", "help": "Initial round trip time, difference between SYN and ACK timestamp divided by 2 in ms", "dbField2": "initRTT", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "communityId", "_score": 1, "_source": {"friendlyName": "Community Id", "group": "general", "help": "Community id flow hash", "dbField2": "communityId", "type": "termfield"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpseq.src", "_score": 1, "_source": {"friendlyName": "TCP Src Seq", "group": "general", "help": "Source SYN sequence number", "dbField2": "tcpseq.src", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "tcpseq.dst", "_score": 1, "_source": {"friendlyName": "TCP Dst Seq", "group": "general", "help": "Destination SYN-ACK sequence number", "dbField2": "tcpseq.dst", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "ethertype", "_score": 1, "_source": {"friendlyName": "Ethertype", "group": "general", "help": "The ethernet protocol type", "dbField2": "ethertype", "type": "integer"}}, {"_index": "arkime_fields_v30", "_type": "_doc", "_id": "protocols", "_score": 1, "_source": {"friendlyName": "Protocols", "group": "general", "help": "Protocols set for session", "dbField2": "protocol", "type": "termfield"}}]