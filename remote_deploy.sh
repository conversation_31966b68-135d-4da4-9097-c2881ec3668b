#!/bin/bash
# Arkime 远程部署脚本
# 用于连接到目标服务器并执行部署

set -e

# 目标服务器配置
TARGET_SERVER="**************"
TARGET_USER="root"
TARGET_PASS="VictoR#.0.0"
TARGET_DIR="/home/<USER>/DataSource"

# 本地配置
LOCAL_PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_MODE="online"  # online 或 offline

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示使用说明
show_usage() {
    cat << EOF
Arkime 远程部署脚本

用法: $0 [选项]

选项:
    -m, --mode MODE     部署模式: online 或 offline (默认: online)
    -s, --server IP     目标服务器IP (默认: **************)
    -u, --user USER     目标服务器用户名 (默认: root)
    -p, --password PASS 目标服务器密码 (默认: VictoR#.0.0)
    -d, --dir DIR       目标服务器部署目录 (默认: /home/<USER>/DataSource)
    -h, --help          显示此帮助信息

示例:
    $0 --mode online                    # 在线部署
    $0 --mode offline                   # 离线部署
    $0 --server ************* --mode online  # 指定服务器在线部署

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                DEPLOY_MODE="$2"
                shift 2
                ;;
            -s|--server)
                TARGET_SERVER="$2"
                shift 2
                ;;
            -u|--user)
                TARGET_USER="$2"
                shift 2
                ;;
            -p|--password)
                TARGET_PASS="$2"
                shift 2
                ;;
            -d|--dir)
                TARGET_DIR="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 验证部署模式
    if [[ "$DEPLOY_MODE" != "online" && "$DEPLOY_MODE" != "offline" ]]; then
        log_error "无效的部署模式: $DEPLOY_MODE"
        exit 1
    fi
}

# 检查依赖工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    
    # 检查sshpass
    if ! command -v sshpass &> /dev/null; then
        missing_tools+=("sshpass")
    fi
    
    # 检查rsync
    if ! command -v rsync &> /dev/null; then
        missing_tools+=("rsync")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_info "请安装缺少的工具:"
        log_info "  CentOS/RHEL: yum install -y sshpass rsync"
        log_info "  Ubuntu/Debian: apt-get install -y sshpass rsync"
        exit 1
    fi
    
    log_success "依赖工具检查完成"
}

# 测试SSH连接
test_ssh_connection() {
    log_info "测试SSH连接到 ${TARGET_USER}@${TARGET_SERVER}..."
    
    if sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 \
        "${TARGET_USER}@${TARGET_SERVER}" "echo 'SSH连接成功'" &>/dev/null; then
        log_success "SSH连接测试成功"
    else
        log_error "SSH连接失败，请检查服务器地址、用户名和密码"
        exit 1
    fi
}

# 准备目标服务器环境
prepare_target_environment() {
    log_info "准备目标服务器环境..."
    
    # 创建部署目录
    sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no \
        "${TARGET_USER}@${TARGET_SERVER}" "mkdir -p ${TARGET_DIR}"
    
    # 检查系统版本
    local os_version=$(sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no \
        "${TARGET_USER}@${TARGET_SERVER}" "cat /etc/redhat-release 2>/dev/null || echo 'Unknown'")
    
    log_info "目标服务器系统: $os_version"
    
    log_success "目标服务器环境准备完成"
}

# 在线部署
deploy_online() {
    log_info "开始在线部署..."
    
    # 同步项目文件
    log_info "同步项目文件到目标服务器..."
    sshpass -p "$TARGET_PASS" rsync -avz --progress \
        --exclude='.git' \
        --exclude='node_modules' \
        --exclude='arkime_offline_packages' \
        "${LOCAL_PROJECT_DIR}/" \
        "${TARGET_USER}@${TARGET_SERVER}:${TARGET_DIR}/arkime/"
    
    # 执行部署脚本
    log_info "在目标服务器上执行部署..."
    sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no \
        "${TARGET_USER}@${TARGET_SERVER}" \
        "cd ${TARGET_DIR}/arkime && chmod +x deploy_arkime_centos7.sh && ./deploy_arkime_centos7.sh"
    
    log_success "在线部署完成"
}

# 离线部署
deploy_offline() {
    log_info "开始离线部署..."
    
    # 检查离线包是否存在
    local offline_package="${LOCAL_PROJECT_DIR}/arkime_offline_packages.tar.gz"
    if [[ ! -f "$offline_package" ]]; then
        log_error "离线安装包不存在: $offline_package"
        log_info "请先运行 collect_offline_packages.sh 生成离线安装包"
        exit 1
    fi
    
    # 传输离线安装包
    log_info "传输离线安装包到目标服务器..."
    sshpass -p "$TARGET_PASS" scp -o StrictHostKeyChecking=no \
        "$offline_package" \
        "${TARGET_USER}@${TARGET_SERVER}:${TARGET_DIR}/"
    
    # 同步项目源码
    log_info "同步项目源码..."
    sshpass -p "$TARGET_PASS" rsync -avz --progress \
        --exclude='.git' \
        --exclude='node_modules' \
        --exclude='arkime_offline_packages' \
        "${LOCAL_PROJECT_DIR}/" \
        "${TARGET_USER}@${TARGET_SERVER}:${TARGET_DIR}/arkime/"
    
    # 在目标服务器上解压并安装
    log_info "在目标服务器上执行离线安装..."
    sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no \
        "${TARGET_USER}@${TARGET_SERVER}" << EOF
cd ${TARGET_DIR}
tar -xzf arkime_offline_packages.tar.gz
cd arkime_offline_packages
chmod +x install_arkime_offline.sh
./install_arkime_offline.sh
EOF
    
    log_success "离线部署完成"
}

# 验证部署结果
verify_deployment() {
    log_info "验证部署结果..."
    
    # 检查Arkime是否安装成功
    local arkime_status=$(sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no \
        "${TARGET_USER}@${TARGET_SERVER}" \
        "test -f /opt/arkime/bin/capture && echo 'installed' || echo 'not_installed'")
    
    if [[ "$arkime_status" == "installed" ]]; then
        log_success "Arkime安装验证成功"
    else
        log_error "Arkime安装验证失败"
        return 1
    fi
    
    # 检查服务状态
    log_info "检查服务状态..."
    sshpass -p "$TARGET_PASS" ssh -o StrictHostKeyChecking=no \
        "${TARGET_USER}@${TARGET_SERVER}" \
        "systemctl status elasticsearch arkimecapture arkimeviewer --no-pager -l || true"
    
    log_success "部署验证完成"
}

# 显示部署后信息
show_post_deployment_info() {
    log_success "Arkime部署完成!"
    
    cat << EOF

=== 部署信息 ===
目标服务器: ${TARGET_SERVER}
部署目录: ${TARGET_DIR}
部署模式: ${DEPLOY_MODE}

=== 访问信息 ===
Web界面: https://${TARGET_SERVER}:8005
默认用户: admin/admin (首次登录后请修改密码)

=== 管理命令 ===
启动服务: ssh ${TARGET_USER}@${TARGET_SERVER} "/opt/arkime/bin/arkime-start.sh"
停止服务: ssh ${TARGET_USER}@${TARGET_SERVER} "/opt/arkime/bin/arkime-stop.sh"
查看状态: ssh ${TARGET_USER}@${TARGET_SERVER} "/opt/arkime/bin/arkime-status.sh"

=== 后续步骤 ===
1. 登录目标服务器完成Elasticsearch配置
2. 根据网络环境调整capture配置
3. 创建用户账户
4. 开始数据捕获

详细信息请参考: ARKIME_DEPLOYMENT_GUIDE.md

EOF
}

# 主函数
main() {
    log_info "Arkime远程部署工具启动..."
    
    parse_args "$@"
    check_dependencies
    test_ssh_connection
    prepare_target_environment
    
    case "$DEPLOY_MODE" in
        "online")
            deploy_online
            ;;
        "offline")
            deploy_offline
            ;;
    esac
    
    verify_deployment
    show_post_deployment_info
    
    log_success "远程部署流程完成!"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
