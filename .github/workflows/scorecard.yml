# This workflow uses actions that are not certified by GitHub. They are provided
# by a third-party and are governed by separate terms of service, privacy
# policy, and support documentation.

name: Scorecard supply-chain security
on:
  # For Branch-Protection check. Only the default branch is supported. See
  # https://github.com/ossf/scorecard/blob/main/docs/checks.md#branch-protection
  branch_protection_rule:
  # To guarantee Maintained check is occasionally updated. See
  # https://github.com/ossf/scorecard/blob/main/docs/checks.md#maintained
  schedule:
    - cron: '30 12 * * 5'
  push:
    branches: [ "main" ]
    paths-ignore:
      - '*.md'
      - '.*'

# Declare default permissions as read only.
permissions: read-all

jobs:
  analysis:
    name: Scorecard analysis
    runs-on: ubuntu-latest
    permissions:
      # Needed for Code scanning upload
      security-events: write
      # Needed for GitHub OIDC token if publish_results is true
      id-token: write

    steps:
      - name: "Checkout code"
        uses: actions/checkout@9bb56186c3b09b4f86b1c65136769dd318469633 # v4.1.2
        with:
          persist-credentials: false

      - name: "Run analysis"
        uses: ossf/scorecard-action@0864cf19026789058feabb7e87baa5f140aac736 # v2.3.1
        with:
          results_file: results.sarif
          results_format: sarif
          # Scorecard team runs a weekly scan of public GitHub repos,
          # see https://github.com/ossf/scorecard#public-data.
          # Setting `publish_results: true` helps us scale by leveraging your workflow to
          # extract the results instead of relying on our own infrastructure to run scans.
          # And it's free for you!
          publish_results: true

      # Upload the results as artifacts (optional). Commenting out will disable
      # uploads of run results in SARIF format to the repository Actions tab.
      # https://docs.github.com/en/actions/advanced-guides/storing-workflow-data-as-artifacts
      - name: "Upload artifact"
        uses: actions/upload-artifact@5d5d22a31266ced268874388b861e4b58bb5c2f3 # v4.3.1
        with:
          name: SARIF file
          path: results.sarif
          retention-days: 5

      # Upload the results to GitHub's code scanning dashboard (optional).
      # Commenting out will disable upload of results to your repo's Code Scanning dashboard
      - name: "Upload to code-scanning"
        uses: github/codeql-action/upload-sarif@cdcdbb579706841c47f7063dda365e292e5cad7a # v2.16.4
        with:
          sarif_file: results.sarif
