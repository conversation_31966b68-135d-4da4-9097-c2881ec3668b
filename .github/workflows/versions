---
# This file contains all the versions of Arkime that are built and tested
# Arguments:
#   version - Unique identifier for the version, will be used in the filename, must be {platform}{seperator}{architecture}
#   buildopt: The options to use with easybutton-build.sh
#   container - Docker container to use for building the package
#   runson - The name of the runner to use for the build
#   package - Type of package to build: rpm, deb, arch
#   fpmdeps - Dependencies to pass to fpm
#   ja4plus - Build the ja4plus plugin, only need once per architecture, should be the same as docker!!!
#   docker - do docker stuff on this build
#   uitest - Should the UI tests be run
#   python - instead of just 'python'
#   notice - build the notice file, only need once
#   viewertest - run the viewer tests can be normal or sanitize


include:
  - version: el8.x86_64
    container: andywick/arkime-build-8:5.0.0-2
    buildopt: "--kafka --pfring"
    package: rpm
    fpmdeps: "-d perl-libwww-perl -d perl-JSON -d ethtool -d libyaml -d perl-LWP-Protocol-https"
    uitest: true
    python: python3.8

  - version: el9.x86_64
    container: andywick/arkime-build-9:5.0.0-2
    buildopt: "--kafka --pfring"
    fpmdeps: "-d perl-libwww-perl -d perl-JSON -d ethtool -d libyaml -d perl-LWP-Protocol-https -d libzstd"
    package: rpm
    notice: true

  - version: el10.x86_64
    container: andywick/arkime-build-10:5.0.0-7
    buildopt: "--kafka"
    fpmdeps: "-d perl-libwww-perl -d perl-JSON -d ethtool -d libyaml -d perl-LWP-Protocol-https -d libzstd -d libmaxminddb -d libpcap -dlibrdkafka"
    package: rpm

  - version: ubuntu2004_amd64
    container: andywick/arkime-build-20:5.0.0-2
    buildopt: "--kafka --pfring"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev"
    package: deb

  - version: ubuntu2204_amd64
    container: andywick/arkime-build-22:5.0.0-5
    buildopt: "--nothirdparty --kafka --pfring"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara8 -d librdkafka1"
    package: deb
    viewertest: sanitize

  - version: ubuntu2404_amd64
    container: andywick/arkime-build-24:5.0.0-3
    buildopt: "--nothirdparty --kafka"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara10 -d librdkafka1 -d libpcre3"
    package: deb

  - version: "arch-x86_64"
    container: andywick/arkime-build-arch:5.0.0-7
    buildopt: "--kafka"
    fpmdeps: "-d libmaxminddb -d libpcap -d yara -d perl-http-message -d perl-lwp-protocol-https -d perl-json -d libnet -d lua -d zstd -d openssl-1.1 -d pcre"
    package: arch
    viewertest: normal

  - version: al2023.x86_64
    container: andywick/arkime-build-al2023:5.0.0-2
    buildopt: "--kafka"
    fpmdeps: "-d perl-libwww-perl -d perl-JSON -d ethtool -d libyaml -d perl-LWP-Protocol-https"
    package: rpm

  - version: debian12_amd64
    container: andywick/arkime-build-d12:5.0.0-7
    buildopt: "--nothirdparty --kafka"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara9 -d librdkafka1 -d libpcre3"
    package: deb
    docker: true
    ja4plus: true

  - version: debian13_amd64
    container: andywick/arkime-build-d13:5.0.0-7
    buildopt: "--nothirdparty --kafka"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara10 -d librdkafka1"
    package: deb

  ############# ARM #############
  - version: al2023.aarch64
    container: andywick/arkime-build-al2023-arm64:5.0.0-3
    buildopt: "--kafka"
    fpmdeps: "-d perl-libwww-perl -d perl-JSON -d ethtool -d libyaml -d perl-LWP-Protocol-https"
    package: rpm
    runson: ubuntu-24.04-arm

  - version: ubuntu2204_arm64
    container: andywick/arkime-build-22-arm64:5.0.0-3
    buildopt: "--nothirdparty --kafka"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara8 -d librdkafka1"
    package: deb
    runson: ubuntu-24.04-arm

  - version: ubuntu2404_arm64
    container: andywick/arkime-build-24-arm64:5.0.0-3
    buildopt: "--nothirdparty --kafka"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara10 -d librdkafka1 -d libpcre3"
    package: deb
    runson: ubuntu-24.04-arm

  - version: debian12_arm64
    container: andywick/arkime-build-d12-arm64:5.0.0-7
    buildopt: "--nothirdparty --kafka"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara9 -d librdkafka1 -d libpcre3"
    package: deb
    runson: ubuntu-24.04-arm
    docker: true
    ja4plus: true

  - version: debian13_arm64
    container: andywick/arkime-build-d13-arm64:5.0.0-7
    buildopt: "--nothirdparty --kafka"
    fpmdeps: "-d libwww-perl -d libjson-perl -d ethtool -d libyaml-dev -d liblua5.4-0 -d libmaxminddb0 -d libcurl4 -d libpcap0.8 -d libglib2.0-0 -d libnghttp2-14 -d libyara10 -d librdkafka1"
    package: deb
    runson: ubuntu-24.04-arm

  - version: el9.aarch64
    container: andywick/arkime-build-9-arm64:5.0.0-6
    buildopt: "--kafka"
    fpmdeps: "-d perl-libwww-perl -d perl-JSON -d ethtool -d libyaml -d perl-LWP-Protocol-https -d libzstd"
    package: rpm
    runson: ubuntu-24.04-arm

  - version: el10.aarch64
    container: andywick/arkime-build-10-arm64:5.0.0-7
    buildopt: "--kafka"
    fpmdeps: "-d perl-libwww-perl -d perl-JSON -d ethtool -d libyaml -d perl-LWP-Protocol-https -d libzstd -d libmaxminddb -d libpcap -dlibrdkafka"
    package: rpm
    runson: ubuntu-24.04-arm
