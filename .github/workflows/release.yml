name: "Release"

on:
  workflow_dispatch:
    inputs:
      release_tag:
        description: 'Enter the release tag without leading v (e.g., 1.0.0)'
        required: true

      iteration:
        description: 'Enter the iteration'
        required: true
        default: '1'

permissions:
  contents: read
  packages: read

jobs:
  generate-matrix:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          fetch-depth: 0
          ref: "refs/tags/v${{ github.event.inputs.release_tag }}"

      - name: Generate Matrix JSON with yq
        id: set-matrix
        run: |
          MATRIX=$(yq -o json .github/workflows/versions | jq -c)
          echo "matrix=$MATRIX" >> $GITHUB_OUTPUT

  build-release:
    permissions:
      contents: write
      packages: write

    concurrency:
      group: ${{ github.workflow }}-${{ matrix.version }}-${{ github.event.inputs.release_tag }}
      cancel-in-progress: true

    needs: generate-matrix

    strategy:
      fail-fast: false
      matrix: ${{fromJson(needs.generate-matrix.outputs.matrix)}}

    env:
      PYTHON: ${{ matrix.python || 'python' }}

    runs-on: ${{ matrix.runson || 'ubuntu-latest' }}

    container:
      image: ${{ matrix.container }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          fetch-depth: 0
          ref: "refs/tags/v${{ github.event.inputs.release_tag }}"

      - name: thirdparty
        run: |
          (cd / ; curl https://raw.githubusercontent.com/arkime/arkime-test-data/main/snfmin.tar.gz | tar -zxvf -)
          if [ -d "/thirdparty" ]; then
            ln -s /thirdparty .
          fi
          git config --global --add safe.directory `pwd`

      - name: build and install
        run: |
          ./easybutton-build.sh ${{ matrix.buildopt }} --rminstall
          export PATH=/opt/arkime/bin:$PATH
          make install

      - name: package arch
        if: ${{ matrix.package == 'arch' }}
        run: |
          gem install --no-document fpm rexml erb
          export ARKIME_VERSION="${{ github.event.inputs.release_tag }}"
          /root/.local/share/gem/ruby/3.3.0/bin/fpm -s dir -t pacman -n arkime -x opt/arkime/logs -x opt/arkime/raw -v $ARKIME_VERSION --iteration ${{ github.event.inputs.iteration }} --template-scripts --after-install "release/afterinstall.sh" --url "https://arkime.com" --description "Arkime Full Packet System" ${{ matrix.fpmdeps }} -p arkime-${ARKIME_VERSION}-${{github.event.inputs.iteration}}_${{matrix.version}}.pkg.tar.zst /opt/arkime
          ls -l *.zst

      - name: package rpm
        if: ${{ matrix.package == 'rpm' }}
        run: |
          export ARKIME_VERSION="${{ github.event.inputs.release_tag }}"
          fpm -s dir -t rpm --rpm-digest sha256 -n arkime -x opt/arkime/logs -x opt/arkime/raw -v $ARKIME_VERSION --iteration ${{ github.event.inputs.iteration }} --template-scripts --after-install "release/afterinstall.sh" --url "https://arkime.com" --description "Arkime Full Packet System" ${{ matrix.fpmdeps }} --rpm-rpmbuild-define "_build_id_links none" -p arkime-${ARKIME_VERSION}-${{github.event.inputs.iteration}}.${{matrix.version}}.rpm /opt/arkime
          ls -l *.rpm

      - name: Arkime ENV Variables
        run: |
          arch=$(arch | sed s/aarch64/arm64/ | sed s/x86_64/amd64/)
          echo "ARKIME_ARCH=$arch" >> $GITHUB_ENV

      - name: ja4plus
        if: ${{ matrix.ja4plus }}
        run: |
          (cd .. ; git clone https://github.com/arkime/ja4)
          cp ../ja4/ja4plus.c capture/plugins
          (cd capture/plugins; make)
          mv capture/plugins/ja4plus.so ja4plus.${{ env.ARKIME_ARCH }}.so
          rm -f capture/plugins/ja4plus.c

      - name: moloch el8 package rpm
        if: ${{ matrix.version == 'el8.x86_64' }}
        run: |
          export ARKIME_VERSION="${{ github.event.inputs.release_tag }}"
          rm -rf /data/moloch; mkdir -p /data
          mv /opt/arkime /data/moloch
          ./easybutton-build.sh ${{ matrix.buildopt }} --dir /data/moloch
          (cd capture ; make clean; make; make install)
          (cd release ; make installmoloch)
          mv /data/moloch/bin/capture /data/moloch/bin/moloch-capture
          /bin/cp -f common/version.js /data/moloch/common/

          fpm -s dir -t rpm -n moloch -x data/moloch/logs -x data/molcoh/raw -v $ARKIME_VERSION --iteration ${{ github.event.inputs.iteration }} --template-scripts --url "https://arkime.com" --description "Moloch Full Packet System" ${{ matrix.fpmdeps }} --rpm-rpmbuild-define "_build_id_links none" -p moloch-${ARKIME_VERSION}-${{github.event.inputs.iteration}}.${{matrix.version}}.rpm /data/moloch
          ls -l *.rpm

      - name: package deb
        if: ${{ matrix.package == 'deb' }}
        run: |
          export ARKIME_VERSION="${{ github.event.inputs.release_tag }}"
          fpm -s dir -t deb -n arkime -x opt/arkime/logs -x opt/arkime/raw -v $ARKIME_VERSION --iteration ${{ github.event.inputs.iteration }} --template-scripts --after-install "release/afterinstall.sh" --url "https://arkime.com" --description "Arkime Full Packet System" ${{ matrix.fpmdeps }} -p arkime_${ARKIME_VERSION}-${{github.event.inputs.iteration}}.${{matrix.version}}.deb /opt/arkime
          ls -l *.deb

      - name: moloch ubuntu2004 package rpm
        if: ${{ matrix.version == 'ubuntu2004_amd64' }}
        run: |
          export ARKIME_VERSION="${{ github.event.inputs.release_tag }}"
          rm -rf /data/moloch; mkdir -p /data
          mv /opt/arkime /data/moloch
          ./easybutton-build.sh ${{ matrix.buildopt }} --dir /data/moloch
          (cd capture ; make clean; make; make install)
          (cd release ; make installmoloch)
          mv /data/moloch/bin/capture /data/moloch/bin/moloch-capture
          /bin/cp -f common/version.js /data/moloch/common/

          fpm -s dir -t deb -n moloch -x data/moloch/logs -x data/moloch/raw -v $ARKIME_VERSION --iteration ${{ github.event.inputs.iteration }} --template-scripts --after-install "release/afterinstall.sh" --url "https://arkime.com" --description "Moloch Full Packet System" ${{ matrix.fpmdeps }} -p moloch_${ARKIME_VERSION}-${{github.event.inputs.iteration}}.${{matrix.version}}.deb /data/moloch
          ls -l *.deb

      - name: notice
        if: ${{ matrix.notice }}
        run: |
          export PATH=/opt/arkime/bin:$PATH
          (npm install license-checker; release/notice.txt.pl /opt/arkime NOTICE release/CAPTURENOTICE > NOTICE.txt)

      - name: upload github
        uses: softprops/action-gh-release@v2.0.5
        with:
          tag_name: "v${{ github.event.inputs.release_tag }}"
          draft: false
          prerelease: true
          generate_release_notes: false
          files: |
            *.deb
            *.so
            *.rpm
            *.zst
            NOTICE.txt
          body: |
            ### [Installation Instructions](https://arkime.com/install) | [4.x -> 5.x Upgrade Instructions](https://arkime.com/faq#how_do_i_upgrade_to_arkime_5) | [FAQ](https://arkime.com/faq) | [CHANGELOG](https://raw.githubusercontent.com/arkime/arkime/main/CHANGELOG) | [JA4+ Install](https://arkime.com/ja4) | [Docker Install](https://arkime.com/docker)

            A db.pl upgrade is required when upgrading from 5.1.2 or earlier

            Support Arkime's ongoing development! Become a **[GitHub Sponsor!](https://github.com/sponsors/awick)**

            # :sparkles: What's new :sparkles:

            ### :arrow_down: Download Info :arrow_down:
            We offer downloads for different Linux distributions and versions because of library differences. For example, use the el8 download for Centos 8 or RHEL 8 not RHEL 9. A libssl version error means that most likely the wrong download was used for your Linux distribution and version, please double check. The moloch builds have the old filesystem layouts, we will stop providing the moloch builds in 2025.


############# DOCKER DOCKER DOCKER #############
      - name: Set up Docker Buildx
        if: matrix.docker
        uses: docker/setup-buildx-action@v3

      - name: Log in to registry
        if: matrix.docker
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        if: matrix.docker
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./release/Dockerfile
          push: true
          provenance: false
          tags: |
            ghcr.io/${{ github.repository_owner }}/arkime/arkime:v${{ github.event.inputs.release_tag }}-${{ env.ARKIME_ARCH }}
          build-args: |
            ARKIME_TAG=v${{ github.event.inputs.release_tag }}
            ARKIME_BUILD=${{ github.event.inputs.release_tag }}-${{ github.event.inputs.iteration }}

      - name: Build and push ja4
        if: matrix.docker
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./release/Dockerfile.ja4
          push: true
          provenance: false
          tags: |
            ghcr.io/${{ github.repository_owner }}/arkime/arkime:v${{ github.event.inputs.release_tag }}-${{ env.ARKIME_ARCH }}-ja4
          build-args: |
            ARKIME_BASE=ghcr.io/${{ github.repository_owner }}/arkime/arkime:v${{ github.event.inputs.release_tag }}-${{ env.ARKIME_ARCH }}

############# Docker Manifest #############
  create-manifest:
    needs: build-release
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
    steps:
      - name: Log in to registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create and push manifest
        run: |
          package="ghcr.io/${{ github.repository_owner }}/arkime/arkime"
          export ARKIME_MAJOR_VERSION=`echo ${{github.event.inputs.release_tag}} | cut -c 1-1`

          create_and_push() {
            local tag=$1
            local extra=$2

            docker manifest create ${package}:${tag} \
              ${package}:v${{github.event.inputs.release_tag}}-amd64${extra} \
              ${package}:v${{github.event.inputs.release_tag}}-arm64${extra}
            docker manifest annotate ${package}:${tag} \
              ${package}:v${{github.event.inputs.release_tag}}-amd64${extra} --arch amd64
            docker manifest annotate ${package}:${tag} \
              ${package}:v${{github.event.inputs.release_tag}}-arm64${extra} --arch arm64
            docker manifest push ${package}:${tag}
          }

          ### Normal ###
          create_and_push "v${{github.event.inputs.release_tag}}" ""
          create_and_push "v${ARKIME_MAJOR_VERSION}-latest" ""
          create_and_push "latest" ""

          ### JA4+ ###
          create_and_push "v${{github.event.inputs.release_tag}}-ja4" "-ja4"
          create_and_push "v${ARKIME_MAJOR_VERSION}-ja4-latest" "-ja4"
          create_and_push "ja4-latest" "-ja4"

############# Slack Message#############
  slack:
    runs-on: ubuntu-latest
    needs:
      - build-release
    steps:
      - name: send
        env:
          SLACK_URL: ${{ secrets.SLACK_URL }}
        run: |
          echo "Sending msg"
          export ARKIME_VERSION="v${{ github.event.inputs.release_tag }}"
          echo ARKIME_VERSION: $ARKIME_VERSION
          BODY="{\"icon_emoji\": \":sushi:\", \"username\": \"GitAction\", \"text\":\"Release $ARKIME_VERSION worked!!!\"}"
          curl -XPOST -H "Content-type: application/json" --data "$BODY" $SLACK_URL

