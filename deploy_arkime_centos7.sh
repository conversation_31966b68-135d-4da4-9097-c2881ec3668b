#!/bin/bash
# Arkime 在CentOS 7.4上的完整部署脚本
# 支持在线和离线安装模式
# 目标服务器: 192.168.31.129
# 用户: root/VictoR#.0.0

set -e

# 配置变量
ARKIME_USER="arkime"
ARKIME_DIR="/opt/arkime"
DATA_DIR="/opt/arkime/data"
LOG_DIR="/opt/arkime/logs"
CONFIG_FILE="/opt/arkime/etc/config.ini"
NODE_VERSION="20.19.2"
ELASTICSEARCH_VERSION="7.17.15"

# 服务器信息
TARGET_SERVER="192.168.31.129"
TARGET_USER="root"
TARGET_PASS="VictoR#.0.0"
DEPLOY_DIR="/home/<USER>/DataSource"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_info "检查系统版本..."
    
    if [[ ! -f /etc/redhat-release ]]; then
        log_error "此脚本仅支持CentOS/RHEL系统"
        exit 1
    fi
    
    local version=$(cat /etc/redhat-release)
    log_info "系统版本: $version"
    
    if [[ ! "$version" =~ CentOS.*7\. ]]; then
        log_warning "建议使用CentOS 7.x系统"
    fi
    
    log_success "系统检查完成"
}

# 配置系统环境
configure_system() {
    log_info "配置系统环境..."
    
    # 关闭SELinux
    setenforce 0 2>/dev/null || true
    sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config
    
    # 配置防火墙
    systemctl stop firewalld 2>/dev/null || true
    systemctl disable firewalld 2>/dev/null || true
    
    # 配置时区
    timedatectl set-timezone Asia/Shanghai
    
    # 增加文件描述符限制
    cat >> /etc/security/limits.conf << EOF
* soft nofile 65536
* hard nofile 65536
* soft nproc 65536
* hard nproc 65536
EOF
    
    # 配置内核参数
    cat >> /etc/sysctl.conf << EOF
# Arkime优化参数
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.core.netdev_max_backlog = 30000
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
vm.max_map_count = 262144
EOF
    
    sysctl -p
    
    log_success "系统环境配置完成"
}

# 创建用户和目录
create_user_and_dirs() {
    log_info "创建Arkime用户和目录..."
    
    # 创建arkime用户
    if ! id "$ARKIME_USER" &>/dev/null; then
        useradd -r -s /bin/false "$ARKIME_USER"
        log_info "创建用户: $ARKIME_USER"
    fi
    
    # 创建目录
    mkdir -p "$ARKIME_DIR"/{bin,etc,logs,data,raw}
    mkdir -p "$DATA_DIR"
    mkdir -p "$LOG_DIR"
    
    # 设置权限
    chown -R "$ARKIME_USER:$ARKIME_USER" "$ARKIME_DIR"
    chmod 755 "$ARKIME_DIR"
    
    log_success "用户和目录创建完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新系统
    yum update -y
    
    # 安装EPEL仓库
    yum install -y epel-release
    
    # 安装开发工具
    yum groupinstall -y "Development Tools"
    
    # 安装必要的软件包
    yum install -y \
        wget curl git vim \
        gcc gcc-c++ make cmake \
        autoconf automake libtool pkgconfig \
        zlib-devel openssl-devel pcre-devel \
        libyaml-devel glib2-devel \
        libpcap-devel libcurl-devel \
        perl perl-libwww-perl perl-JSON \
        perl-LWP-Protocol-https \
        ethtool tcpdump \
        java-1.8.0-openjdk java-1.8.0-openjdk-devel \
        python-pip \
        htop iotop nethogs \
        rsync screen tmux
    
    log_success "系统依赖安装完成"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js ${NODE_VERSION}..."
    
    local node_url="https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-linux-x64.tar.xz"
    local node_file="/tmp/node-v${NODE_VERSION}-linux-x64.tar.xz"
    
    # 下载Node.js
    if [[ ! -f "$node_file" ]]; then
        wget -O "$node_file" "$node_url"
    fi
    
    # 解压到/opt/arkime
    tar -xf "$node_file" -C "$ARKIME_DIR"
    
    # 创建符号链接
    ln -sf "$ARKIME_DIR/node-v${NODE_VERSION}-linux-x64/bin/node" /usr/local/bin/node
    ln -sf "$ARKIME_DIR/node-v${NODE_VERSION}-linux-x64/bin/npm" /usr/local/bin/npm
    
    # 验证安装
    node --version
    npm --version
    
    log_success "Node.js安装完成"
}

# 编译第三方依赖
compile_thirdparty() {
    log_info "编译第三方依赖..."
    
    local build_dir="/tmp/arkime_thirdparty"
    mkdir -p "$build_dir"
    cd "$build_dir"
    
    # 编译libmaxminddb
    log_info "编译libmaxminddb..."
    wget https://github.com/maxmind/libmaxminddb/releases/download/1.7.1/libmaxminddb-1.7.1.tar.gz
    tar -xzf libmaxminddb-1.7.1.tar.gz
    cd libmaxminddb-1.7.1
    ./configure --prefix=/usr/local
    make -j$(nproc)
    make install
    cd ..
    
    # 编译yara
    log_info "编译yara..."
    wget https://github.com/VirusTotal/yara/archive/v4.2.3.tar.gz -O yara-4.2.3.tar.gz
    tar -xzf yara-4.2.3.tar.gz
    cd yara-4.2.3
    ./bootstrap.sh
    ./configure --prefix=/usr/local
    make -j$(nproc)
    make install
    cd ..
    
    # 更新库路径
    echo "/usr/local/lib" > /etc/ld.so.conf.d/arkime.conf
    ldconfig
    
    log_success "第三方依赖编译完成"
}

# 编译Arkime
compile_arkime() {
    log_info "编译Arkime..."
    
    cd "$(dirname "$0")"
    
    # 配置编译选项
    ./configure \
        --prefix="$ARKIME_DIR" \
        --with-libpcap=/usr \
        --with-yara=/usr/local \
        --with-maxminddb=/usr/local
    
    # 编译
    make -j$(nproc)
    
    log_success "Arkime编译完成"
}

# 安装Arkime
install_arkime() {
    log_info "安装Arkime..."
    
    cd "$(dirname "$0")"
    
    # 安装二进制文件
    make install
    
    # 安装npm依赖
    cd "$ARKIME_DIR"
    npm ci --production
    
    # 设置权限
    chown -R "$ARKIME_USER:$ARKIME_USER" "$ARKIME_DIR"
    
    log_success "Arkime安装完成"
}

# 安装Elasticsearch
install_elasticsearch() {
    log_info "安装Elasticsearch ${ELASTICSEARCH_VERSION}..."
    
    # 添加Elasticsearch仓库
    cat > /etc/yum.repos.d/elasticsearch.repo << EOF
[elasticsearch]
name=Elasticsearch repository for 7.x packages
baseurl=https://artifacts.elastic.co/packages/7.x/yum
gpgcheck=1
gpgkey=https://artifacts.elastic.co/GPG-KEY-elasticsearch
enabled=0
autorefresh=1
type=rpm-md
EOF
    
    # 导入GPG密钥
    rpm --import https://artifacts.elastic.co/GPG-KEY-elasticsearch
    
    # 安装Elasticsearch
    yum install -y --enablerepo=elasticsearch elasticsearch
    
    # 配置Elasticsearch
    cat > /etc/elasticsearch/elasticsearch.yml << EOF
cluster.name: arkime
node.name: arkime-node-1
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch
network.host: 127.0.0.1
http.port: 9200
discovery.type: single-node
xpack.security.enabled: false
EOF
    
    # 启动Elasticsearch
    systemctl daemon-reload
    systemctl enable elasticsearch
    systemctl start elasticsearch
    
    # 等待Elasticsearch启动
    log_info "等待Elasticsearch启动..."
    for i in {1..30}; do
        if curl -s http://localhost:9200 >/dev/null; then
            break
        fi
        sleep 2
    done
    
    log_success "Elasticsearch安装完成"
}

# 配置Arkime
configure_arkime() {
    log_info "配置Arkime..."
    
    # 创建配置文件
    cat > "$CONFIG_FILE" << EOF
[default]
elasticsearch=http://127.0.0.1:9200
interface=eth0
pcapDir=${DATA_DIR}/raw
logDir=${LOG_DIR}
pluginsDir=${ARKIME_DIR}/plugins
parsersDir=${ARKIME_DIR}/parsers
maxFileSizeG=12
tcpTimeout=600
tcpSaveTimeout=720
udpTimeout=30
icmpTimeout=10
maxPackets=10000
minFreeSpaceG=100
viewPort=8005
certFile=${ARKIME_DIR}/etc/arkime.cert
keyFile=${ARKIME_DIR}/etc/arkime.key
passwordSecret=CHANGE_ME_PLEASE
httpRealm=Arkime
userNameHeader=arkime-user
webBasePath=/
maxReqBody=20971520
compressES=false
antiSynDrop=false
readTruncatedPackets=false
trackESP=false

[logging]
level=info

[override]
# 可以在这里添加特定配置覆盖
EOF
    
    # 生成SSL证书
    openssl req -new -newkey rsa:2048 -days 365 -nodes -x509 \
        -keyout "${ARKIME_DIR}/etc/arkime.key" \
        -out "${ARKIME_DIR}/etc/arkime.cert" \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=Arkime/CN=arkime.local"
    
    # 设置权限
    chown "$ARKIME_USER:$ARKIME_USER" "$CONFIG_FILE"
    chown "$ARKIME_USER:$ARKIME_USER" "${ARKIME_DIR}/etc/arkime.key"
    chown "$ARKIME_USER:$ARKIME_USER" "${ARKIME_DIR}/etc/arkime.cert"
    chmod 600 "${ARKIME_DIR}/etc/arkime.key"
    
    log_success "Arkime配置完成"
}

# 初始化Elasticsearch索引
init_elasticsearch() {
    log_info "初始化Elasticsearch索引..."
    
    cd "$ARKIME_DIR/db"
    ./db.pl http://127.0.0.1:9200 init
    
    log_success "Elasticsearch索引初始化完成"
}

# 创建systemd服务
create_systemd_services() {
    log_info "创建systemd服务..."
    
    # Arkime Capture服务
    cat > /etc/systemd/system/arkimecapture.service << EOF
[Unit]
Description=Arkime Capture
After=network.target elasticsearch.service

[Service]
Type=simple
Restart=on-failure
StandardOutput=journal
StandardError=journal
ExecStart=${ARKIME_DIR}/bin/capture -c ${CONFIG_FILE}
WorkingDirectory=${ARKIME_DIR}
LimitNOFILE=1048576
LimitNPROC=1048576
User=${ARKIME_USER}
Group=${ARKIME_USER}

[Install]
WantedBy=multi-user.target
EOF
    
    # Arkime Viewer服务
    cat > /etc/systemd/system/arkimeviewer.service << EOF
[Unit]
Description=Arkime Viewer
After=network.target elasticsearch.service

[Service]
Type=simple
Restart=on-failure
StandardOutput=journal
StandardError=journal
ExecStart=${ARKIME_DIR}/bin/node ${ARKIME_DIR}/viewer/viewer.js -c ${CONFIG_FILE}
WorkingDirectory=${ARKIME_DIR}
User=${ARKIME_USER}
Group=${ARKIME_USER}
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_success "systemd服务创建完成"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 启动脚本
    cat > "${ARKIME_DIR}/bin/arkime-start.sh" << 'EOF'
#!/bin/bash
echo "启动Arkime服务..."
systemctl start elasticsearch
sleep 10
systemctl start arkimecapture
systemctl start arkimeviewer
echo "Arkime服务启动完成"
EOF
    
    # 停止脚本
    cat > "${ARKIME_DIR}/bin/arkime-stop.sh" << 'EOF'
#!/bin/bash
echo "停止Arkime服务..."
systemctl stop arkimeviewer
systemctl stop arkimecapture
systemctl stop elasticsearch
echo "Arkime服务停止完成"
EOF
    
    # 状态检查脚本
    cat > "${ARKIME_DIR}/bin/arkime-status.sh" << 'EOF'
#!/bin/bash
echo "=== Arkime服务状态 ==="
echo "Elasticsearch:"
systemctl status elasticsearch --no-pager -l
echo ""
echo "Arkime Capture:"
systemctl status arkimecapture --no-pager -l
echo ""
echo "Arkime Viewer:"
systemctl status arkimeviewer --no-pager -l
EOF
    
    chmod +x "${ARKIME_DIR}/bin/arkime-"*.sh
    
    log_success "管理脚本创建完成"
}

# 主安装流程
main() {
    log_info "开始Arkime部署..."
    
    check_root
    check_system
    configure_system
    create_user_and_dirs
    install_system_dependencies
    install_nodejs
    compile_thirdparty
    compile_arkime
    install_arkime
    install_elasticsearch
    configure_arkime
    init_elasticsearch
    create_systemd_services
    create_management_scripts
    
    log_success "Arkime部署完成!"
    log_info "Web界面地址: https://localhost:8005"
    log_info "默认用户名/密码: admin/admin"
    log_info "使用以下命令管理服务:"
    log_info "  启动: ${ARKIME_DIR}/bin/arkime-start.sh"
    log_info "  停止: ${ARKIME_DIR}/bin/arkime-stop.sh"
    log_info "  状态: ${ARKIME_DIR}/bin/arkime-status.sh"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
