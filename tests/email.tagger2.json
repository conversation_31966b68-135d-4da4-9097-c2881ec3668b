#field:tagger.str;kind:lotermfield;count:true;friendly:Str;db:tagger.str;help:Help String;shortcut:0
#field:tagger.int;kind:integer;count:true;friendly:Int;db:tagger.int;help:Help Int;shortcut:1
#field:tags;shortcut:2
#view:if (session.tagger.str)
#view:  div.sessionDetailMeta.bold Tagger
#view:  dl.sessionDetailMeta.bold Tagger
#view:    +arrayList(session.tagger, 'str', 'Str', 'tagger.str')
<EMAIL>;email.dst=added1;2=srcmatch;0=house;tagger.str=boat;dontSaveSPI=0
<EMAIL>;email.src=added2;tags=dstmatch;1=1;tagger.int=3

