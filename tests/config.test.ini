[default]
getSessionBySearch=true
#aes256Encryption=true
elasticsearch= , , http://127.0.0.1:9200,
#elasticsearch=elastic:changeme@127.0.0.1:9200
#elasticsearch=http://[::1]:9200
elasticsearchScrollTimeout=90
pcapDir=/tmp
geoLite2ASN=GeoLite2-ASN.mmdb
geoLite2Country=GeoLite2-Country.mmdb
rirFile = ipv4-address-space.csv
ouiFile = oui.txt
parsersDir = ../capture/parsers;parsers
pluginsDir = plugins;../tests/plugins;../capture/plugins
yara=rules.yara
smtpIpHeaders=X-Originating-IP:;X-Barracuda-Apparent-Source-IP:
keyFile=
spiDataMaxIndices=-1
parseQSValue=true
parseCookieValue=true
passwordSecret=password
viewPort=8123
viewerPlugins=wise.js
icmpTimeout=60
rulesFiles=rules.yaml
supportSha256=true
maxStreams=1000
sessionIdTracking=vlan
trackESP=true
parseSMTPHeaderAll=true
parseHTTPHeaderRequestAll=true
parseHTTPHeaderResponseAll=true
dnsOutputAnswers=true
autoGenerateId=true
enablePacketLen=true
ja3Strings=true
maxReqBody=2000
disableParsers=
maxESConns=3
enablePacketDedup=false
authMode=regressionTests
ja4Raw=true
parseDNSRecordAll=true

packetThreads=2
magicMode=basic
filenameOps=tags=/gre-(.*)\\.pcap%gretest-\\1;tags=dns-error%error-dns

esAdminUsers=adminuser1,admin

scrubspi=http.uri=/sheepskin%20boots/Sheepskin%20Boots/

# Stuff for rules testing
rules_communityId=FuiRMncy2mhAkCJ4mYumMiRK0uw=
rules_value_2=2

[scrubspi]
http.uri=/sheepskin%20boots/Sheepskin%20Boots/

[user-setting-defaults]
timezone=gmt
; manualQuery=true
; timelineDataFilters=network.packets;network.bytes

[test]
pcapWriteMethod=simple
interface=eth1
prefix=tests
passwordSecret=
plugins=test.so;tagger.so;wise.so;scrubspi.so
cronQueries=true
dontSaveBPFs=port 12345
viewUrl=http://localhost:8123
gapPacketPos=false

[test-pois]
pcapReadMethod=pcap-over-ip-server
pcapWriteMethod=simple
interface=eth1
prefix=tests
passwordSecret=
dontSaveBPFs=port 12345
viewUrl=http://localhost:8123
gapPacketPos=true

[test-poic]
pcapReadMethod=pcap-over-ip-client
pcapWriteMethod=simple
interface=localhost:57012;localhost:57013;localhost:443
prefix=tests
passwordSecret=
dontSaveBPFs=port 12345
viewUrl=http://localhost:8123
gapPacketPos=true

[testuser]
pcapWriteMethod=simple
interface=eth1
prefix=tests
passwordSecret=secret
plugins=test.so;tagger.so;wise.so
cronQueries=true
dontSaveBPFs=port 12345
webBasePath = /arkime/
esAdminUsers=admin
businessDayStart=9
businessDayEnd=17
businessDays=1,2,3,4,5
arkimeWebURL = localhost:8123/arkime/
turnOffGraphDays = 30
disableUserPasswordUI=false
loginMessage=Welcome! Test form auth...
defaultTimeRange=-1
#spiViewCategoryOrder=cert,ldap,quic,unknown,tls

# Digest Auth
authMode=digest
# Form Auth
#authMode=form
# KeyCloak
#authMode=oidc
#authCookieSecure=false
#authDiscoverURL=http://localhost:8080/realms/master/.well-known/openid-configuration
#authClientId=andy-oidc-client
#authClientSecret=andy
#authUserIdField=preferred_username
#authRedirectURIs=http://localhost:8123/arkime/auth/login/callback

[testmulties]
pcapWriteMethod=simple
interface=eth1
prefix=tests
usersPrefix=tests
passwordSecret=secret
plugins=test.so;tagger.so;wise.so
dontSaveBPFs=port 12345
webBasePath = /arkime/
usersElasticsearch=http://127.0.0.1:9200
elasticsearch=http://127.0.0.1:8200
multiES=true
multiESPort=8200
multiESNodes=127.0.0.1:9200,prefix:tests,name:test;127.0.0.1:9200,prefix:tests2_,name:test2;127.0.0.1:9201,prefix:tests3_,name:test3
isLocalViewRegExp=testmulties
authMode=digest

#rootPlugins=reader-daq.so
#pcapReadMethod=daq

[nowise]
prefix=tests
passwordSecret=
plugins=test.so;tagger.so
interface=en0
packetThreads=1

[suricata]
prefix=tests
passwordSecret=
plugins=suricata.so
interface=en0
packetThreads=1
suricataExpireMinutes=6000
suricataAlertFile=eve.json

[nowisekek]
prefix=tests
passwordSecret=
plugins=test.so;tagger.so
interface=en0
simpleEncoding=aes-256-ctr
simpleKEKId=test

[test2]
viewPort=8124
prefix=tests2
cronQueries=true
usersPrefix=tests
usersElasticsearch=http://127.0.0.1:9200
passwordSecret=
plugins=test.so;tagger.so
viewerPlugins=writer-s3

[test3]
viewPort=8126
prefix=tests2
cronQueries=false
usersPrefix=tests
usersElasticsearch=http://127.0.0.1:9200
passwordSecret=testtest
authMode=header
userNameHeader=arkime_user
userAutoCreateTmpl={"userId": "${this.arkime_user}", "userName": "${this.arkime_user_name}", "enabled": true, "webEnabled": true, "headerAuthEnabled": true, "emailSearch": true, "createEnabled": false, "removeEnabled": false, "packetSearch": true }
uploadRoles=parliamentUser
uploadCommand=/bin/true

[all]
viewPort=8125
passwordSecret=
elasticsearch=http://127.0.0.1:8200
usersElasticsearch=http://127.0.0.1:9200
usersPrefix=tests
multiES=true
multiESPort=8200
multiESNodes=127.0.0.1:9200,prefix:tests,name:test;127.0.0.1:9200,prefix:tests2_,name:test2
#multiESNodes=elastic:changeme@127.0.0.1:9200,prefix:tests,name:test;elastic:changeme@127.0.0.1:9200,prefix:tests2_,name:test2

[s3]
prefix=tests2
plugins=writer-s3
pcapWriteMethod=s3
s3Region=us-west-2
s3Bucket=andywick-testbucket-1
packetThreads=1

[minio]
prefix=tests
plugins=writer-s3.so
pcapWriteMethod=s3
viewerPlugins=writer-s3/index.js
s3AccessKeyId=admin
s3SecretAccessKey=password
s3Region=us-east-1
s3Bucket=arkime
s3Host=localhost:9000
s3UseHttp=true
s3PathAccessStyle=true
packetThreads=1
s3Compression=none

[lua]
pcapWriteMethod=simple
plugins=lua.so
cronQueries=true
luaFiles=../capture/plugins/lua/moloch.lua

[headers-http-request]
referer=type:string;count:true;unique:true
authorization=type:string;count:true

# headers-http-response is a special section to add response headers to index
[headers-http-response]
location=type:string

[headers-email]
x-priority=type:integer
x-elnk-trace=type:lotermfield

[override-ips]
********=tag:srcip;asn:AS0000 This is neat;country:RU
********=tag:dstip;asn:AS0001 Cool Beans!;rir:TEST;country:CA
********=asn:AS0002 Hmm!@#$%^&*()
10.180/16=country:US

[moloch-clusters]
test2=url:http://localhost:8124;passwordSecret:password;name:Test2

[wise-types]
mac=db:source.mac;mac.dst

# WISE Service Config, usually in its own file, but it can live in the same
[wiseService]

[file:email]
file=../tests/email.wise
tags=emailwise
type=email
format=tagger

[file:ip]
file=../tests/ip.wise
tags=ipwise,ipwise2
type=ip
format=tagger

[file:mac]
file=../tests/mac.wise
tags=macwise
type=mac
format=tagger

[file:ipcsv]
file=../tests/ip.wise.csv
tags=ipwisecsv
type=ip
column=1

[file:ipjson]
file=../tests/ip.wise.json
tags=ipwisejson
type=ip
format=json
keyColumn=ip
fields=field:tags;shortcut:tag

[file:md5]
file=../tests/md5.wise
tags=md5wise
type=md5
format=tagger

[file:ja3]
file=../tests/ja3.wise
tags=ja3wise
type=ja3
format=tagger

[file:sha256]
file=../tests/sha256.wise
tags=sha256wise
type=sha256
format=tagger

[file:domain]
file=../tests/domain.wise
tags=domainwise
type=domain
format=tagger

[file:url]
file=../tests/uri.wise
tags=urlwise
type=url
format=tagger

#[url:zeus.ips]
#url=https://zeustracker.abuse.ch/blocklist.php?download=ipblocklist
#tags=zeustracker,botnet
#type=ip
#reload=60

#[url:zeus.domain]
#url=https://zeustracker.abuse.ch/blocklist.php?download=domainblocklist
#tags=zeustracker,botnet
#type=domain
#reload=60

[value-actions]
VTIP=url:https://www.virustotal.com/en/ip-address/%TEXT%/information/;name:Virus Total IP;category:ip
VTHOST=url:https://www.virustotal.com/en/domain/%HOST%/information/;name:Virus Total Host;category:host
VTURL=url:https://www.virustotal.com/latest-scan/%URL%;name:Virus Total URL;category:url
ALLTESTConfig=url:http:/www.example.com;name:AllConfigTest;all:true

[field-actions]
ASDF=url:https://www.asdf.com?expression=%EXPRESSION%&date=%DATE%&field=%FIELD%&dbField=%DBFIELD%;name:Field Action %FIELDNAME%!;category:ip;users:admin,sac-test1
ALLTEST=url:https://www.asdf.com?expression=%EXPRESSION%&date=%DATE%&field=%FIELD%&dbField=%DBFIELD%;name:All Field Action %FIELDNAME%!;all:true

[custom-fields]
iscool=kind:termfield;count:true;friendly:Is cool;db:iscool;help:Is Cool String;viewerOnly:true
sample.md5=db:sample.md5;kind:lotermfield;friendly:Sample MD5;count:true;help:MD5 of the sample
asset.src=kind:lotermfield;count:true;friendly:Asset Src;db:assetName.src;help:Asset Name Src
asset.dst=kind:lotermfield;count:true;friendly:Asset Dst;db:assetName.dst;help:Asset Name Dst

[custom-fields-remap]
asset=ip.src=asset.src;ip.dst=asset.dst

[custom-views]
nice=title:Nice;require:http;fields:ip.src,iscool,http.host
sample=title:Sample;require:sample;fields:sample.md5

[reversedns]
ips=0.0.0.0/32
field=asset

[keks]
test=abc123

[esproxy]
prefix=tests
elasticsearch=http://localhost:9200

[esproxy-sensors]
test=pass:test;ip:127.0.0.1,*******,::1
test2=pass:test2;ip:*******,127.0.0.1
nowise=pass:nowise;ip:*******,127.0.0.1

[vlan-vni-collapse]
0=50,100,300,400,500
