{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 18761}, "destination": {"as": {"full": "AS63949 Linode, LLC", "number": 63949, "organization": {"name": "Linode, LLC"}}, "bytes": 48832, "geo": {"country_iso_code": "JP"}, "ip": "***************", "packets": 48, "port": 80}, "dstPayload8": "0000180400000000", "dstRIR": "ARIN", "dstTTL": [50], "dstTTLCnt": 1, "fileId": [], "firstPacket": 1593995681271, "http": {"host": ["nghttp2.org"], "hostCnt": 1, "md5": ["4b806c16dac801940f7075fd9d9b7bae", "4bf2f259ee1245e1b0cbe6f0127228b5"], "md5Cnt": 2, "method": ["GET"], "methodCnt": 1, "path": ["/", "/stylesheets/screen.css"], "pathCnt": 2, "requestHeader": ["accept", "accept-encoding", "continuation-test-1", "continuation-test-2", "continuation-test-3", "continuation-test-4", "continuation-test-5", "continuation-test-6", "user-agent"], "requestHeaderCnt": 9, "requestHeaderField": ["accept", "accept-encoding", "continuation-test-1", "continuation-test-2", "continuation-test-3", "continuation-test-4", "continuation-test-5", "continuation-test-6"], "requestHeaderValue": ["*/*", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------", "gzip, deflate"], "requestHeaderValueCnt": 8, "responseHeader": ["accept-encoding", "accept-ranges", "alt-svc", "content-length", "content-type", "date", "etag", "last-modified", "server", "user-agent", "via", "x-backend-header-rtt", "x-content-type-options", "x-frame-options", "x-http2-push", "x-xss-protection"], "responseHeaderCnt": 16, "responseHeaderField": ["accept-encoding", "accept-ranges", "accept-ranges", "alt-svc", "alt-svc", "content-length", "content-length", "content-type", "content-type", "date", "date", "etag", "etag", "last-modified", "last-modified", "server", "server", "user-agent", "via", "via", "x-backend-header-rtt", "x-backend-header-rtt", "x-content-type-options", "x-content-type-options", "x-frame-options", "x-frame-options", "x-http2-push", "x-xss-protection", "x-xss-protection"], "responseHeaderValue": ["\"5ed644c7-19d8\"", "\"5ed644c7-98aa\"", "0.001873", "0.001992", "1", "1; mode=block", "1; mode=block", "2 nghttpx", "2 nghttpx", "39082", "6616", "bytes", "bytes", "gzip, deflate", "h3-28=\":4433\"; ma=3600", "h3-28=\":4433\"; ma=3600", "mon, 06 jul 2020 00:34:42 gmt", "mon, 06 jul 2020 00:34:42 gmt", "nghttp2/1.41.0", "nghttpx", "nghttpx", "nosniff", "nosniff", "sameorigin", "sameorigin", "text/css", "text/html", "tue, 02 jun 2020 12:23:35 gmt", "tue, 02 jun 2020 12:23:35 gmt"], "responseHeaderValueCnt": 29, "sha256": ["80d4404b834c034238ac2e3caeb3a6f24dd093432fd13c5f0e64da9b0d9334bb", "cfd844f4f94b56e0d93d6ad47530ba9030cc8d8e41ad6ae53b30fe930efa774f"], "sha256Cnt": 2, "statuscode": [200], "statuscodeCnt": 1, "useragent": ["nghttp2/1.41.0"], "useragentCnt": 1}, "initRTT": 105, "ipProtocol": 6, "lastPacket": 1593995683015, "length": 1745, "network": {"bytes": 70573, "community_id": "1:t8w2y51bTGSv9Z4iWI9ZJSV2gxg=", "packets": 101}, "node": "test", "packetLen": [84, 80, 72, 1426, 1426, 1426, 72, 72, 1426, 114, 1426, 1426, 1426, 1426, 1426, 390, 72, 1426, 72, 72, 1426, 1426, 1426, 870, 72, 128, 72, 1426, 1426, 72, 1426, 1426, 72, 1426, 1426, 72, 1426, 1426, 72, 1426, 72, 1426, 72, 72, 1426, 1426, 72, 72, 1426, 1426, 72, 72, 1426, 1426, 72, 72, 1426, 1426, 72, 72, 1426, 1426, 72, 72, 1426, 72, 1426, 72, 1426, 72, 1426, 72, 1426, 72, 1426, 72, 85, 1426, 1426, 72, 1426, 1426, 72, 72, 85, 1426, 1426, 72, 1426, 1426, 72, 74, 72, 72, 89, 72, 72, 72, 72, 72, 72], "packetPos": [24, 108, 188, 260, 1686, 3112, 4538, 4610, 4682, 6108, 6222, 7648, 9074, 10500, 11926, 13352, 13742, 13814, 15240, 15312, 15384, 16810, 18236, 19662, 20532, 20604, 20732, 20804, 22230, 23656, 23728, 25154, 26580, 26652, 28078, 29504, 29576, 31002, 32428, 32500, 33926, 33998, 35424, 35496, 35568, 36994, 38420, 38492, 38564, 39990, 41416, 41488, 41560, 42986, 44412, 44484, 44556, 45982, 47408, 47480, 47552, 48978, 50404, 50476, 50548, 51974, 52046, 53472, 53544, 54970, 55042, 56468, 56540, 57966, 58038, 59464, 59536, 59621, 61047, 62473, 62545, 63971, 65397, 65469, 65541, 65626, 67052, 68478, 68550, 69976, 71402, 71474, 71548, 71620, 71692, 71781, 71853, 71925, 71997, 72069, 72141], "protocol": ["http2", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 46136}, "source": {"bytes": 21741, "ip": "***********", "packets": 53, "port": 49570}, "srcPayload8": "505249202a204854", "srcTTL": [64], "srcTTLCnt": 1, "tcpflags": {"ack": 73, "dstZero": 0, "fin": 2, "psh": 24, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 2262989232}, "totDataBytes": 64897}, "header": {"index": {"_index": "tests_sessions3-200706"}}}]}