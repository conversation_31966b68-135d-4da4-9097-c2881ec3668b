{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 325}, "destination": {"as": {"full": "AS1 Cool Beans!", "number": 1, "organization": {"name": "Cool Beans!"}}, "bytes": 60, "geo": {"country_iso_code": "CA"}, "ip": "********", "mac": ["00:00:0c:07:ac:01", "00:d0:2b:d1:76:00"], "mac-cnt": 2, "packets": 1, "port": 25}, "dstOui": ["Cisco Systems, Inc", "Jetcell, Inc."], "dstOuiCnt": 2, "dstRIR": "TEST", "dstTTL": [54], "dstTTLCnt": 1, "email": {"dst": ["<EMAIL>"], "dstCnt": 1, "header": ["from", "subject", "to"], "headerCnt": 3, "smtpHello": ["xxxxxx.xxxxxx.xxx.com"], "smtpHelloCnt": 1, "src": ["<EMAIL>"], "srcCnt": 1, "subject": ["xxxxxxxxxxxxx: xxxxêxx xxéxxxxxxx  xx xxx xxx xxxxxxxxxx  x xx xxxxxxxxxxxxxxx - xxxx"], "subjectCnt": 1}, "ethertype": 2048, "fileId": [], "firstPacket": 1394730057309, "initRTT": 1, "ipProtocol": 6, "lastPacket": 1394730057312, "length": 2, "network": {"bytes": 621, "community_id": "1:qditMDravu+OcbnNpvCWlD6jMU0=", "packets": 5}, "node": "test", "packetLen": [90, 76, 70, 395, 70], "packetPos": [24, 114, 190, 260, 655], "protocol": ["smtp", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 561, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 4, "port": 58802}, "srcOui": ["Dell Inc."], "srcOuiCnt": 1, "srcPayload8": "45484c4f20787878", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["dstip", "srcip"], "tagsCnt": 2, "tcpflags": {"ack": 1, "dstZero": 0, "fin": 1, "psh": 1, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 2966903064}, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [33554442], "string.snow": ["16777226:58802,33554442:25"]}, "totDataBytes": 325}, "header": {"index": {"_index": "tests_sessions3-140313"}}}]}