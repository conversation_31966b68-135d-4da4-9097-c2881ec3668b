{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 5222}, "destination": {"bytes": 5240, "ip": "************", "mac": ["00:19:56:bb:8d:70"], "mac-cnt": 1, "packets": 44, "port": 4000}, "dstOui": ["Cisco Systems, Inc"], "dstOuiCnt": 1, "dstPayload8": "485454502f312e31", "dstRIR": "ARIN", "dstTTL": [62], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1259023909119, "http": {"clientVersion": ["1.1"], "clientVersionCnt": 1, "host": [""], "hostCnt": 1, "md5": ["160f27030f302ad5db80621bb0fc67e3", "5152b54f1af8b7ca5ca51a3fbd0b2e4c", "82ad8b9cf95d91f6a5a7cbeb3480f003", "9e0176ab1582bb20858ba6fe7e202130"], "md5Cnt": 4, "method": ["POST"], "method-POST": 4, "methodCnt": 1, "path": ["/exhmedmx"], "pathCnt": 1, "requestBody": ["<NWIXML:notificationSequence xmlns:NWIXML=\"http://www.nokia.com\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:NX2SFM=\"http://www.nokia.com/nx2sfm\" xsi:schemaLocation=\"http://www.nokia.com exhalagx.xsd\"><notification><fixedHeader><domainType>com.nokia.ntc::NWIXML</domainType><typeName>alarmEvent</typeName><eventName></eventName></fixedHeader><optionalHeader><sender>MSCi-945649</sender><eventTime>2009-11-24T02:00:00+00:00</eventTime></optionalHeader><eventBody><NX2SFM:alarmNew systemDN=\"MSCi-945649/FU-OMU-0\"><NX2SFM:eventTime>2009-11-24T02:00:00+00:00</NX2SFM:eventTime><NX2SFM:specificProblem>1093</NX2SFM:specificProblem><NX2SFM:alarmText>OUTPUT FAILURE IN FIELD REPORTING</NX2SFM:alarmText><NX2SFM:additionalText1>10057d 05 \r\n</NX2SFM:additionalText1><NX2SFM:additionalText2></NX2SFM:additionalText2><NX2SFM:additionalText3></NX2SFM:additionalText3><NX2SFM:perceivedSeverity>warning</NX2SFM:perceivedSeverity><NX2SFM:eventType>equipment</NX2SFM:eventType><NX2SFM:alarmId>65535</NX2SFM:alarmId></NX2SFM:alarmNew></eventBody></notification></NWIXML:notificationSequence>", "<NWIXML:notificationSequence xmlns:NWIXML=\"http://www.nokia.com\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:NX2SFM=\"http://www.nokia.com/nx2sfm\" xsi:schemaLocation=\"http://www.nokia.com exhalagx.xsd\"><notification><fixedHeader><domainType>com.nokia.ntc::NWIXML</domainType><typeName>alarmEvent</typeName><eventName></eventName></fixedHeader><optionalHeader><sender>MSCi-945649</sender><eventTime>2009-11-24T02:00:07+00:00</eventTime></optionalHeader><eventBody><NX2SFM:alarmNew systemDN=\"MSCi-945649/FU-VTP-24\"><NX2SFM:eventTime>2009-11-24T02:00:07+00:00</NX2SFM:eventTime><NX2SFM:specificProblem>690</NX2SFM:specificProblem><NX2SFM:alarmText>WORKING STATE CHANGE</NX2SFM:alarmText><NX2SFM:additionalText1>BL-SY WO-BU 0000 0000 0000 00000000 00000000 00000000 \r\n</NX2SFM:additionalText1><NX2SFM:additionalText2></NX2SFM:additionalText2><NX2SFM:additionalText3></NX2SFM:additionalText3><NX2SFM:perceivedSeverity>warning</NX2SFM:perceivedSeverity><NX2SFM:eventType>equipment</NX2SFM:eventType><NX2SFM:alarmId>65535</NX2SFM:alarmId></NX2SFM:alarmNew></eventBody></notification></NWIXML:notificationSequence>", "<NWIXML:notificationSequence xmlns:NWIXML=\"http://www.nokia.com\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:NX2SFM=\"http://www.nokia.com/nx2sfm\" xsi:schemaLocation=\"http://www.nokia.com exhalagx.xsd\"><notification><fixedHeader><domainType>com.nokia.ntc::NWIXML</domainType><typeName>alarmEvent</typeName><eventName></eventName></fixedHeader><optionalHeader><sender>MSCi-945649</sender><eventTime>2009-11-24T02:00:14+00:00</eventTime></optionalHeader><eventBody><NX2SFM:alarmNew systemDN=\"MSCi-945649/FU-OMU-0\"><NX2SFM:eventTime>2009-11-24T02:00:14+00:00</NX2SFM:eventTime><NX2SFM:specificProblem>557</NX2SFM:specificProblem><NX2SFM:alarmText>STATISTICAL REPORTS LOST</NX2SFM:alarmText><NX2SFM:additionalText1>0060 000001AE 000001FA 84 % \r\n</NX2SFM:additionalText1><NX2SFM:additionalText2></NX2SFM:additionalText2><NX2SFM:additionalText3></NX2SFM:additionalText3><NX2SFM:perceivedSeverity>warning</NX2SFM:perceivedSeverity><NX2SFM:eventType>equipment</NX2SFM:eventType><NX2SFM:alarmId>65535</NX2SFM:alarmId></NX2SFM:alarmNew></eventBody></notification></NWIXML:notificationSequence>", "<NWIXML:notificationSequence xmlns:NWIXML=\"http://www.nokia.com\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:NX2SFM=\"http://www.nokia.com/nx2sfm\" xsi:schemaLocation=\"http://www.nokia.com exhalagx.xsd\"><notification><fixedHeader><domainType>com.nokia.ntc::NWIXML</domainType><typeName>alarmEvent</typeName><eventName></eventName></fixedHeader><optionalHeader><sender>MSCi-945649</sender><eventTime>2009-11-24T02:00:20+00:00</eventTime></optionalHeader><eventBody><NX2SFM:alarmNew systemDN=\"MSCi-945649/FU-VTP-24\"><NX2SFM:eventTime>2009-11-24T02:00:20+00:00</NX2SFM:eventTime><NX2SFM:specificProblem>690</NX2SFM:specificProblem><NX2SFM:alarmText>WORKING STATE CHANGE</NX2SFM:alarmText><NX2SFM:additionalText1>WO-BU BL-SY 0000 0000 0000 00000000 00000000 00000000 \r\n</NX2SFM:additionalText1><NX2SFM:additionalText2></NX2SFM:additionalText2><NX2SFM:additionalText3></NX2SFM:additionalText3><NX2SFM:perceivedSeverity>warning</NX2SFM:perceivedSeverity><NX2SFM:eventType>equipment</NX2SFM:eventType><NX2SFM:alarmId>65535</NX2SFM:alarmId></NX2SFM:alarmNew></eventBody></notification></NWIXML:notificationSequence>"], "requestHeader": ["accept-encoding", "cache-control", "content-length", "content-type", "date", "host", "user-agent"], "requestHeaderCnt": 7, "requestHeaderField": ["accept-encoding", "accept-encoding", "accept-encoding", "accept-encoding", "cache-control", "cache-control", "cache-control", "cache-control", "content-length", "content-length", "content-length", "content-length", "content-type", "content-type", "content-type", "content-type", "date", "date", "date", "date"], "requestHeaderValue": ["1090", "1098", "1121", "1121", "identity", "identity", "identity", "identity", "no-store", "no-store", "no-store", "no-store", "text/xml", "text/xml", "text/xml", "text/xml", "tue, 24 nov 2009 02:00:00 gmt", "tue, 24 nov 2009 02:00:07 gmt", "tue, 24 nov 2009 02:00:14 gmt", "tue, 24 nov 2009 02:00:20 gmt"], "requestHeaderValueCnt": 20, "responseHeader": ["content-type", "date", "server", "transfer-encoding"], "responseHeaderCnt": 4, "responseHeaderField": ["content-type", "content-type", "content-type", "content-type", "date", "date", "date", "date", "server", "server", "server", "server", "transfer-encoding", "transfer-encoding", "transfer-encoding", "transfer-encoding"], "responseHeaderValue": ["apache/1.3.31 (unix)", "apache/1.3.31 (unix)", "apache/1.3.31 (unix)", "apache/1.3.31 (unix)", "chunked", "chunked", "chunked", "chunked", "text/html", "text/html", "text/html", "text/html", "tue, 24 nov 2009 01:50:17 gmt", "tue, 24 nov 2009 01:50:25 gmt", "tue, 24 nov 2009 01:50:32 gmt", "tue, 24 nov 2009 01:50:38 gmt"], "responseHeaderValueCnt": 16, "serverVersion": ["1.1"], "serverVersionCnt": 1, "sha256": ["0ab4084e1efdc702cefa26b6e3951a35753346c29f7c1055c71853d1b53b4884", "1dcedca9ad8d641e6c03f2927f1d5783f4e635a89c8d1cdbeb89a3e378392f68", "aa41ae95899ebd5245d3927bd504401347afc224bb0fb09fbada808cf9517522", "f0b7d3846507e9f9ba9b40e58906de400ae92f04e59c8e3304be78dc102230bb"], "sha256Cnt": 4, "statuscode": [200], "statuscodeCnt": 1, "uri": ["/exhmedmx"], "uriCnt": 1, "useragent": ["Nokia-eheprb/1"], "useragentCnt": 1}, "initRTT": 1, "ipProtocol": 6, "lastPacket": 1259023939991, "length": 30872, "network": {"bytes": 30384, "community_id": "1:edLn61HrOoPJoKGLwmvOMdlNXWI=", "packets": 108}, "node": "test", "packetLen": [90, 90, 90, 90, 90, 82, 82, 280, 280, 90, 90, 90, 82, 82, 280, 280, 82, 82, 82, 82, 1172, 1172, 1172, 1172, 226, 226, 226, 82, 82, 226, 82, 82, 280, 280, 280, 280, 82, 82, 1203, 1203, 226, 226, 82, 82, 82, 82, 1203, 1203, 226, 226, 82, 82, 280, 280, 280, 280, 82, 82, 82, 82, 1180, 1180, 1180, 1180, 226, 226, 82, 82, 226, 226, 82, 82, 280, 280, 280, 280, 82, 82, 82, 1203, 1203, 82, 1203, 1203, 226, 226, 82, 82, 226, 226, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82, 82], "packetPos": [24, 114, 204, 294, 384, 474, 556, 638, 918, 1198, 1288, 1378, 1468, 1550, 1632, 1912, 2192, 2274, 2356, 2438, 2520, 3692, 4864, 6036, 7208, 7434, 7660, 7886, 7968, 8050, 8276, 8358, 8440, 8720, 9000, 9280, 9560, 9642, 9724, 10927, 12130, 12356, 12582, 12664, 12746, 12828, 12910, 14113, 15316, 15542, 15768, 15850, 15932, 16212, 16492, 16772, 17052, 17134, 17216, 17298, 17380, 18560, 19740, 20920, 22100, 22326, 22552, 22634, 22716, 22942, 23168, 23250, 23332, 23612, 23892, 24172, 24452, 24534, 24616, 24698, 25901, 27104, 27186, 28389, 29592, 29818, 30044, 30126, 30208, 30434, 30660, 30742, 30824, 30906, 30988, 31070, 31152, 31234, 31316, 31398, 31480, 31562, 31644, 31726, 31808, 31890, 31972, 32054], "protocol": ["http", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 576}, "source": {"bytes": 25144, "ip": "*************", "mac": ["00:0b:ab:0e:e8:ec"], "mac-cnt": 1, "packets": 64, "port": 59221}, "srcOui": ["Advantech Technology (CHINA) Co., Ltd."], "srcOuiCnt": 1, "srcPayload8": "504f5354202f6578", "srcRIR": "ARIN", "srcTTL": [64], "srcTTLCnt": 1, "tcpflags": {"ack": 44, "dstZero": 4, "fin": 8, "psh": 48, "rst": 0, "srcZero": 0, "syn": 4, "syn-ack": 4, "urg": 0}, "tcpseq": {"dst": 0, "src": 1810635452}, "totDataBytes": 5798}, "header": {"index": {"_index": "tests_sessions3-091124"}}}]}