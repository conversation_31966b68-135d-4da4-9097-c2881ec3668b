{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 1834}, "destination": {"bytes": 1163, "geo": {"country_iso_code": "US"}, "ip": "*************", "mac": ["00:25:90:ac:d0:6a"], "mac-cnt": 1, "packets": 10, "port": 25}, "dstOui": ["Super Micro Computer, Inc."], "dstOuiCnt": 1, "dstPayload8": "32323020616f6c6d", "dstRIR": "ARIN", "dstTTL": [64], "dstTTLCnt": 1, "email": {"ASN": ["---", "---"], "GEO": ["---", "---"], "RIR": ["", ""], "contentType": ["multipart/mixed; boundary=\"===============1250870193309395048==\""], "contentTypeCnt": 1, "dst": ["<EMAIL>"], "dstCnt": 1, "header": ["content-type", "from", "mime-version", "received", "subject", "to"], "headerCnt": 6, "host": ["smtp-01.xxxxxxxxx.com", "smtp-02.xxxxxxxxx.com", "xxxx.xxxx.xxxxxxxxx.com"], "hostCnt": 3, "ip": ["**********", "**********"], "ipCnt": 2, "md5": ["5b153a606bea42005e1eedb5ddeabcf0"], "md5Cnt": 1, "mimeVersion": ["1.0"], "mimeVersionCnt": 1, "sha256": ["8d4b2e39ccf34cff2147c7b6896f4bd5ce0a209d0cd87ca75035b4c8243bf865"], "sha256Cnt": 1, "smtpHello": ["smtp-01.xxxxxxxxx.com"], "smtpHelloCnt": 1, "src": ["<EMAIL>"], "srcCnt": 1, "subject": ["xxxxxx xxxxxx xx xxxxxxxxx xx-xxxxxxx（ANA）GGL_xxxxxxxxxxxxx_xxxxxx.zip"], "subjectCnt": 1}, "ethertype": 2048, "fileId": [], "firstPacket": 1384762012670, "initRTT": 39, "ipProtocol": 6, "lastPacket": 1384762016457, "length": 3788, "network": {"bytes": 3599, "community_id": "1:cD5m7ZYd5aNPATHx1SsYbijQBNo=", "packets": 19}, "node": "test", "packetLen": [90, 90, 82, 153, 110, 82, 251, 182, 123, 82, 184, 1530, 340, 82, 114, 162, 82, 82, 82], "packetPos": [24, 114, 204, 286, 439, 549, 631, 882, 1064, 1187, 1269, 1453, 2983, 3323, 3405, 3519, 3681, 3763, 3845], "protocol": ["smtp", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 495}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 2436, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:00:0c:07:ac:01", "00:0b:5f:6b:5d:40"], "mac-cnt": 2, "packets": 9, "port": 20720}, "srcOui": ["Cisco Systems, Inc"], "srcOuiCnt": 1, "srcPayload8": "45484c4f20736d74", "srcTTL": [51], "srcTTLCnt": 1, "tags": ["smtp:statuscode:250", "srcip"], "tagsCnt": 2, "tcpflags": {"ack": 7, "dstZero": 0, "fin": 2, "psh": 9, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 2426100333}, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [3795905600], "string.snow": ["16777226:20720,-499061696:25"]}, "totDataBytes": 2329}, "header": {"index": {"_index": "tests_sessions3-131118"}}}]}