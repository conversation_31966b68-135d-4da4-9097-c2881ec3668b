{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 30}, "destination": {"as": {"full": "AS1 Cool Beans!", "number": 1, "organization": {"name": "Cool Beans!"}}, "bytes": 147, "geo": {"country_iso_code": "CA"}, "ip": "********", "mac": ["00:00:0c:07:ac:01", "00:0e:d6:0b:98:80"], "mac-cnt": 2, "packets": 1, "port": 53}, "dns": [{"answers": [{"class": "IN", "name": "<root>", "ttl": 300, "type": "SOA"}], "answersCnt": 1, "headerFlags": ["AA", "RA", "RD"], "host": ["no.such.host"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "A", "queryHost": "no.such.host", "status": "NXDOMAIN"}], "dnsCnt": 1, "dstOui": ["Cisco Systems, Inc"], "dstOuiCnt": 1, "dstPayload8": "a9ba858300010000", "dstRIR": "TEST", "dstTTL": [60], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1394587409097, "ipProtocol": 17, "lastPacket": 1394587409103, "length": 5, "network": {"bytes": 219, "community_id": "1:czx3uclbVAqcKB38zV0MbAzF/kA=", "packets": 2}, "node": "test", "packetLen": [88, 163], "packetPos": [24, 112], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 105}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 72, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 1, "port": 56329}, "srcOui": ["Dell Inc."], "srcOuiCnt": 1, "srcPayload8": "a9ba010000010000", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["dstip", "error-dns", "srcip"], "tagsCnt": 3, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [33554442], "string.snow": ["16777226:56329,33554442:53"]}, "totDataBytes": 135}, "header": {"index": {"_index": "tests_sessions3-140312"}}}]}