{"sessions3": [{"body": {"@timestamp": "SET", "cert": [{"hash": "df:59:fa:ee:21:8a:ee:0b:99:b4:25:40:4a:78:e1:f9:16:20:9e:5c", "issuerON": ["Internet Widgits Pty Ltd"], "notAfter": 1720007767000, "notBefore": 1404647767000, "publicAlgorithm": "rsaEncryption", "remainingDays": 3649, "remainingSeconds": 315359665, "serial": "00f1965d11e3ff07ee", "subjectON": ["Internet Widgits Pty Ltd"], "validDays": 3650, "validSeconds": 315360000}], "certCnt": 1, "client": {"bytes": 487}, "destination": {"as": {"full": "AS24940 Hetzner Online GmbH", "number": 24940, "organization": {"name": "Hetzner Online GmbH"}}, "bytes": 1018, "geo": {"country_iso_code": "DE"}, "ip": "*************", "mac": ["c0:25:06:be:ec:c3"], "mac-cnt": 1, "packets": 2, "port": 7000}, "dstOui": ["AVM GmbH"], "dstOuiCnt": 1, "dstPayload8": "16feff0000000000", "dstRIR": "RIPE", "dstTTL": [52], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1404648102097, "ipProtocol": 17, "lastPacket": 1404648102237, "length": 139, "network": {"bytes": 1631, "community_id": "1:UPZ9f3UdNc+eoCm8yVYQOonoaBE=", "packets": 5}, "node": "test", "packetLen": [222, 710, 304, 340, 135], "packetPos": [24, 246, 956, 1260, 1600], "protocol": ["dtls", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 934}, "source": {"bytes": 613, "ip": "**************", "mac": ["50:e5:49:d8:73:12"], "mac-cnt": 1, "packets": 3, "port": 6000}, "srcOui": ["Giga-Byte Technology Co.,Ltd."], "srcOuiCnt": 1, "srcPayload8": "16feff0000000000", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "tls": {"ja4": ["dd1i440200_49bcda3e7ebf_18d1e47e0978"], "ja4Cnt": 1, "ja4_r": ["dd1i440200_0006,0008,0009,000a,0011,0012,0013,0014,0015,0016,002f,0032,0033,0035,0038,0039,0041,0044,0045,0084,0087,0088,0096,0099,009a,00ff,c003,c004,c005,c008,c009,c00a,c00d,c00e,c00f,c012,c013,c014,c01b,c01c,c01e,c01f,c021,c022_000f,0023"], "ja4_rCnt": 1}, "totDataBytes": 1421}, "header": {"index": {"_index": "tests_sessions3-140706"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 28}, "destination": {"as": {"full": "AS25152 Reseaux IP Europeens Network Coordination Centre (RIPE NCC)", "number": 25152, "organization": {"name": "Reseaux IP Europeens Network Coordination Centre (RIPE NCC)"}}, "bytes": 955, "geo": {"country_iso_code": "NL"}, "ip": "************", "mac": ["c0:25:06:be:ec:c3"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answers": [{"class": "IN", "name": "<root>", "nameserver": "a.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "b.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "c.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "d.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "e.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "f.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "g.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "h.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "i.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "j.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "k.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "l.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "nameserver": "m.root-servers.net", "ttl": 518400, "type": "NS"}, {"class": "IN", "name": "<root>", "ttl": 518400, "type": "RRSIG"}, {"class": "IN", "ip": "**********", "name": "a.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "**************", "name": "b.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "***********", "name": "c.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "***********", "name": "d.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "**************", "name": "e.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "***********", "name": "f.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "************", "name": "g.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "***********", "name": "h.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "*************", "name": "i.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "*************", "name": "j.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "************", "name": "k.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "***********", "name": "l.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "************", "name": "m.root-servers.net", "ttl": 518400, "type": "A"}, {"class": "IN", "ip": "2001:503:ba3e::2:30", "name": "a.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:500:84::b", "name": "b.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:500:2::c", "name": "c.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:500:2d::d", "name": "d.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:500:2f::f", "name": "f.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:500:1::803f:235", "name": "h.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:7fe::53", "name": "i.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:503:c27::2:30", "name": "j.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:7fd::1", "name": "k.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:500:3::42", "name": "l.root-servers.net", "ttl": 518400, "type": "AAAA"}, {"class": "IN", "ip": "2001:dc3::35", "name": "m.root-servers.net", "ttl": 518400, "type": "AAAA"}], "answersCnt": 38, "headerFlags": ["AA", "RD"], "nameserverASN": ["AS10515 VeriSign Infrastructure & Operations", "AS10515 VeriSign Infrastructure & Operations", "AS10886 University of Maryland", "AS10886 University of Maryland", "AS13 Headquarters, USAISC", "AS1508 Headquarters, USAISC", "AS20144 ICANN", "AS20144 ICANN", "AS20172 VeriSign Global Registry Services", "AS20431 VeriSign Global Registry Services", "AS2149 Cogent Communications", "AS2149 Cogent Communications", "AS21556 NASA Ames Research Center", "AS25152 Reseaux IP Europeens Network Coordination Centre (RIPE NCC)", "AS25152 Reseaux IP Europeens Network Coordination Centre (RIPE NCC)", "AS29216 NETNOD Internet Exchange i Sverige AB", "AS29216 NETNOD Internet Exchange i Sverige AB", "AS3557 Internet Systems Consortium, Inc.", "AS3557 Internet Systems Consortium, Inc.", "AS394353 <PERSON><PERSON>-Server-OPS", "AS394353 <PERSON><PERSON>-Server-OPS", "AS5927 DoD Network Information Center", "AS7500 WIDE Project", "AS7500 WIDE Project"], "nameserverGEO": ["---", "JP", "JP", "NL", "SE", "SE", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US", "US"], "nameserverHost": ["a.root-servers.net", "b.root-servers.net", "c.root-servers.net", "d.root-servers.net", "e.root-servers.net", "f.root-servers.net", "g.root-servers.net", "h.root-servers.net", "i.root-servers.net", "j.root-servers.net", "k.root-servers.net", "l.root-servers.net", "m.root-servers.net"], "nameserverHostCnt": 13, "nameserverIp": ["***********", "************", "**************", "**************", "***********", "*************", "***********", "*************", "************", "**********", "***********", "***********", "2001:0500:0001:0000:0000:0000:803f:0235", "2001:0500:0002:0000:0000:0000:0000:000c", "2001:0500:002d:0000:0000:0000:0000:000d", "2001:0500:002f:0000:0000:0000:0000:000f", "2001:0500:0003:0000:0000:0000:0000:0042", "2001:0500:0084:0000:0000:0000:0000:000b", "2001:0503:ba3e:0000:0000:0000:0002:0030", "2001:0503:0c27:0000:0000:0000:0002:0030", "2001:07fd:0000:0000:0000:0000:0000:0001", "2001:07fe:0000:0000:0000:0000:0000:0053", "2001:0dc3:0000:0000:0000:0000:0000:0035", "************"], "nameserverIpCnt": 24, "nameserverRIR": ["", "", "", "", "", "", "", "", "", "", "", "APNIC", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "RIPE"], "opcode": "QUERY", "qc": "IN", "qt": "NS", "queryHost": "<root>", "status": "NOERROR"}], "dnsCnt": 1, "dstOui": ["AVM GmbH"], "dstOuiCnt": 1, "dstPayload8": "273b85000001000e", "dstRIR": "RIPE", "dstTTL": [56], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1404648108283, "ipProtocol": 17, "lastPacket": 1404648108320, "length": 36, "network": {"bytes": 1025, "community_id": "1:DuL94POE3Q8ma0tVDRZS9/PU/5o=", "packets": 2}, "node": "test", "packetLen": [86, 971], "packetPos": [1735, 1821], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 913}, "source": {"bytes": 70, "ip": "**************", "mac": ["50:e5:49:d8:73:12"], "mac-cnt": 1, "packets": 1, "port": 60170}, "srcOui": ["Giga-Byte Technology Co.,Ltd."], "srcOuiCnt": 1, "srcPayload8": "273b012000010000", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "totDataBytes": 941}, "header": {"index": {"_index": "tests_sessions3-140706"}}}]}