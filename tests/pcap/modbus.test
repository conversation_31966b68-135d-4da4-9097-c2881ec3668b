{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 0}, "destination": {"as": {"full": "AS2 Hmm!@#$%^&*()", "number": 2, "organization": {"name": "Hmm!@#$%^&*()"}}, "bytes": 120, "ip": "********", "mac": ["00:02:b3:ce:70:51"], "mac-cnt": 1, "packets": 2, "port": 502}, "dstOui": ["Intel Corporation"], "dstOuiCnt": 1, "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1093521678945, "ipProtocol": 6, "lastPacket": 1093521678945, "length": 0, "network": {"bytes": 240, "community_id": "1:q6f4dI93sNUlL7tfl69o4hoyMvE=", "packets": 4}, "node": "test", "packetLen": [76, 76, 76, 76], "packetPos": [24, 100, 176, 252], "protocol": ["tcp"], "protocolCnt": 1, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"bytes": 120, "ip": "*********", "mac": ["00:20:78:00:62:0d"], "mac-cnt": 1, "packets": 2, "port": 2387}, "srcOui": ["Runtop Inc"], "srcOuiCnt": 1, "srcTTL": [128], "srcTTLCnt": 1, "tcpflags": {"ack": 2, "dstZero": 0, "fin": 2, "psh": 0, "rst": 0, "srcZero": 0, "syn": 0, "syn-ack": 0, "urg": 0}, "totDataBytes": 0}, "header": {"index": {"_index": "tests_sessions3-040826"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 112}, "destination": {"as": {"full": "AS2 Hmm!@#$%^&*()", "number": 2, "organization": {"name": "Hmm!@#$%^&*()"}}, "bytes": 800, "ip": "********", "mac": ["00:02:b3:ce:70:51"], "mac-cnt": 1, "packets": 12, "port": 502}, "dstOui": ["Intel Corporation"], "dstOuiCnt": 1, "dstPayload8": "0000000000030a88", "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1093521681696, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1093522067391, "length": 385695, "modbus": {"exccode": [11], "exccodeCnt": 1, "funccode": ["17", "8"], "funccodeCnt": 2, "protocolid": 0, "transactionid": [0], "transactionidCnt": 1, "unitid": 10}, "network": {"bytes": 2054, "community_id": "1:MaXLsh2l1JFKfqud0SOuIhVIh2Q=", "packets": 32}, "node": "test", "packetLen": [78, 78, 76, 82, 79, 82, 79, 82, 79, 76, 82, 79, 82, 82, 76, 82, 82, 76, 82, 82, 76, 82, 82, 76, 78, 97, 76, 78, 97, 76, 76, 76], "packetPos": [328, 406, 484, 560, 642, 721, 803, 882, 964, 1043, 1119, 1201, 1280, 1362, 1444, 1520, 1602, 1684, 1760, 1842, 1924, 2000, 2082, 2164, 3092, 3170, 3267, 3343, 3421, 3518, 3594, 3670], "protocol": ["modbus", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 138}, "source": {"bytes": 1254, "ip": "*********", "mac": ["00:20:78:00:62:0d"], "mac-cnt": 1, "packets": 20, "port": 2578}, "srcOui": ["Runtop Inc"], "srcOuiCnt": 1, "srcPayload8": "0000000000060a08", "srcTTL": [128], "srcTTLCnt": 1, "tcpflags": {"ack": 9, "dstZero": 0, "fin": 1, "psh": 20, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 1637347714}, "totDataBytes": 250}, "header": {"index": {"_index": "tests_sessions3-040826"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 24}, "destination": {"bytes": 302, "ip": "********", "mac": ["00:50:04:93:70:67"], "mac-cnt": 1, "packets": 5, "port": 502}, "dstOui": ["3com"], "dstOuiCnt": 1, "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1093521953490, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1093521976747, "length": 23256, "modbus": {"funccode": [43], "funccodeCnt": 1, "protocolid": 0, "transactionid": [1], "transactionidCnt": 1, "unitid": 10}, "network": {"bytes": 676, "community_id": "1:ux3yXqZ9Wq4bvdjhURA1fB9H7ok=", "packets": 11}, "node": "test", "packetLen": [78, 78, 76, 82, 76, 82, 76, 76, 76, 76, 76], "packetPos": [2240, 2318, 2396, 2472, 2554, 2630, 2712, 2788, 2864, 2940, 3016], "protocol": ["modbus", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"bytes": 374, "ip": "*********", "mac": ["00:20:78:00:62:0d"], "mac-cnt": 1, "packets": 6, "port": 2579}, "srcOui": ["Runtop Inc"], "srcOuiCnt": 1, "srcPayload8": "0001000000060a2b", "srcTTL": [128], "srcTTLCnt": 1, "tcpflags": {"ack": 5, "dstZero": 0, "fin": 2, "psh": 2, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 1699523493}, "totDataBytes": 24}, "header": {"index": {"_index": "tests_sessions3-040826"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 72}, "destination": {"as": {"full": "AS2 Hmm!@#$%^&*()", "number": 2, "organization": {"name": "Hmm!@#$%^&*()"}}, "bytes": 575, "ip": "********", "mac": ["00:02:b3:ce:70:51"], "mac-cnt": 1, "packets": 9, "port": 502}, "dstOui": ["Intel Corporation"], "dstOuiCnt": 1, "dstPayload8": "0001000000040a01", "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1093522326102, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1093522503198, "length": 177095, "modbus": {"funccode": ["1", "3", "5", "6"], "funccodeCnt": 4, "protocolid": 0, "transactionid": [1], "transactionidCnt": 1, "unitid": 10}, "network": {"bytes": 1573, "community_id": "1:E8oJvgGRT6SbgwRzDVhg61rb8go=", "packets": 25}, "node": "test", "packetLen": [78, 78, 76, 82, 80, 76, 82, 80, 76, 82, 83, 76, 82, 82, 76, 82, 82, 76, 82, 82, 76, 76, 76, 76, 76], "packetPos": [3746, 3824, 3902, 3978, 4060, 4140, 4216, 4298, 4378, 4454, 4536, 4619, 4695, 4777, 4859, 4935, 5017, 5099, 5175, 5257, 5339, 5415, 5491, 5567, 5643], "protocol": ["modbus", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 69}, "source": {"bytes": 998, "ip": "********", "mac": ["00:50:04:93:70:67"], "mac-cnt": 1, "packets": 16, "port": 3082}, "srcOui": ["3com"], "srcOuiCnt": 1, "srcPayload8": "0001000000060a01", "srcTTL": [128], "srcTTLCnt": 1, "tcpflags": {"ack": 9, "dstZero": 0, "fin": 2, "psh": 12, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 1211940485}, "totDataBytes": 141}, "header": {"index": {"_index": "tests_sessions3-040826"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 926}, "destination": {"bytes": 422, "ip": "********", "mac": ["00:50:04:93:70:67"], "mac-cnt": 1, "packets": 7, "port": 502}, "dstOui": ["3com"], "dstOuiCnt": 1, "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1093522946554, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1093523023116, "length": 76563, "network": {"bytes": 1806, "community_id": "1:wIutTfE+u89XTDjHPTfWqx+x6Ss=", "packets": 15}, "node": "test", "packetLen": [78, 78, 76, 521, 76, 521, 76, 82, 76, 82, 76, 76, 76, 76, 76], "packetPos": [5719, 5797, 5875, 5951, 6472, 6548, 7069, 7145, 7227, 7303, 7385, 7461, 7537, 7613, 7689], "protocol": ["tcp"], "protocolCnt": 1, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"bytes": 1384, "ip": "*********", "mac": ["00:20:78:00:62:0d"], "mac-cnt": 1, "packets": 8, "port": 2585}, "srcOui": ["Runtop Inc"], "srcOuiCnt": 1, "srcPayload8": "0001000088040507", "srcTTL": [128], "srcTTLCnt": 1, "tcpflags": {"ack": 7, "dstZero": 0, "fin": 2, "psh": 4, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 1926682180}, "totDataBytes": 926}, "header": {"index": {"_index": "tests_sessions3-040826"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 128}, "destination": {"bytes": 902, "ip": "*********", "mac": ["00:20:78:00:62:0d"], "mac-cnt": 1, "packets": 15, "port": 4446}, "dstOui": ["Runtop Inc"], "dstOuiCnt": 1, "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1093523065562, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1093523220676, "length": 155114, "modbus": {"funccode": ["129", "42"], "funccodeCnt": 2, "protocolid": 0, "transactionid": [1], "transactionidCnt": 1, "unitid": 10}, "network": {"bytes": 1920, "community_id": "1:kv4NQN3bta8Ork33ERod5i7nT3Q=", "packets": 31}, "node": "test", "packetLen": [78, 78, 76, 80, 76, 80, 76, 80, 76, 80, 76, 80, 76, 80, 76, 80, 76, 80, 76, 80, 76, 80, 76, 84, 76, 84, 76, 76, 76, 76, 76], "packetPos": [7765, 7843, 7921, 7997, 8077, 8153, 8233, 8309, 8389, 8465, 8545, 8621, 8701, 8777, 8857, 8933, 9013, 9089, 9169, 9245, 9325, 9401, 9481, 9557, 9641, 9717, 9801, 9877, 9953, 10029, 10105], "protocol": ["modbus", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"bytes": 1018, "ip": "********", "mac": ["00:50:04:93:70:67"], "mac-cnt": 1, "packets": 16, "port": 502}, "srcOui": ["3com"], "srcOuiCnt": 1, "srcPayload8": "0001000000040a81", "srcTTL": [128], "srcTTLCnt": 1, "tcpflags": {"ack": 15, "dstZero": 0, "fin": 2, "psh": 12, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 1397535383}, "totDataBytes": 128}, "header": {"index": {"_index": "tests_sessions3-040826"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 1692}, "destination": {"as": {"full": "AS22394 Cellco Partnership DBA Verizon Wireless", "number": 22394, "organization": {"name": "Cellco Partnership DBA Verizon Wireless"}}, "bytes": 11298, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:90:4c:91:00:01", "00:c0:a8:f2:bf:fb"], "mac-cnt": 2, "packets": 181, "port": 502}, "dstOui": ["Epigram, Inc.", "GVC Corporation"], "dstOuiCnt": 2, "dstPayload8": "0000000000030180", "dstRIR": "ARIN", "dstTTL": [43], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1153491879610, "initRTT": 6288, "ipProtocol": 6, "lastPacket": 1153491974091, "length": 94481, "modbus": {"exccode": ["1", "2", "3"], "exccodeCnt": 3, "funccode": ["0", "1", "10", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "11", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "12", "120", "121", "122", "123", "124", "125", "126", "127", "13", "14", "15", "16", "17", "18", "19", "2", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "6", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "7", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "9", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99"], "funccodeCnt": 128, "protocolid": 0, "transactionid": [0], "transactionidCnt": 1, "unitid": 1}, "network": {"bytes": 22140, "community_id": "1:b8B7eIZduzffbsODXRciLN/VRAQ=", "packets": 350}, "node": "test", "packetLen": [78, 78, 78, 76, 70, 82, 76, 76, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 82, 84, 79, 70, 78, 76, 79, 82, 82, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 70, 82, 76, 79, 83, 79, 83, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 76, 70, 82, 76, 79, 82, 79, 82, 76, 79, 82, 76, 79, 70, 82, 76, 79, 82, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 76, 70, 82, 76, 79, 82, 79, 82, 76, 82, 82, 79, 70, 82, 76, 82, 82, 79, 70, 82, 76, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 70, 82, 76, 79, 82, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 76, 79, 82, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 70, 82, 76, 79, 82, 79, 82, 79, 82, 76, 79, 82, 79, 82, 79, 82, 76, 79, 82, 79, 82, 79, 82, 76, 79, 82, 79, 82, 79, 82, 79, 70, 82, 76, 79, 82, 79, 70, 82, 76, 79, 70, 82, 76, 79, 82, 79, 82, 76, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 79, 82, 81, 82, 79, 82, 79, 70, 82, 76, 80, 82, 80, 82, 79, 82, 79, 70, 82, 76, 79, 82, 79, 82, 79, 70, 82, 79, 76, 70, 82, 76, 80, 82, 80, 82, 79, 82, 76, 79, 82], "packetPos": [10181, 10259, 10337, 10415, 10491, 10561, 10643, 10719, 10795, 10874, 10944, 11026, 11102, 11181, 11263, 11342, 11424, 11503, 11585, 11664, 11746, 11828, 11912, 11991, 12061, 12139, 12215, 12294, 12376, 12458, 12540, 12616, 12695, 12777, 12856, 12938, 13017, 13099, 13178, 13260, 13339, 13409, 13491, 13567, 13646, 13729, 13808, 13891, 13970, 14040, 14122, 14198, 14277, 14359, 14438, 14520, 14599, 14681, 14757, 14836, 14918, 14997, 15079, 15158, 15240, 15319, 15401, 15480, 15556, 15626, 15708, 15784, 15863, 15945, 16024, 16106, 16182, 16261, 16343, 16419, 16498, 16568, 16650, 16726, 16805, 16887, 16966, 17036, 17118, 17194, 17273, 17355, 17434, 17516, 17595, 17677, 17756, 17838, 17917, 17999, 18078, 18160, 18239, 18315, 18385, 18467, 18543, 18622, 18704, 18783, 18865, 18941, 19023, 19105, 19184, 19254, 19336, 19412, 19494, 19576, 19655, 19725, 19807, 19883, 19962, 20032, 20114, 20190, 20269, 20351, 20430, 20512, 20591, 20673, 20752, 20834, 20913, 20983, 21065, 21141, 21220, 21302, 21381, 21463, 21542, 21624, 21703, 21785, 21864, 21946, 22025, 22107, 22186, 22268, 22347, 22429, 22508, 22590, 22669, 22751, 22830, 22912, 22988, 23067, 23149, 23228, 23310, 23389, 23471, 23550, 23632, 23711, 23793, 23872, 23954, 24033, 24115, 24194, 24276, 24355, 24425, 24507, 24583, 24662, 24744, 24823, 24893, 24975, 25051, 25130, 25212, 25291, 25373, 25452, 25534, 25613, 25695, 25774, 25856, 25935, 26017, 26096, 26178, 26257, 26327, 26409, 26485, 26564, 26646, 26725, 26807, 26886, 26968, 27047, 27129, 27208, 27290, 27366, 27445, 27527, 27606, 27676, 27758, 27834, 27913, 27995, 28074, 28156, 28235, 28317, 28396, 28478, 28557, 28639, 28718, 28800, 28879, 28961, 29040, 29122, 29201, 29283, 29362, 29432, 29514, 29590, 29669, 29751, 29830, 29912, 29991, 30073, 30149, 30228, 30310, 30389, 30471, 30550, 30632, 30708, 30787, 30869, 30948, 31030, 31109, 31191, 31267, 31346, 31428, 31507, 31589, 31668, 31750, 31829, 31899, 31981, 32057, 32136, 32218, 32297, 32367, 32449, 32525, 32604, 32674, 32756, 32832, 32911, 32993, 33072, 33154, 33230, 33309, 33391, 33470, 33552, 33631, 33713, 33792, 33874, 33953, 34035, 34114, 34196, 34275, 34357, 34436, 34518, 34597, 34679, 34758, 34840, 34921, 35003, 35082, 35164, 35243, 35313, 35395, 35471, 35551, 35633, 35713, 35795, 35874, 35956, 36035, 36105, 36187, 36263, 36342, 36424, 36503, 36585, 36664, 36734, 36816, 36895, 36971, 37041, 37123, 37199, 37279, 37361, 37441, 37523, 37602, 37684, 37760, 37839], "protocol": ["modbus", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 1278}, "source": {"bytes": 10842, "ip": "**************", "mac": ["00:0c:29:6b:2d:28"], "mac-cnt": 1, "packets": 169, "port": 2582}, "srcOui": ["VMware, Inc."], "srcOuiCnt": 1, "srcPayload8": "0000000000060100", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "tcpflags": {"ack": 65, "dstZero": 0, "fin": 0, "psh": 281, "rst": 0, "srcZero": 0, "syn": 3, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 4058832205}, "totDataBytes": 2970}, "header": {"index": {"_index": "tests_sessions3-060721"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 3072}, "destination": {"bytes": 67248, "ip": "**********", "mac": ["00:0c:29:af:7f:fe"], "mac-cnt": 1, "packets": 256, "port": 502}, "dstOui": ["VMware, Inc."], "dstOuiCnt": 1, "dstPayload8": "000100000006ff06", "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1342774499588, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1342774524572, "length": 24984, "modbus": {"funccode": ["3", "4", "6"], "funccodeCnt": 3, "protocolid": 0, "transactionid": ["1", "10", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "11", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "12", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "13", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "14", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "15", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "16", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "17", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "18", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "19", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "2", "20", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "21", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "22", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "23", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239", "24", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "25", "250", "251", "252", "253", "254", "255", "256", "26", "27", "28", "29", "3", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "4", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "5", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "6", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "7", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "8", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "9", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99"], "transactionidCnt": 256, "unitid": 255}, "network": {"bytes": 104190, "community_id": "1:fVI8I1oSOscddKgy8h687WkHVsI=", "packets": 769}, "node": "test", "packetLen": [94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 94, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94, 291, 82, 94], "packetPos": [37921, 38015, 38109, 38191, 38285, 38379, 38461, 38555, 38649, 38731, 38825, 38919, 39001, 39095, 39189, 39271, 39365, 39459, 39541, 39635, 39729, 39811, 39905, 39999, 40081, 40175, 40269, 40351, 40445, 40539, 40621, 40715, 40809, 40891, 40985, 41276, 41358, 41452, 41743, 41825, 41919, 42210, 42292, 42386, 42677, 42759, 42853, 43144, 43226, 43320, 43611, 43693, 43787, 44078, 44160, 44254, 44545, 44627, 44721, 45012, 45094, 45188, 45479, 45561, 45655, 45946, 46028, 46122, 46216, 46298, 46392, 46486, 46568, 46662, 46756, 46838, 46932, 47026, 47108, 47202, 47296, 47378, 47472, 47763, 47845, 47939, 48230, 48312, 48406, 48697, 48779, 48873, 49164, 49246, 49340, 49631, 49713, 49807, 50098, 50180, 50274, 50565, 50647, 50741, 51032, 51114, 51208, 51499, 51581, 51675, 51966, 52048, 52142, 52433, 52515, 52609, 52900, 52982, 53076, 53367, 53449, 53543, 53834, 53916, 54010, 54301, 54383, 54477, 54768, 54850, 54944, 55235, 55317, 55411, 55702, 55784, 55878, 56169, 56251, 56345, 56636, 56718, 56812, 57103, 57185, 57279, 57570, 57652, 57746, 58037, 58119, 58213, 58504, 58586, 58680, 58971, 59053, 59147, 59438, 59520, 59614, 59905, 59987, 60081, 60372, 60454, 60548, 60839, 60921, 61015, 61306, 61388, 61482, 61773, 61855, 61949, 62240, 62322, 62416, 62707, 62789, 62883, 63174, 63256, 63350, 63641, 63723, 63817, 64108, 64190, 64284, 64575, 64657, 64751, 65042, 65124, 65218, 65509, 65591, 65685, 65976, 66058, 66152, 66443, 66525, 66619, 66910, 66992, 67086, 67377, 67459, 67553, 67844, 67926, 68020, 68311, 68393, 68487, 68778, 68860, 68954, 69245, 69327, 69421, 69712, 69794, 69888, 70179, 70261, 70355, 70646, 70728, 70822, 71113, 71195, 71289, 71580, 71662, 71756, 72047, 72129, 72223, 72514, 72596, 72690, 72981, 73063, 73157, 73448, 73530, 73624, 73915, 73997, 74091, 74382, 74464, 74558, 74849, 74931, 75025, 75316, 75398, 75492, 75783, 75865, 75959, 76250, 76332, 76426, 76717, 76799, 76893, 77184, 77266, 77360, 77651, 77733, 77827, 78118, 78200, 78294, 78585, 78667, 78761, 79052, 79134, 79228, 79519, 79601, 79695, 79986, 80068, 80162, 80453, 80535, 80629, 80920, 81002, 81096, 81387, 81469, 81563, 81854, 81936, 82030, 82321, 82403, 82497, 82788, 82870, 82964, 83255, 83337, 83431, 83722, 83804, 83898, 84189, 84271, 84365, 84656, 84738, 84832, 85123, 85205, 85299, 85590, 85672, 85766, 86057, 86139, 86233, 86524, 86606, 86700, 86991, 87073, 87167, 87458, 87540, 87634, 87925, 88007, 88101, 88392, 88474, 88568, 88859, 88941, 89035, 89326, 89408, 89502, 89793, 89875, 89969, 90260, 90342, 90436, 90727, 90809, 90903, 91194, 91276, 91370, 91661, 91743, 91837, 92128, 92210, 92304, 92595, 92677, 92771, 93062, 93144, 93238, 93529, 93611, 93705, 93996, 94078, 94172, 94463, 94545, 94639, 94930, 95012, 95106, 95397, 95479, 95573, 95864, 95946, 96040, 96331, 96413, 96507, 96798, 96880, 96974, 97265, 97347, 97441, 97732, 97814, 97908, 98199, 98281, 98375, 98666, 98748, 98842, 99133, 99215, 99309, 99600, 99682, 99776, 100067, 100149, 100243, 100534, 100616, 100710, 101001, 101083, 101177, 101468, 101550, 101644, 101935, 102017, 102111, 102402, 102484, 102578, 102869, 102951, 103045, 103336, 103418, 103512, 103803, 103885, 103979, 104270, 104352, 104446, 104737, 104819, 104913, 105204, 105286, 105380, 105671, 105753, 105847, 106138, 106220, 106314, 106605, 106687, 106781, 107072, 107154, 107248, 107539, 107621, 107715, 108006, 108088, 108182, 108473, 108555, 108649, 108940, 109022, 109116, 109407, 109489, 109583, 109874, 109956, 110050, 110341, 110423, 110517, 110808, 110890, 110984, 111275, 111357, 111451, 111742, 111824, 111918, 112209, 112291, 112385, 112676, 112758, 112852, 113143, 113225, 113319, 113610, 113692, 113786, 114077, 114159, 114253, 114544, 114626, 114720, 115011, 115093, 115187, 115478, 115560, 115654, 115945, 116027, 116121, 116412, 116494, 116588, 116879, 116961, 117055, 117346, 117428, 117522, 117813, 117895, 117989, 118280, 118362, 118456, 118747, 118829, 118923, 119214, 119296, 119390, 119681, 119763, 119857, 120148, 120230, 120324, 120615, 120697, 120791, 121082, 121164, 121258, 121549, 121631, 121725, 122016, 122098, 122192, 122483, 122565, 122659, 122950, 123032, 123126, 123417, 123499, 123593, 123884, 123966, 124060, 124351, 124433, 124527, 124818, 124900, 124994, 125285, 125367, 125461, 125752, 125834, 125928, 126219, 126301, 126395, 126686, 126768, 126862, 127153, 127235, 127329, 127620, 127702, 127796, 128087, 128169, 128263, 128554, 128636, 128730, 129021, 129103, 129197, 129488, 129570, 129664, 129955, 130037, 130131, 130422, 130504, 130598, 130889, 130971, 131065, 131356, 131438, 131532, 131823, 131905, 131999, 132290, 132372, 132466, 132757, 132839, 132933, 133224, 133306, 133400, 133691, 133773, 133867, 134158, 134240, 134334, 134625, 134707, 134801, 135092, 135174, 135268, 135559, 135641, 135735, 136026, 136108, 136202, 136493, 136575, 136669, 136960, 137042, 137136, 137427, 137509, 137603, 137894, 137976, 138070, 138361, 138443, 138537, 138828, 138910, 139004, 139295, 139377, 139471, 139762, 139844, 139938, 140229, 140311, 140405, 140696, 140778, 140872, 141163, 141245, 141339, 141630, 141712, 141806, 142097, 142179, 142273, 142564, 142646, 142740, 143031, 143113, 143207, 143498, 143580, 143674, 143965, 144047, 144141, 144432, 144514, 144608, 144899, 144981, 145075, 145366, 145448, 145542, 145833, 145915, 146009, 146300, 146382, 146476, 146767, 146849, 146943, 147234, 147316, 147410, 147701, 147783, 147877, 148168, 148250, 148344, 148635, 148717, 148811, 149102, 149184, 149278, 149569, 149651, 149745, 150036, 150118, 150212, 150503, 150585, 150679, 150970, 151052, 151146, 151437, 151519, 151613, 151904, 151986, 152080, 152371, 152453, 152547, 152838, 152920, 153014, 153305, 153387, 153481, 153772, 153854, 153948, 154239, 154321], "protocol": ["modbus", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 50340}, "source": {"bytes": 36942, "ip": "**********", "mac": ["10:9a:dd:4e:06:0d"], "mac-cnt": 1, "packets": 513, "port": 51411}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "000100000006ff06", "srcTTL": [64], "srcTTLCnt": 1, "tcpflags": {"ack": 256, "dstZero": 0, "fin": 0, "psh": 511, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 3765628665}, "totDataBytes": 53412}, "header": {"index": {"_index": "tests_sessions3-120720"}}}]}