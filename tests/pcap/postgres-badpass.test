{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 82}, "destination": {"as": {"full": "AS1 Cool Beans!", "number": 1, "organization": {"name": "Cool Beans!"}}, "bytes": 352, "geo": {"country_iso_code": "CA"}, "ip": "********", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 5, "port": 5432}, "dstOui": ["Dell Inc."], "dstOuiCnt": 1, "dstPayload8": "4e520000000c0000", "dstRIR": "TEST", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1399312748531, "initRTT": 2, "ipProtocol": 6, "lastPacket": 1399312748549, "length": 18, "network": {"bytes": 974, "community_id": "1:00y2N2+TsH6kM7On9rU3DFGgzZ8=", "packets": 13}, "node": "test", "packetLen": [94, 90, 82, 90, 82, 83, 82, 156, 95, 82, 82, 82, 82], "packetPos": [24, 118, 208, 290, 380, 462, 545, 627, 783, 878, 960, 1042, 1124], "postgresql": {"app": "psql", "db": "bar", "user": "foo"}, "protocol": ["postgresql", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 14}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 622, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:00:0c:07:ac:01", "00:d0:2b:d1:76:00"], "mac-cnt": 2, "packets": 8, "port": 53499}, "srcOui": ["Cisco Systems, Inc", "Jetcell, Inc."], "srcOuiCnt": 2, "srcPayload8": "0000000804d2162f", "srcTTL": [60], "srcTTLCnt": 1, "tags": ["dstip", "srcip"], "tagsCnt": 2, "tcpflags": {"ack": 5, "dstZero": 0, "fin": 2, "psh": 4, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 3954908796}, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [33554442], "string.snow": ["16777226:53499,33554442:5432"]}, "totDataBytes": 96}, "header": {"index": {"_index": "tests_sessions3-140505"}}}]}