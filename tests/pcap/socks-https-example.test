{"sessions3": [{"body": {"@timestamp": "SET", "cert": [{"alt": ["gp1.wac.edgecastcdn.net", "www.edgecast.com", "wac.edgecastcdn.net", "ne.wac.edgecastcdn.net", "swf.mixpo.com", "cdn.traceregister.com", "s.tmocache.com", "s.my.tmocache.com", "e1.boxcdn.net", "e2.boxcdn.net", "e3.boxcdn.net", "www.sonos.com", "static-cache.tp-global.net", "ssl-cdn.sometrics.com", "cache.vehicleassets.captivelead.com", "static.woopra.com", "images.ink2.com", "assets-secure.razoo.com", "ec.pond5.com", "images.esellerpro.com", "use.typekit.com", "static.iseatz.com", "static.www.turnto.com", "inpath-static.iseatz.com", "secure.avelleassets.com", "static.dubli.com", "www-cdn.cinamuse.com", "www-cdn.cineble.com", "www-cdn.cinemaden.com", "www-cdn.filmlush.com", "www-cdn.flixaddict.com", "www-cdn.itshd.com", "www-cdn.moviease.com", "www-cdn.movielush.com", "www-cdn.reelhd.com", "www-cdn.pushplay.com", "cdn1.fishpond.co.nz", "cdn1.fishpond.com.au", "www.isaca.org", "cdn.optimizely.com", "static.shoedazzle.com", "www.travelrepublic.co.uk", "cdn.nprove.com", "sslbest.booztx.com", "www.travelrepublic.com", "www.blacklabelads.com", "cdn.whois.com.au", "ne1.wac.edgecastcdn.net", "gs1.wac.edgecastcdn.net", "c1.socialcastcontent.com", "www.steepandcheap.com", "www.whiskeymilitia.com", "www.chainlove.com", "www.tramdock.com", "www.bonktown.com", "www.brociety.com", "edgecast.onegrp.com", "cdn.psw.net", "cdn.gaggle.net", "www-cdn.reelvidz.com", "fast.fonts.com", "ec.xnglobalres.com", "images.vrbo.com", "beta.fileblaze.net", "cdn.brandsexclusive.com.au", "www-cdn.ireel.com", "cdcssl.ibsrv.net", "cdn.betchoice.com", "player.vzaar.com", "framegrabs.vzaar.com", "thumbs.vzaar.com", "stylistlounge.stelladot.com", "www.stelladot.com", "content.aqcdn.com", "content.ebgames.com.au", "content.ebgames.co.nz", "images.pagerage.com", "images.allsaints.com", "cdnb1.kodakgallery.com", "cdn.orbengine.com", "cdn.quickoffice.com", "content.glscrip.com", "cdn.bidfan.com", "media.quantumads.com", "cdn.allenbrothers.com", "pics.intelius.com", "pics.peoplelookup.com", "pics.lookupanyone.com", "cdn1-ssl.iha.com", "s.cdn-care.com", "cdn2-b.examiner.com", "cdn.trtk.net", "edgecdn.ink2.com", "ec.dstimage.disposolutions.com", "cdn.clytel.com", "welcome2.carsdirect.com", "s1.card-images.com", "update.alot.com", "www.outsystems.com", "www.drwmedia.com", "lookup.bluecava.com", "cdn.taxact.com", "cdn.taxactonline.com", "cdn.200581.com", "img.vxcdn.com", "js.vxcdn.com", "www.goal.com", "cdns1.kodakgallery.com", "edge.dropdowndeals.com", "edge.pagerage.com", "edge.sanityswitch.com", "edge.yontoo.com", "layers.yontoo.com", "cdn.widgetserver.com", "www.cloudwords.com", "edge.actaads.com", "images.skincarerx.com", "ssl.cdn-redfin.com", "small.outso-media.com", "cdn.foxycart.com", "edge.jeetyetmedia.com", "cdn.ticketfly.com", "images.cosmeticmall.com", "www.backcountry.com", "ssl.booztx.com", "p.typekit.net", "use.typekit.net", "cdn.thewatershed.com", "www.sf-cdn.net", "static.cdn.dollarsdirect.com.au", "edge.redfordmediallc.com", "edge.pluralmediallc.com", "www.gourmetgiftbaskets.com", "www.numberinvestigator.com", "b2bportal.disneylandparis.com", "b2bportal.disneytravelagents.co.uk", "www.nwf.org", "assets.zendesk.com", "a.cdnkic.com", "s.cdnkic.com", "www.superbiketoystore.com", "cdn.stylethread.com.au", "cdn.cartrawler.com", "publicstaticcdn.tableausoftware.com", "secure.33across.com", "c.ztstatic.com", "c.mscimg.com", "static.teamtreehouse.com", "wac.a8b5.edgecastcdn.net"], "altCnt": 149, "hash": "d8:af:99:8d:b5:e0:42:a7:b4:7b:6d:41:62:75:00:a7:f7:ed:96:5c", "issuerCN": ["digicert high assurance ca-3"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1418212800000, "notBefore": 1317600000000, "publicAlgorithm": "rsaEncryption", "remainingDays": 372, "remainingSeconds": 32208328, "serial": "062d488986c9a6d7f94901c2b5906882", "subjectCN": ["gp1.wac.edgecastcdn.net"], "subjectON": ["EdgeCast Networks, Inc."], "validDays": 1164, "validSeconds": *********}, {"hash": "42:85:78:55:fb:0e:a4:3f:54:c9:91:1e:30:e7:79:1d:8c:e8:27:05", "issuerCN": ["digicert high assurance ev root ca"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1648944000000, "notBefore": 1207137600000, "publicAlgorithm": "rsaEncryption", "remainingDays": 3043, "remainingSeconds": 262939528, "serial": "0a5f114d035b179117d2efd4038c3f3b", "subjectCN": ["digicert high assurance ca-3"], "subjectON": ["DigiCert Inc"], "subjectOU": ["www.digicert.com"], "validDays": 5113, "validSeconds": 441806400}], "certCnt": 2, "client": {"bytes": 641}, "destination": {"bytes": 9145, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 13, "port": 1080}, "dstOui": ["Dell Inc."], "dstOuiCnt": 1, "dstPayload8": "005a99b40ab49cf9", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386004472572, "http": {"host": ["www.example.com"], "hostCnt": 1}, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1386004472629, "length": 57, "network": {"bytes": 10920, "community_id": "1:mKawWPl/vbyo+D8YOO/ybLJJIhQ=", "packets": 30}, "node": "test", "packetLen": [94, 90, 82, 91, 82, 90, 82, 202, 1530, 94, 82, 1530, 1530, 82, 1530, 82, 836, 82, 396, 129, 82, 253, 428, 82, 1402, 82, 109, 82, 82, 82], "packetPos": [24, 118, 208, 290, 381, 463, 553, 635, 837, 2367, 2461, 2543, 4073, 5603, 5685, 7215, 7297, 8133, 8215, 8611, 8740, 8822, 9075, 9503, 9585, 10987, 11069, 11178, 11260, 11342], "protocol": ["socks", "tcp", "tls", "tlsrulestest"], "protocolCnt": 4, "segmentCnt": 1, "server": {"bytes": 8279}, "socks": {"ASN": "AS15133 MCI Communications Services, Inc. d/b/a Verizon Business", "GEO": "US", "RIR": "RIPE", "ip": "**************", "port": 443}, "source": {"bytes": 1775, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 17, "port": 53554}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "040101bb5db8d877", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["cert:certificate-authority"], "tagsCnt": 1, "tcpflags": {"ack": 15, "dstZero": 0, "fin": 2, "psh": 11, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 2129669914}, "tls": {"cipher": ["TLS_RSA_WITH_RC4_128_SHA"], "cipherCnt": 1, "dstSessionId": ["85bbc584132410aa03c3d6aac195e2d81e7b8d24e63b314a4a5d214cbcf0080a"], "ja3": ["06a92bf69b367389d2feb0d70501ddfe"], "ja3Cnt": 1, "ja3s": ["280ca4511bfaa384b2e931c058e8816e"], "ja3sCnt": 1, "ja3sstring": ["769,5,65281"], "ja3sstringCnt": 1, "ja3string": ["769,57-56-53-22-19-10-51-50-**************-5-4-21-18-9-20-17-8-6-3-255,0,,"], "ja3stringCnt": 1, "ja4": ["t10d230100_6a57a6f57151_000000000000"], "ja4Cnt": 1, "ja4_r": ["t10d230100_0003,0004,0005,0006,0008,0009,000a,0011,0012,0013,0014,0015,0016,002f,0032,0033,0035,0038,0039,0096,0099,009a,00ff_"], "ja4_rCnt": 1, "version": ["TLSv1"], "versionCnt": 1}, "totDataBytes": 8920}, "header": {"index": {"_index": "tests_sessions3-131202"}}}, {"body": {"@timestamp": "SET", "cert": [{"alt": ["gp1.wac.edgecastcdn.net", "www.edgecast.com", "wac.edgecastcdn.net", "ne.wac.edgecastcdn.net", "swf.mixpo.com", "cdn.traceregister.com", "s.tmocache.com", "s.my.tmocache.com", "e1.boxcdn.net", "e2.boxcdn.net", "e3.boxcdn.net", "www.sonos.com", "static-cache.tp-global.net", "ssl-cdn.sometrics.com", "cache.vehicleassets.captivelead.com", "static.woopra.com", "images.ink2.com", "assets-secure.razoo.com", "ec.pond5.com", "images.esellerpro.com", "use.typekit.com", "static.iseatz.com", "static.www.turnto.com", "inpath-static.iseatz.com", "secure.avelleassets.com", "static.dubli.com", "www-cdn.cinamuse.com", "www-cdn.cineble.com", "www-cdn.cinemaden.com", "www-cdn.filmlush.com", "www-cdn.flixaddict.com", "www-cdn.itshd.com", "www-cdn.moviease.com", "www-cdn.movielush.com", "www-cdn.reelhd.com", "www-cdn.pushplay.com", "cdn1.fishpond.co.nz", "cdn1.fishpond.com.au", "www.isaca.org", "cdn.optimizely.com", "static.shoedazzle.com", "www.travelrepublic.co.uk", "cdn.nprove.com", "sslbest.booztx.com", "www.travelrepublic.com", "www.blacklabelads.com", "cdn.whois.com.au", "ne1.wac.edgecastcdn.net", "gs1.wac.edgecastcdn.net", "c1.socialcastcontent.com", "www.steepandcheap.com", "www.whiskeymilitia.com", "www.chainlove.com", "www.tramdock.com", "www.bonktown.com", "www.brociety.com", "edgecast.onegrp.com", "cdn.psw.net", "cdn.gaggle.net", "www-cdn.reelvidz.com", "fast.fonts.com", "ec.xnglobalres.com", "images.vrbo.com", "beta.fileblaze.net", "cdn.brandsexclusive.com.au", "www-cdn.ireel.com", "cdcssl.ibsrv.net", "cdn.betchoice.com", "player.vzaar.com", "framegrabs.vzaar.com", "thumbs.vzaar.com", "stylistlounge.stelladot.com", "www.stelladot.com", "content.aqcdn.com", "content.ebgames.com.au", "content.ebgames.co.nz", "images.pagerage.com", "images.allsaints.com", "cdnb1.kodakgallery.com", "cdn.orbengine.com", "cdn.quickoffice.com", "content.glscrip.com", "cdn.bidfan.com", "media.quantumads.com", "cdn.allenbrothers.com", "pics.intelius.com", "pics.peoplelookup.com", "pics.lookupanyone.com", "cdn1-ssl.iha.com", "s.cdn-care.com", "cdn2-b.examiner.com", "cdn.trtk.net", "edgecdn.ink2.com", "ec.dstimage.disposolutions.com", "cdn.clytel.com", "welcome2.carsdirect.com", "s1.card-images.com", "update.alot.com", "www.outsystems.com", "www.drwmedia.com", "lookup.bluecava.com", "cdn.taxact.com", "cdn.taxactonline.com", "cdn.200581.com", "img.vxcdn.com", "js.vxcdn.com", "www.goal.com", "cdns1.kodakgallery.com", "edge.dropdowndeals.com", "edge.pagerage.com", "edge.sanityswitch.com", "edge.yontoo.com", "layers.yontoo.com", "cdn.widgetserver.com", "www.cloudwords.com", "edge.actaads.com", "images.skincarerx.com", "ssl.cdn-redfin.com", "small.outso-media.com", "cdn.foxycart.com", "edge.jeetyetmedia.com", "cdn.ticketfly.com", "images.cosmeticmall.com", "www.backcountry.com", "ssl.booztx.com", "p.typekit.net", "use.typekit.net", "cdn.thewatershed.com", "www.sf-cdn.net", "static.cdn.dollarsdirect.com.au", "edge.redfordmediallc.com", "edge.pluralmediallc.com", "www.gourmetgiftbaskets.com", "www.numberinvestigator.com", "b2bportal.disneylandparis.com", "b2bportal.disneytravelagents.co.uk", "www.nwf.org", "assets.zendesk.com", "a.cdnkic.com", "s.cdnkic.com", "www.superbiketoystore.com", "cdn.stylethread.com.au", "cdn.cartrawler.com", "publicstaticcdn.tableausoftware.com", "secure.33across.com", "c.ztstatic.com", "c.mscimg.com", "static.teamtreehouse.com", "wac.a8b5.edgecastcdn.net"], "altCnt": 149, "hash": "d8:af:99:8d:b5:e0:42:a7:b4:7b:6d:41:62:75:00:a7:f7:ed:96:5c", "issuerCN": ["digicert high assurance ca-3"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1418212800000, "notBefore": 1317600000000, "publicAlgorithm": "rsaEncryption", "remainingDays": 372, "remainingSeconds": 32208325, "serial": "062d488986c9a6d7f94901c2b5906882", "subjectCN": ["gp1.wac.edgecastcdn.net"], "subjectON": ["EdgeCast Networks, Inc."], "validDays": 1164, "validSeconds": *********}, {"hash": "42:85:78:55:fb:0e:a4:3f:54:c9:91:1e:30:e7:79:1d:8c:e8:27:05", "issuerCN": ["digicert high assurance ev root ca"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1648944000000, "notBefore": 1207137600000, "publicAlgorithm": "rsaEncryption", "remainingDays": 3043, "remainingSeconds": 262939525, "serial": "0a5f114d035b179117d2efd4038c3f3b", "subjectCN": ["digicert high assurance ca-3"], "subjectON": ["DigiCert Inc"], "subjectOU": ["www.digicert.com"], "validDays": 5113, "validSeconds": 441806400}], "certCnt": 2, "client": {"bytes": 657}, "destination": {"bytes": 9120, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 13, "port": 1080}, "dstOui": ["Dell Inc."], "dstOuiCnt": 1, "dstPayload8": "005a99b50ab49cf9", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386004475691, "http": {"host": ["www.example.com"], "hostCnt": 1}, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1386004475761, "length": 69, "network": {"bytes": 10911, "community_id": "1:QFSWpQ4ebG5/0YcprIWXFTWEzvI=", "packets": 30}, "node": "test", "packetLen": [94, 90, 82, 107, 82, 90, 82, 202, 1530, 94, 82, 1530, 1530, 1530, 82, 82, 836, 82, 396, 129, 82, 253, 428, 82, 1377, 82, 109, 82, 82, 82], "packetPos": [11424, 11518, 11608, 11690, 11797, 11879, 11969, 12051, 12253, 13783, 13877, 13959, 15489, 17019, 18549, 18631, 18713, 19549, 19631, 20027, 20156, 20238, 20491, 20919, 21001, 22378, 22460, 22569, 22651, 22733], "protocol": ["socks", "tcp", "tls", "tlsrulestest"], "protocolCnt": 4, "segmentCnt": 1, "server": {"bytes": 8254}, "socks": {"host": "www.example.com", "port": 443}, "source": {"bytes": 1791, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 17, "port": 53555}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "040101bb00000001", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["cert:certificate-authority"], "tagsCnt": 1, "tcpflags": {"ack": 15, "dstZero": 0, "fin": 2, "psh": 11, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 2207000401}, "tls": {"cipher": ["TLS_RSA_WITH_RC4_128_SHA"], "cipherCnt": 1, "dstSessionId": ["8142ff7f5af97c1486dc2addaf9cb504fdfb6c26df171cbd15bd29551adacd03"], "ja3": ["06a92bf69b367389d2feb0d70501ddfe"], "ja3Cnt": 1, "ja3s": ["280ca4511bfaa384b2e931c058e8816e"], "ja3sCnt": 1, "ja3sstring": ["769,5,65281"], "ja3sstringCnt": 1, "ja3string": ["769,57-56-53-22-19-10-51-50-**************-5-4-21-18-9-20-17-8-6-3-255,0,,"], "ja3stringCnt": 1, "ja4": ["t10d230100_6a57a6f57151_000000000000"], "ja4Cnt": 1, "ja4_r": ["t10d230100_0003,0004,0005,0006,0008,0009,000a,0011,0012,0013,0014,0015,0016,002f,0032,0033,0035,0038,0039,0096,0099,009a,00ff_"], "ja4_rCnt": 1, "version": ["TLSv1"], "versionCnt": 1}, "totDataBytes": 8911}, "header": {"index": {"_index": "tests_sessions3-131202"}}}, {"body": {"@timestamp": "SET", "cert": [{"alt": ["gp1.wac.edgecastcdn.net", "www.edgecast.com", "wac.edgecastcdn.net", "ne.wac.edgecastcdn.net", "swf.mixpo.com", "cdn.traceregister.com", "s.tmocache.com", "s.my.tmocache.com", "e1.boxcdn.net", "e2.boxcdn.net", "e3.boxcdn.net", "www.sonos.com", "static-cache.tp-global.net", "ssl-cdn.sometrics.com", "cache.vehicleassets.captivelead.com", "static.woopra.com", "images.ink2.com", "assets-secure.razoo.com", "ec.pond5.com", "images.esellerpro.com", "use.typekit.com", "static.iseatz.com", "static.www.turnto.com", "inpath-static.iseatz.com", "secure.avelleassets.com", "static.dubli.com", "www-cdn.cinamuse.com", "www-cdn.cineble.com", "www-cdn.cinemaden.com", "www-cdn.filmlush.com", "www-cdn.flixaddict.com", "www-cdn.itshd.com", "www-cdn.moviease.com", "www-cdn.movielush.com", "www-cdn.reelhd.com", "www-cdn.pushplay.com", "cdn1.fishpond.co.nz", "cdn1.fishpond.com.au", "www.isaca.org", "cdn.optimizely.com", "static.shoedazzle.com", "www.travelrepublic.co.uk", "cdn.nprove.com", "sslbest.booztx.com", "www.travelrepublic.com", "www.blacklabelads.com", "cdn.whois.com.au", "ne1.wac.edgecastcdn.net", "gs1.wac.edgecastcdn.net", "c1.socialcastcontent.com", "www.steepandcheap.com", "www.whiskeymilitia.com", "www.chainlove.com", "www.tramdock.com", "www.bonktown.com", "www.brociety.com", "edgecast.onegrp.com", "cdn.psw.net", "cdn.gaggle.net", "www-cdn.reelvidz.com", "fast.fonts.com", "ec.xnglobalres.com", "images.vrbo.com", "beta.fileblaze.net", "cdn.brandsexclusive.com.au", "www-cdn.ireel.com", "cdcssl.ibsrv.net", "cdn.betchoice.com", "player.vzaar.com", "framegrabs.vzaar.com", "thumbs.vzaar.com", "stylistlounge.stelladot.com", "www.stelladot.com", "content.aqcdn.com", "content.ebgames.com.au", "content.ebgames.co.nz", "images.pagerage.com", "images.allsaints.com", "cdnb1.kodakgallery.com", "cdn.orbengine.com", "cdn.quickoffice.com", "content.glscrip.com", "cdn.bidfan.com", "media.quantumads.com", "cdn.allenbrothers.com", "pics.intelius.com", "pics.peoplelookup.com", "pics.lookupanyone.com", "cdn1-ssl.iha.com", "s.cdn-care.com", "cdn2-b.examiner.com", "cdn.trtk.net", "edgecdn.ink2.com", "ec.dstimage.disposolutions.com", "cdn.clytel.com", "welcome2.carsdirect.com", "s1.card-images.com", "update.alot.com", "www.outsystems.com", "www.drwmedia.com", "lookup.bluecava.com", "cdn.taxact.com", "cdn.taxactonline.com", "cdn.200581.com", "img.vxcdn.com", "js.vxcdn.com", "www.goal.com", "cdns1.kodakgallery.com", "edge.dropdowndeals.com", "edge.pagerage.com", "edge.sanityswitch.com", "edge.yontoo.com", "layers.yontoo.com", "cdn.widgetserver.com", "www.cloudwords.com", "edge.actaads.com", "images.skincarerx.com", "ssl.cdn-redfin.com", "small.outso-media.com", "cdn.foxycart.com", "edge.jeetyetmedia.com", "cdn.ticketfly.com", "images.cosmeticmall.com", "www.backcountry.com", "ssl.booztx.com", "p.typekit.net", "use.typekit.net", "cdn.thewatershed.com", "www.sf-cdn.net", "static.cdn.dollarsdirect.com.au", "edge.redfordmediallc.com", "edge.pluralmediallc.com", "www.gourmetgiftbaskets.com", "www.numberinvestigator.com", "b2bportal.disneylandparis.com", "b2bportal.disneytravelagents.co.uk", "www.nwf.org", "assets.zendesk.com", "a.cdnkic.com", "s.cdnkic.com", "www.superbiketoystore.com", "cdn.stylethread.com.au", "cdn.cartrawler.com", "publicstaticcdn.tableausoftware.com", "secure.33across.com", "c.ztstatic.com", "c.mscimg.com", "static.teamtreehouse.com", "wac.a8b5.edgecastcdn.net"], "altCnt": 149, "hash": "d8:af:99:8d:b5:e0:42:a7:b4:7b:6d:41:62:75:00:a7:f7:ed:96:5c", "issuerCN": ["digicert high assurance ca-3"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1418212800000, "notBefore": 1317600000000, "publicAlgorithm": "rsaEncryption", "remainingDays": 372, "remainingSeconds": 32208320, "serial": "062d488986c9a6d7f94901c2b5906882", "subjectCN": ["gp1.wac.edgecastcdn.net"], "subjectON": ["EdgeCast Networks, Inc."], "validDays": 1164, "validSeconds": *********}, {"hash": "42:85:78:55:fb:0e:a4:3f:54:c9:91:1e:30:e7:79:1d:8c:e8:27:05", "issuerCN": ["digicert high assurance ev root ca"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1648944000000, "notBefore": 1207137600000, "publicAlgorithm": "rsaEncryption", "remainingDays": 3043, "remainingSeconds": 262939520, "serial": "0a5f114d035b179117d2efd4038c3f3b", "subjectCN": ["digicert high assurance ca-3"], "subjectON": ["DigiCert Inc"], "subjectOU": ["www.digicert.com"], "validDays": 5113, "validSeconds": 441806400}], "certCnt": 2, "client": {"bytes": 646}, "destination": {"bytes": 9215, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 14, "port": 1080}, "dstOui": ["Dell Inc."], "dstOuiCnt": 1, "dstPayload8": "0500050000010ab4", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386004480852, "http": {"host": ["www.example.com"], "hostCnt": 1}, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1386004480888, "length": 35, "network": {"bytes": 11127, "community_id": "1:FU35c9jWM24tS4OJPq+kgps1akw=", "packets": 33}, "node": "test", "packetLen": [94, 90, 82, 86, 82, 84, 82, 92, 92, 82, 202, 1530, 1530, 106, 82, 82, 1530, 82, 1530, 824, 82, 396, 129, 82, 253, 428, 82, 1402, 82, 109, 82, 82, 82], "packetPos": [22815, 22909, 22999, 23081, 23167, 23249, 23333, 23415, 23507, 23599, 23681, 23883, 25413, 26943, 27049, 27131, 27213, 28743, 28825, 30355, 31179, 31261, 31657, 31786, 31868, 32121, 32549, 32631, 34033, 34115, 34224, 34306, 34388], "protocol": ["socks", "tcp", "tls", "tlsrulestest"], "protocolCnt": 4, "segmentCnt": 1, "server": {"bytes": 8283}, "socks": {"ASN": "AS15133 MCI Communications Services, Inc. d/b/a Verizon Business", "GEO": "US", "RIR": "RIPE", "ip": "**************", "port": 443}, "source": {"bytes": 1912, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 19, "port": 53556}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "0502000105010001", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["cert:certificate-authority"], "tagsCnt": 1, "tcpflags": {"ack": 16, "dstZero": 0, "fin": 2, "psh": 13, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 3710039944}, "tls": {"cipher": ["TLS_RSA_WITH_RC4_128_SHA"], "cipherCnt": 1, "dstSessionId": ["f7186f0670e348e24ec9b816eb2a2832c21ce3a14ea81aa1a388f91c3cd014b1"], "ja3": ["06a92bf69b367389d2feb0d70501ddfe"], "ja3Cnt": 1, "ja3s": ["280ca4511bfaa384b2e931c058e8816e"], "ja3sCnt": 1, "ja3sstring": ["769,5,65281"], "ja3sstringCnt": 1, "ja3string": ["769,57-56-53-22-19-10-51-50-**************-5-4-21-18-9-20-17-8-6-3-255,0,,"], "ja3stringCnt": 1, "ja4": ["t10d230100_6a57a6f57151_000000000000"], "ja4Cnt": 1, "ja4_r": ["t10d230100_0003,0004,0005,0006,0008,0009,000a,0011,0012,0013,0014,0015,0016,002f,0032,0033,0035,0038,0039,0096,0099,009a,00ff_"], "ja4_rCnt": 1, "version": ["TLSv1"], "versionCnt": 1}, "totDataBytes": 8929}, "header": {"index": {"_index": "tests_sessions3-131202"}}}]}