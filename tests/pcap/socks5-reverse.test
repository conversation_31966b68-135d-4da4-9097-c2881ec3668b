{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 23392}, "destination": {"as": {"full": "AS1 Cool Beans!", "number": 1, "organization": {"name": "Cool Beans!"}}, "bytes": 2199, "geo": {"country_iso_code": "CA"}, "ip": "********", "mac": ["00:00:5e:00:01:01", "80:71:1f:83:9f:c6"], "mac-cnt": 2, "packets": 21, "port": 8855}, "dstOui": ["ICANN, IANA Department", "Juniper Networks"], "dstOuiCnt": 2, "dstPayload8": "050100050100014a", "dstRIR": "TEST", "dstTTL": [56], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386790367120, "http": {"bodyMagic": ["application/x-gzip", "text/html"], "bodyMagicCnt": 2, "clientVersion": ["1.1"], "clientVersionCnt": 1, "cookieKey": ["NID", "PREF"], "cookieKeyCnt": 2, "cookieValue": ["xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"], "cookieValueCnt": 2, "host": ["www.google.com"], "hostCnt": 1, "key": ["client", "gl", "hl", "num", "q", "rls", "start", "uule"], "keyCnt": 8, "md5": ["2069181ae704855f29caf964ca52ec49", "b0cecae354b9eab1f04f70e46a612cb1"], "md5Cnt": 2, "method": ["GET"], "method-GET": 2, "methodCnt": 1, "path": ["/search"], "pathCnt": 1, "request-referer": ["", "http://www.google.com/search?client=firefox&rls=en&q=sheepskin%20boots&start=0&num=10&hl=en&gl=us&uule=w+CAIQICINVW5pdGVkIFN0YXRlcw"], "request-refererCnt": 2, "requestHeader": ["accept", "accept-encoding", "cookie", "host", "referer", "user-agent"], "requestHeaderCnt": 6, "requestHeaderField": ["alternate-protocol", "alternate-protocol", "cache-control", "cache-control", "content-encoding", "content-length", "content-type", "content-type", "date", "date", "expires", "expires", "p3p", "pragma", "server", "server", "set-cookie", "set-cookie", "transfer-encoding", "x-frame-options", "x-frame-options", "x-xss-protection", "x-xss-protection"], "requestHeaderValue": ["-1", "1; mode=block", "1; mode=block", "409", "80:quic", "80:quic", "chunked", "cp=\"this is not a p3p policy! see http://www.google.com/support/accounts/bin/answer.py?hl=en&answer=151657 for more info.\"", "fri, 01 jan 1990 00:00:00 gmt", "gws", "gzip", "http server (unknown)", "nid=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx; expires=thu, 12-jun-2014 19:32:57 gmt; path=/; domain=.google.com; httponly", "no-cache", "no-store, no-cache, must-revalidate, post-check=0, pre-check=0", "pref=id=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx; expires=fri, 11-dec-2015 19:32:57 gmt; path=/; domain=.google.com", "private, max-age=0", "sameorigin", "sameorigin", "text/html; charset=utf-8", "text/html; charset=utf-8", "wed, 11 dec 2013 19:32:57 gmt", "wed, 11 dec 2013 19:33:15 gmt"], "requestHeaderValueCnt": 23, "response-location": ["http://ipv4.google.com/sorry/IndexRedirect?continue=http://www.google.com/search?client=firefox&rls=en&q=sheepskin%20boots&start=10&num=10&hl=en&gl=us&uule=xxxxxxxxxxxxxxxxxxxxxxxxxxxx"], "responseHeader": ["alternate-protocol", "cache-control", "content-encoding", "content-length", "content-type", "date", "expires", "location", "p3p", "pragma", "server", "set-cookie", "transfer-encoding", "x-frame-options", "x-xss-protection"], "responseHeaderCnt": 15, "responseHeaderField": ["accept", "accept", "accept-encoding", "accept-encoding", "cookie"], "responseHeaderValue": ["*/*", "*/*", "deflate, gzip", "deflate, gzip", "nid=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx; pref=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"], "responseHeaderValueCnt": 5, "serverVersion": ["1.1"], "serverVersionCnt": 1, "sha256": ["3de069d74ca3feb4d7a2fc381045e6d2f422d7b5de307cebbc61889d2724bc2d", "6b4076081406f4529d876579ee60731ef737b1e99e42cf5e9058b19cbfa313b6"], "sha256Cnt": 2, "statuscode": [200, 302], "statuscodeCnt": 2, "uri": ["www.google.com/search?client=firefox&rls=en&q=Sheepskin%20Boots&start=0&num=10&hl=en&gl=us&uule=xxxxxxxxxxxxxxxxxxxxxxxxxxxx", "www.google.com/search?client=firefox&rls=en&q=Sheepskin%20Boots&start=10&num=10&hl=en&gl=us&uule=xxxxxxxxxxxxxxxxxxxxxxxxxxxx"], "uriCnt": 2, "useragent": ["Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 1.1.4322)"], "useragentCnt": 1, "value": ["0", "10", "en", "firefox", "sheepskin boots", "us", "xxxxxxxxxxxxxxxxxxxxxxxxxxxx"], "valueCnt": 7}, "initRTT": 694, "ipProtocol": 6, "lastPacket": 1386790404657, "length": 37537, "network": {"bytes": 27311, "community_id": "1:I/NY/ZdvnK7EtFfAgYuqVZkOKxY=", "packets": 52}, "node": "test", "packetLen": [82, 82, 76, 76, 76, 80, 76, 76, 76, 80, 363, 76, 1094, 76, 1430, 424, 76, 1430, 1430, 76, 1430, 76, 1430, 76, 1430, 1430, 154, 76, 76, 1430, 758, 76, 1430, 758, 76, 1430, 758, 76, 1430, 76, 758, 1430, 76, 758, 76, 1238, 718, 76, 1054, 76, 76, 76], "packetPos": [24, 106, 188, 264, 340, 416, 496, 572, 648, 724, 804, 1167, 1243, 2337, 2413, 3843, 4267, 4343, 5773, 7203, 7279, 8709, 8785, 10215, 10291, 11721, 13151, 13305, 13381, 13457, 14887, 15645, 15721, 17151, 17909, 17985, 19415, 20173, 20249, 21679, 21755, 22513, 23943, 24019, 24777, 24853, 26091, 26809, 26885, 27939, 28015, 28091], "protocol": ["http", "socks", "socksipset", "tcp"], "protocolCnt": 4, "segmentCnt": 1, "server": {"bytes": 954}, "socks": {"ASN": "AS15169 Google LLC", "GEO": "US", "RIR": "ARIN", "ip": "**************", "port": 80}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 25112, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:0a:f3:31:94:00"], "mac-cnt": 1, "packets": 31, "port": 54263}, "srcOui": ["Cisco Systems, Inc"], "srcOuiCnt": 1, "srcPayload8": "4e5a8d08874e0500", "srcTTL": [126], "srcTTLCnt": 1, "tags": ["dstip", "srcip"], "tagsCnt": 2, "tcpflags": {"ack": 20, "dstZero": 0, "fin": 1, "psh": 29, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 1607305587}, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [33554442], "string.snow": ["16777226:54263,33554442:8855"]}, "totDataBytes": 24346}, "header": {"index": {"_index": "tests_sessions3-131211"}}}]}