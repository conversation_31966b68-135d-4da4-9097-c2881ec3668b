{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 581}, "destination": {"as": {"full": "AS1 Cool Beans!", "number": 1, "organization": {"name": "Cool Beans!"}}, "bytes": 62, "geo": {"country_iso_code": "CA"}, "ip": "********", "mac": ["00:00:5e:00:01:01", "00:1d:b5:ce:ef:c0"], "mac-cnt": 2, "packets": 1, "port": 80}, "dstOui": ["ICANN, IANA Department", "Juniper Networks"], "dstOuiCnt": 2, "dstRIR": "TEST", "dstTTL": [118], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1414887555048, "http": {"authType": ["digest"], "authTypeCnt": 1, "clientVersion": ["1.1"], "clientVersionCnt": 1, "host": ["xxxxxxx.xxxxxxxxxxx.xxx"], "hostCnt": 1, "method": ["GET"], "method-GET": 1, "methodCnt": 1, "path": ["/xxxxxxxxxxxxxxxxxxxxxxxxxxx/xxxx/xxxxxxxxxxxxxxxxxxxxxxxx"], "pathCnt": 1, "request-authorization": ["Digest username=\"<EMAIL>\",realm=\"xxxxxxx\",nonce=\"xxxxxxxxxxxxxxxx\",uri=\"/xxxxxxxxxxxxxxxxxxxxxxxxxxx/xxxx/xxxxxxxxxxxxxxxxxxxxxxxx\",algorithm=\"MD5\",cnonce=\"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\",nc=00000003,qop=\"auth\",response=\"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\",opaque=\"\""], "request-authorizationCnt": 1, "requestHeader": ["authorization", "cache-control", "host", "pragma", "user-agent", "xxxxxxxxxxxxxxxxxx"], "requestHeaderCnt": 6, "requestHeaderField": ["cache-control", "pragma", "xxxxxxxxxxxxxxxxxx"], "requestHeaderValue": ["no-cache", "no-store,no-cache", "product=xxxxxxxxxx;xxxxxxxxxx=xxxxxxxxx;xx=xxxxx;xxxxxxxxx;xxxxxxxxxx"], "requestHeaderValueCnt": 3, "uri": ["xxxxxxx.xxxxxxxxxxx.xxx/xxxxxxxxxxxxxxxxxxxxxxxxxxx/xxxx/xxxxxxxxxxxxxxxxxxxxxxxx"], "uriCnt": 1, "user": ["<EMAIL>"], "userCnt": 1, "useragent": ["xxxxxxxxxxxxxxxxxx"], "useragentCnt": 1}, "initRTT": 94, "ipProtocol": 6, "lastPacket": 1414887555242, "length": 194, "network": {"bytes": 819, "community_id": "1:6EZ0y1VNTROJYrhnFw+IN3bG61U=", "packets": 4}, "node": "test", "packetLen": [78, 78, 76, 651], "packetPos": [24, 102, 180, 256], "protocol": ["http", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 757, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:0f:f7:76:82:80"], "mac-cnt": 1, "packets": 3, "port": 4411}, "srcOui": ["Cisco Systems, Inc"], "srcOuiCnt": 1, "srcPayload8": "474554202f787878", "srcTTL": [126], "srcTTLCnt": 1, "tags": ["dstip", "srcip"], "tagsCnt": 2, "tcpflags": {"ack": 1, "dstZero": 0, "fin": 0, "psh": 1, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 1810654027}, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [33554442], "string.snow": ["16777226:4411,33554442:80"]}, "totDataBytes": 581}, "header": {"index": {"_index": "tests_sessions3-141102"}}}]}