{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 427}, "destination": {"bytes": 12880, "ip": "*********", "mac": ["aa:aa:aa:aa:aa:aa"], "mac-cnt": 1, "packets": 11, "port": 80}, "dstPayload8": "485454502f312e31", "dstTTL": [228], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1467366160266, "http": {"bodyMagic": ["application/x-gzip"], "bodyMagicCnt": 1, "clientVersion": ["1.1"], "clientVersionCnt": 1, "host": ["js.navigator.io"], "hostCnt": 1, "md5": ["38bf34783fc26180631a614a88301cab"], "md5Cnt": 1, "method": ["GET"], "method-GET": 1, "methodCnt": 1, "path": ["/1"], "pathCnt": 1, "request-referer": ["http://xxxxxxx.xxxxx.xx/news/xxxxxxx/xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"], "request-refererCnt": 1, "requestHeader": ["accept", "accept-encoding", "accept-language", "connection", "host", "referer", "user-agent"], "requestHeaderCnt": 7, "requestHeaderField": ["accept", "accept-encoding", "accept-language", "connection"], "requestHeaderValue": ["*/*", "gzip, deflate, xxxx", "keep-alive", "xx-xx,et;q=0.8,en-us;q=0.6,en;q=0.4"], "requestHeaderValueCnt": 4, "responseHeader": ["access-control-allow-origin", "cache-control", "connection", "content-encoding", "content-type", "date", "expires", "pragma", "server", "transfer-encoding", "vary"], "responseHeaderCnt": 11, "responseHeaderField": ["access-control-allow-origin", "cache-control", "connection", "content-encoding", "content-type", "date", "expires", "pragma", "server", "transfer-encoding", "vary"], "responseHeaderValue": ["*", "0", "accept-encoding", "application/javascript", "chunked", "fri, 01 jul 2016 09:42:40 gmt", "gzip", "keep-alive", "must-revalidate, post-check=0, pre-check=0", "nginx", "public"], "responseHeaderValueCnt": 11, "serverVersion": ["1.1"], "serverVersionCnt": 1, "sha256": ["ac518a69ca82f4efbd3ea94d4d423d843582771127ef6d214c3d7a45acf7be04"], "sha256Cnt": 1, "statuscode": [200], "statuscodeCnt": 1, "uri": ["js.navigator.io/1"], "uriCnt": 1, "useragent": ["Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36"], "useragentCnt": 1}, "ipProtocol": 6, "lastPacket": 1467366160339, "length": 73, "network": {"bytes": 13424, "community_id": "1:+Q7ZZLs5Blo4/kXY55Yf6dTtt2Y=", "packets": 13}, "node": "test", "packetLen": [84, 76, 500, 72, 1532, 1532, 1532, 1532, 1532, 1084, 1532, 1532, 1092], "packetPos": [24, 108, 184, 684, 756, 2288, 3820, 5352, 6884, 8416, 9500, 11032, 12564], "protocol": ["http", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 12252}, "source": {"bytes": 544, "ip": "*********", "mac": ["aa:aa:aa:aa:aa:aa"], "mac-cnt": 1, "packets": 2, "port": 10882}, "srcPayload8": "474554202f312048", "srcTTL": [127], "srcTTLCnt": 1, "tcpflags": {"ack": 9, "dstZero": 0, "fin": 0, "psh": 3, "rst": 0, "srcZero": 0, "syn": 0, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 2552120857}, "totDataBytes": 12679}, "header": {"index": {"_index": "tests_sessions3-160701"}}}]}