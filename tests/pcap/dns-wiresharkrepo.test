{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 388}, "destination": {"bytes": 1328, "ip": "**************", "mac": ["00:c0:9f:32:41:8c"], "mac-cnt": 1, "packets": 12, "port": 53}, "dns": [{"answers": [{"class": "IN", "cname": "www.l.google.com", "name": "www.google.com", "ttl": 633, "type": "CNAME"}], "answersCnt": 1, "headerFlags": ["RA", "RD"], "host": ["www.google.com", "www.l.google.com"], "hostCnt": 2, "opcode": "QUERY", "qc": "IN", "qt": "AAAA", "queryHost": "www.google.com", "status": "NOERROR"}, {"answersCnt": 0, "headerFlags": ["AA", "RA", "RD"], "host": ["www.example.notginh"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "AAAA", "queryHost": "www.example.notginh", "status": "NXDOMAIN"}, {"ASN": ["AS1280 Internet Systems Consortium, Inc."], "GEO": ["US"], "RIR": ["ARIN"], "answers": [{"class": "IN", "ip": "**************", "name": "www.netbsd.org", "ttl": 82159, "type": "A"}], "answersCnt": 1, "headerFlags": ["RA", "RD"], "host": ["www.netbsd.org"], "hostCnt": 1, "ip": ["**************"], "ipCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "A", "queryHost": "www.netbsd.org", "status": "NOERROR"}, {"answers": [{"class": "IN", "name": "google.com", "ttl": 270, "txt": "v=spf1 ptr ?all", "type": "TXT"}], "answersCnt": 1, "headerFlags": ["RA", "RD"], "host": ["google.com"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "TXT", "queryHost": "google.com", "status": "NOERROR"}, {"ASN": ["AS1280 Internet Systems Consortium, Inc."], "GEO": ["US"], "RIR": [""], "answers": [{"class": "IN", "ip": "2001:4f8:4:7:2e0:81ff:fe52:9a6b", "name": "www.netbsd.org", "ttl": 86340, "type": "AAAA"}], "answersCnt": 1, "headerFlags": ["RA", "RD"], "host": ["www.netbsd.org"], "hostCnt": 1, "ip": ["2001:04f8:0004:0007:02e0:81ff:fe52:9a6b"], "ipCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "AAAA", "queryHost": "www.netbsd.org", "status": "NOERROR"}, {"ASN": ["AS1280 Internet Systems Consortium, Inc."], "GEO": ["US"], "RIR": [""], "answers": [{"class": "IN", "ip": "2001:4f8:4:7:2e0:81ff:fe52:9a6b", "name": "www.netbsd.org", "ttl": 86400, "type": "AAAA"}], "answersCnt": 1, "headerFlags": ["RA", "RD"], "host": ["www.netbsd.org"], "hostCnt": 1, "ip": ["2001:04f8:0004:0007:02e0:81ff:fe52:9a6b"], "ipCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "AAAA", "queryHost": "www.netbsd.org", "status": "NOERROR"}, {"answersCnt": 0, "headerFlags": ["RA", "RD"], "host": ["www.l.google.com"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "AAAA", "queryHost": "www.l.google.com", "status": "NOERROR"}, {"answers": [{"class": "IN", "mx": "smtp4.google.com", "name": "google.com", "priority": 40, "ttl": 552, "type": "MX"}, {"class": "IN", "mx": "smtp5.google.com", "name": "google.com", "priority": 10, "ttl": 552, "type": "MX"}, {"class": "IN", "mx": "smtp6.google.com", "name": "google.com", "priority": 10, "ttl": 552, "type": "MX"}, {"class": "IN", "mx": "smtp1.google.com", "name": "google.com", "priority": 10, "ttl": 552, "type": "MX"}, {"class": "IN", "mx": "smtp2.google.com", "name": "google.com", "priority": 10, "ttl": 552, "type": "MX"}, {"class": "IN", "mx": "smtp3.google.com", "name": "google.com", "priority": 40, "ttl": 552, "type": "MX"}, {"class": "IN", "ip": "*************", "name": "smtp4.google.com", "ttl": 600, "type": "A"}, {"class": "IN", "ip": "*************", "name": "smtp5.google.com", "ttl": 600, "type": "A"}, {"class": "IN", "ip": "***********", "name": "smtp6.google.com", "ttl": 600, "type": "A"}, {"class": "IN", "ip": "*************", "name": "smtp1.google.com", "ttl": 600, "type": "A"}, {"class": "IN", "ip": "*************", "name": "smtp2.google.com", "ttl": 600, "type": "A"}, {"class": "IN", "ip": "*************", "name": "smtp3.google.com", "ttl": 600, "type": "A"}], "answersCnt": 12, "headerFlags": ["RA", "RD"], "host": ["google.com"], "hostCnt": 1, "mailserverASN": ["AS15169 Google LLC", "AS15169 Google LLC", "AS15169 Google LLC", "AS15169 Google LLC", "AS15169 Google LLC", "AS15169 Google LLC"], "mailserverGEO": ["MX", "MX", "US", "US", "US", "US"], "mailserverHost": ["smtp1.google.com", "smtp2.google.com", "smtp3.google.com", "smtp4.google.com", "smtp5.google.com", "smtp6.google.com"], "mailserverHostCnt": 6, "mailserverIp": ["*************", "*************", "*************", "*************", "*************", "***********"], "mailserverIpCnt": 6, "mailserverRIR": ["ARIN", "ARIN", "ARIN", "ARIN", "ARIN", "ARIN"], "opcode": "QUERY", "qc": "IN", "qt": "MX", "queryHost": "google.com", "status": "NOERROR"}, {"answersCnt": 0, "headerFlags": ["RA", "RD"], "host": ["www.example.com"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "AAAA", "queryHost": "www.example.com", "status": "NOERROR"}, {"answersCnt": 0, "headerFlags": ["RA", "RD"], "host": ["google.com"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "LOC", "queryHost": "google.com", "status": "NOERROR"}, {"ASN": ["AS1280 Internet Systems Consortium, Inc.", "AS1280 Internet Systems Consortium, Inc."], "GEO": ["US", "US"], "RIR": ["", "ARIN"], "answers": [{"class": "IN", "ip": "2001:4f8:0:2::d", "name": "www.isc.org", "ttl": 600, "type": "AAAA"}, {"class": "IN", "ip": "**************", "name": "www.isc.org", "ttl": 600, "type": "A"}], "answersCnt": 2, "headerFlags": ["RA", "RD"], "host": ["www.isc.org"], "hostCnt": 1, "ip": ["2001:04f8:0000:0002:0000:0000:0000:000d", "**************"], "ipCnt": 2, "opcode": "QUERY", "qc": "IN", "qt": "ANY", "queryHost": "www.isc.org", "status": "NOERROR"}, {"answers": [{"class": "IN", "name": "************.in-addr.arpa", "ttl": 86309, "type": "PTR"}], "answersCnt": 1, "headerFlags": ["RA", "RD"], "host": ["************.in-addr.arpa"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "PTR", "queryHost": "************.in-addr.arpa", "status": "NOERROR"}], "dnsCnt": 12, "dstOui": ["Quanta Computer Inc"], "dstOuiCnt": 1, "dstPayload8": "1032818000010001", "dstRIR": "ARIN", "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172466496, "ipProtocol": 17, "lastPacket": 1112172737733, "length": 271237, "network": {"bytes": 2220, "community_id": "1:PIDOtGFIHUsiDppmnIxS0PK/t7Y=", "packets": 24}, "node": "test", "packetLen": [86, 114, 86, 314, 86, 86, 101, 145, 90, 106, 90, 118, 90, 118, 90, 110, 92, 92, 91, 91, 95, 95, 87, 131], "packetPos": [24, 110, 224, 310, 624, 710, 796, 897, 1042, 1132, 1238, 1328, 1446, 1536, 1654, 1744, 1854, 1946, 2038, 2129, 2220, 2315, 2410, 2497], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 824}, "source": {"bytes": 892, "ip": "*************", "mac": ["00:e0:18:b1:0c:ad"], "mac-cnt": 1, "packets": 12, "port": 32795}, "srcOui": ["<PERSON><PERSON><PERSON>"], "srcOuiCnt": 1, "srcPayload8": "1032010000010000", "srcRIR": "ARIN", "srcTTL": [64], "srcTTLCnt": 1, "totDataBytes": 1212}, "header": {"index": {"_index": "tests_sessions3-050330"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 40}, "destination": {"bytes": 105, "ip": "**************", "mac": ["00:c0:9f:32:41:8c"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answers": [{"class": "IN", "name": "*********.in-addr.arpa", "ttl": 3600, "type": "PTR"}], "answersCnt": 1, "headerFlags": ["AA", "RA", "RD"], "host": ["*********.in-addr.arpa"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "PTR", "queryHost": "*********.in-addr.arpa", "status": "NOERROR"}], "dnsCnt": 1, "dstOui": ["Quanta Computer Inc"], "dstOuiCnt": 1, "dstPayload8": "5a53858000010001", "dstRIR": "ARIN", "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172737737, "ipProtocol": 17, "lastPacket": 1112172737737, "length": 0, "network": {"bytes": 187, "community_id": "1:5hLk4/1TLHDVutfqIPb3yiUsYBE=", "packets": 2}, "node": "test", "packetLen": [98, 121], "packetPos": [2628, 2726], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 63}, "source": {"bytes": 82, "ip": "*************", "mac": ["00:e0:18:b1:0c:ad"], "mac-cnt": 1, "packets": 1, "port": 32796}, "srcOui": ["<PERSON><PERSON><PERSON>"], "srcOuiCnt": 1, "srcPayload8": "5a53010000010000", "srcRIR": "ARIN", "srcTTL": [64], "srcTTLCnt": 1, "totDataBytes": 103}, "header": {"index": {"_index": "tests_sessions3-050330"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 25}, "destination": {"bytes": 166, "ip": "**************", "mac": ["00:c0:9f:32:41:8c"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answers": [{"class": "IN", "name": "isc.org", "nameserver": "ns-ext.nrt1.isc.org", "ttl": 3600, "type": "NS"}, {"class": "IN", "name": "isc.org", "nameserver": "ns-ext.sth1.isc.org", "ttl": 3600, "type": "NS"}, {"class": "IN", "name": "isc.org", "nameserver": "ns-ext.isc.org", "ttl": 3600, "type": "NS"}, {"class": "IN", "name": "isc.org", "nameserver": "ns-ext.lga1.isc.org", "ttl": 3600, "type": "NS"}], "answersCnt": 4, "headerFlags": ["RA", "RD"], "host": ["isc.org"], "hostCnt": 1, "nameserverHost": ["ns-ext.isc.org", "ns-ext.lga1.isc.org", "ns-ext.nrt1.isc.org", "ns-ext.sth1.isc.org"], "nameserverHostCnt": 4, "opcode": "QUERY", "qc": "IN", "qt": "NS", "queryHost": "isc.org", "status": "NOERROR"}], "dnsCnt": 1, "dstOui": ["Quanta Computer Inc"], "dstOuiCnt": 1, "dstPayload8": "208a818000010004", "dstRIR": "ARIN", "dstTTL": [128], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172737740, "ipProtocol": 17, "lastPacket": 1112172737758, "length": 18, "network": {"bytes": 233, "community_id": "1:1PV+BXh7g/oZoNfFL5dz1Vkf+xo=", "packets": 2}, "node": "test", "packetLen": [83, 182], "packetPos": [2847, 3075], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 124}, "source": {"bytes": 67, "ip": "*************", "mac": ["00:e0:18:b1:0c:ad"], "mac-cnt": 1, "packets": 1, "port": 32797}, "srcOui": ["<PERSON><PERSON><PERSON>"], "srcOuiCnt": 1, "srcPayload8": "208a010000010000", "srcRIR": "ARIN", "srcTTL": [64], "srcTTLCnt": 1, "totDataBytes": 149}, "header": {"index": {"_index": "tests_sessions3-050330"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 87}, "destination": {"as": {"full": "AS15659 NextGenTel AS", "number": 15659, "organization": {"name": "NextGenTel AS"}}, "bytes": 129, "geo": {"country_iso_code": "NO"}, "ip": "***********", "mac": ["00:12:a9:00:32:23"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answersCnt": 0, "headerFlags": ["AA", "RA", "RD"], "host": ["_ldap._tcp.default-first-site-name._sites.dc._msdcs.utelsystems.local"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "SRV", "queryHost": "_ldap._tcp.default-first-site-name._sites.dc._msdcs.utelsystems.local", "status": "NXDOMAIN"}], "dnsCnt": 1, "dstOui": ["3Com Ltd"], "dstOuiCnt": 1, "dstPayload8": "326e858300010000", "dstRIR": "RIPE", "dstTTL": [58], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172737755, "ipProtocol": 17, "lastPacket": 1112172737775, "length": 19, "network": {"bytes": 258, "community_id": "1:ohgJJxCtl8hquEFlCrXctgmMr5I=", "packets": 2}, "node": "test", "packetLen": [145, 145], "packetPos": [2930, 3257], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 87}, "source": {"bytes": 129, "ip": "**************", "mac": ["00:60:08:45:e4:55"], "mac-cnt": 1, "packets": 1, "port": 1707}, "srcOui": ["3Com"], "srcOuiCnt": 1, "srcPayload8": "326e010000010000", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "totDataBytes": 174}, "header": {"index": {"_index": "tests_sessions3-050330"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 56}, "destination": {"as": {"full": "AS15659 NextGenTel AS", "number": 15659, "organization": {"name": "NextGenTel AS"}}, "bytes": 98, "geo": {"country_iso_code": "NO"}, "ip": "***********", "mac": ["00:12:a9:00:32:23"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answersCnt": 0, "headerFlags": ["AA", "RA", "RD"], "host": ["_ldap._tcp.dc._msdcs.utelsystems.local"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "SRV", "queryHost": "_ldap._tcp.dc._msdcs.utelsystems.local", "status": "NXDOMAIN"}], "dnsCnt": 1, "dstOui": ["3Com Ltd"], "dstOuiCnt": 1, "dstPayload8": "f161858300010000", "dstRIR": "RIPE", "dstTTL": [58], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172737776, "ipProtocol": 17, "lastPacket": 1112172737793, "length": 17, "network": {"bytes": 196, "community_id": "1:hqisi7acI4ga52k6zobiWGqEIOU=", "packets": 2}, "node": "test", "packetLen": [114, 114], "packetPos": [3402, 3516], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 56}, "source": {"bytes": 98, "ip": "**************", "mac": ["00:60:08:45:e4:55"], "mac-cnt": 1, "packets": 1, "port": 1708}, "srcOui": ["3Com"], "srcOuiCnt": 1, "srcPayload8": "f161010000010000", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "totDataBytes": 112}, "header": {"index": {"_index": "tests_sessions3-050330"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 98}, "destination": {"as": {"full": "AS15659 NextGenTel AS", "number": 15659, "organization": {"name": "NextGenTel AS"}}, "bytes": 140, "geo": {"country_iso_code": "NO"}, "ip": "***********", "mac": ["00:12:a9:00:32:23"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answersCnt": 0, "headerFlags": ["AA", "RA", "RD"], "host": ["_ldap._tcp.05b5292b-34b8-4fb7-85a3-8beef5fd2069.domains._msdcs.utelsystems.local"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "SRV", "queryHost": "_ldap._tcp.05b5292b-34b8-4fb7-85a3-8beef5fd2069.domains._msdcs.utelsystems.local", "status": "NXDOMAIN"}], "dnsCnt": 1, "dstOui": ["3Com Ltd"], "dstOuiCnt": 1, "dstPayload8": "8361858300010000", "dstRIR": "RIPE", "dstTTL": [58], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172737794, "ipProtocol": 17, "lastPacket": 1112172737813, "length": 19, "network": {"bytes": 280, "community_id": "1:plvYpJqXTOAQPov3pvYacIRfowE=", "packets": 2}, "node": "test", "packetLen": [156, 156], "packetPos": [3630, 3786], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 98}, "source": {"bytes": 140, "ip": "**************", "mac": ["00:60:08:45:e4:55"], "mac-cnt": 1, "packets": 1, "port": 1709}, "srcOui": ["3Com"], "srcOuiCnt": 1, "srcPayload8": "8361010000010000", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "totDataBytes": 196}, "header": {"index": {"_index": "tests_sessions3-050330"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 41}, "destination": {"as": {"full": "AS15659 NextGenTel AS", "number": 15659, "organization": {"name": "NextGenTel AS"}}, "bytes": 83, "geo": {"country_iso_code": "NO"}, "ip": "***********", "mac": ["00:12:a9:00:32:23"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answersCnt": 0, "headerFlags": ["AA", "RA", "RD"], "host": ["grimm.utelsystems.local"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "A", "queryHost": "grimm.utelsystems.local", "status": "NXDOMAIN"}], "dnsCnt": 1, "dstOui": ["3Com Ltd"], "dstOuiCnt": 1, "dstPayload8": "d060858300010000", "dstRIR": "RIPE", "dstTTL": [58], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172737915, "ipProtocol": 17, "lastPacket": 1112172737932, "length": 16, "network": {"bytes": 166, "community_id": "1:BYN5bVH5Ip1rnFDnzEUTqwo5d9E=", "packets": 2}, "node": "test", "packetLen": [99, 99], "packetPos": [3942, 4041], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 41}, "source": {"bytes": 83, "ip": "**************", "mac": ["00:60:08:45:e4:55"], "mac-cnt": 1, "packets": 1, "port": 1710}, "srcOui": ["3Com"], "srcOuiCnt": 1, "srcPayload8": "d060010000010000", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "totDataBytes": 82}, "header": {"index": {"_index": "tests_sessions3-050330"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 41}, "destination": {"as": {"full": "AS15659 NextGenTel AS", "number": 15659, "organization": {"name": "NextGenTel AS"}}, "bytes": 83, "geo": {"country_iso_code": "NO"}, "ip": "***********", "mac": ["00:12:a9:00:32:23"], "mac-cnt": 1, "packets": 1, "port": 53}, "dns": [{"answersCnt": 0, "headerFlags": ["AA", "RA", "RD"], "host": ["grimm.utelsystems.local"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "A", "queryHost": "grimm.utelsystems.local", "status": "NXDOMAIN"}], "dnsCnt": 1, "dstOui": ["3Com Ltd"], "dstOuiCnt": 1, "dstPayload8": "7663858300010000", "dstRIR": "RIPE", "dstTTL": [58], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1112172745357, "ipProtocol": 17, "lastPacket": 1112172745375, "length": 18, "network": {"bytes": 166, "community_id": "1:yyjGfQMyZcoNoQa+U4siXkQP9K4=", "packets": 2}, "node": "test", "packetLen": [99, 99], "packetPos": [4140, 4239], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 41}, "source": {"bytes": 83, "ip": "**************", "mac": ["00:60:08:45:e4:55"], "mac-cnt": 1, "packets": 1, "port": 1711}, "srcOui": ["3Com"], "srcOuiCnt": 1, "srcPayload8": "7663010000010000", "srcRIR": "ARIN", "srcTTL": [128], "srcTTLCnt": 1, "totDataBytes": 82}, "header": {"index": {"_index": "tests_sessions3-050330"}}}]}