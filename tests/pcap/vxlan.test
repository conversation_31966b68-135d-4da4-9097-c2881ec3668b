{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 0}, "destination": {"bytes": 0, "ip": "*************", "mac": ["ba:09:2b:6e:f8:be", "ff:ff:ff:ff:ff:ff"], "mac-cnt": 2, "packets": 0, "port": 0}, "dstOuterMac": ["08:00:27:ae:4d:62", "08:00:27:f2:1d:8c"], "dstOuterMac-cnt": 2, "dstOuterOui": ["PCS Computer Systems GmbH"], "dstOuterOuiCnt": 1, "dstRIR": "ARIN", "ethertype": 2048, "fileId": [], "firstPacket": 1467818432675, "ipProtocol": 17, "lastPacket": 1467818432675, "length": 0, "network": {"bytes": 184, "packets": 2}, "node": "test", "packetLen": [108, 108], "packetPos": [24, 132], "protocol": ["arp", "vxlan"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"bytes": 184, "ip": "*************", "mac": ["4a:7f:01:3b:a2:71", "ba:09:2b:6e:f8:be"], "mac-cnt": 2, "packets": 2, "port": 0}, "srcOuterMac": ["08:00:27:ae:4d:62", "08:00:27:f2:1d:8c"], "srcOuterMac-cnt": 2, "srcOuterOui": ["PCS Computer Systems GmbH"], "srcOuterOuiCnt": 1, "srcRIR": "ARIN", "srcTTL": [64], "srcTTLCnt": 1, "totDataBytes": 0, "vni": [123], "vniCnt": 1}, "header": {"index": {"_index": "tests_sessions3-160706"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 256}, "destination": {"as": {"full": "AS1 Cool Beans!", "number": 1, "organization": {"name": "Cool Beans!"}}, "bytes": 592, "geo": {"country_iso_code": "CA"}, "ip": "********", "mac": ["4a:7f:01:3b:a2:71"], "mac-cnt": 1, "packets": 4, "port": 0}, "dstOuterASN": ["---", "---"], "dstOuterGEO": ["---", "---"], "dstOuterIp": ["*************", "*************"], "dstOuterIpCnt": 2, "dstOuterMac": ["08:00:27:ae:4d:62", "08:00:27:f2:1d:8c"], "dstOuterMac-cnt": 2, "dstOuterOui": ["PCS Computer Systems GmbH"], "dstOuterOuiCnt": 1, "dstOuterRIR": ["ARIN", "ARIN"], "dstRIR": "TEST", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1467818432676, "icmp": {"code": [0], "type": [0, 8]}, "ipProtocol": 1, "lastPacket": 1467818435680, "length": 3004, "network": {"bytes": 1184, "community_id": "1:YcMyyWJfhc95EW1GfXt6jlZ3DiQ=", "packets": 8}, "node": "test", "packetLen": [164, 164, 164, 164, 164, 164, 164, 164], "packetPos": [240, 404, 568, 732, 896, 1060, 1224, 1388], "protocol": ["icmp", "vxlan"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 256}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 592, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["ba:09:2b:6e:f8:be"], "mac-cnt": 1, "packets": 4, "port": 0}, "srcOuterASN": ["---", "---"], "srcOuterGEO": ["---", "---"], "srcOuterIp": ["*************", "*************"], "srcOuterIpCnt": 2, "srcOuterMac": ["08:00:27:ae:4d:62", "08:00:27:f2:1d:8c"], "srcOuterMac-cnt": 2, "srcOuterOui": ["PCS Computer Systems GmbH"], "srcOuterOuiCnt": 1, "srcOuterRIR": ["ARIN", "ARIN"], "srcTTL": [64], "srcTTLCnt": 1, "tags": ["dstip", "srcip"], "tagsCnt": 2, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [33554442], "string.snow": ["16777226:0,33554442:0"]}, "totDataBytes": 512, "vni": [123], "vniCnt": 1}, "header": {"index": {"_index": "tests_sessions3-160706"}}}]}