{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 28}, "destination": {"as": {"full": "AS15169 Google LLC", "number": 15169, "organization": {"name": "Google LLC"}}, "bytes": 0, "geo": {"country_iso_code": "US"}, "ip": "*******", "mac": ["00:19:e2:ba:2f:c1"], "mac-cnt": 1, "packets": 0, "port": 53}, "dns": [{"opcode": "QUERY", "qc": "IN", "qt": "DNSKEY", "queryHost": "<root>"}], "dnsCnt": 1, "dstOui": ["Juniper Networks"], "dstOuiCnt": 1, "dstRIR": "ARIN", "ethertype": 2048, "fileId": [], "firstPacket": 1393428477365, "ipProtocol": 17, "lastPacket": 1393428477365, "length": 0, "network": {"bytes": 74, "community_id": "1:hqixyKnikIIf2iYrE//kB+ft+lc=", "packets": 1, "vlan": {"id": [500], "id-cnt": 1}}, "node": "test", "packetLen": [90], "packetPos": [24], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 74, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:1a:e3:dc:2e:c0"], "mac-cnt": 1, "packets": 1, "port": 53869}, "srcOui": ["Cisco Systems, Inc"], "srcOuiCnt": 1, "srcPayload8": "f376011000010000", "srcTTL": [61], "srcTTLCnt": 1, "tags": ["srcip"], "tagsCnt": 1, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [134744072], "string.snow": ["16777226:53869,134744072:53"]}, "totDataBytes": 28}, "header": {"index": {"_index": "tests_sessions3-140226"}}}]}