{"sessions3": [{"body": {"@timestamp": "SET", "cert": [{"hash": "db:c7:e9:0b:0d:a5:d8:8a:55:35:43:0e:eb:66:5d:07:78:59:e8:e8", "issuerCN": ["digicert high assurance ev root ca"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1636502400000, "notBefore": 1194609600000, "publicAlgorithm": "rsaEncryption", "remainingDays": 2906, "remainingSeconds": 251092127, "serial": "0337b928347c60a6aec5adb1217f3860", "subjectCN": ["digicert high assurance ev ca-1"], "subjectON": ["DigiCert Inc"], "subjectOU": ["www.digicert.com"], "validDays": 5114, "validSeconds": 441892800}, {"alt": ["github.com", "www.github.com"], "altCnt": 2, "hash": "d7:12:e9:69:65:dc:f2:36:c8:74:c7:03:7d:c0:b2:24:a9:3b:d2:33", "issuerCN": ["digicert high assurance ev ca-1"], "issuerON": ["DigiCert Inc"], "issuerOU": ["www.digicert.com"], "notAfter": 1441195200000, "notBefore": 1370822400000, "publicAlgorithm": "rsaEncryption", "remainingDays": 645, "remainingSeconds": 55784927, "serial": "047fbe2e4bde0084d2caf8e3ecfe7058", "subjectCN": ["github.com"], "subjectON": ["GitHub, Inc."], "validDays": 814, "validSeconds": 70372800}], "certCnt": 2, "client": {"bytes": 613}, "destination": {"as": {"full": "AS36459 GitHub, Inc.", "number": 36459, "organization": {"name": "GitHub, Inc."}}, "bytes": 4806, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:00:0c:07:ac:01", "00:d0:2b:d1:76:00"], "mac-cnt": 2, "packets": 9, "port": 443}, "dstOui": ["Cisco Systems, Inc", "Jetcell, Inc."], "dstOuiCnt": 2, "dstPayload8": "1603010051020000", "dstRIR": "ARIN", "dstTTL": [50], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1385410273592, "initRTT": 20, "ipProtocol": 6, "lastPacket": 1385410274128, "length": 536, "network": {"bytes": 6319, "community_id": "1:f4vOitD+00jrDP6RcE/AYEBZAAQ=", "packets": 23}, "node": "test", "packetLen": [94, 90, 82, 212, 1506, 1506, 971, 82, 82, 396, 129, 82, 224, 475, 82, 109, 82, 109, 82, 82, 70, 70, 70], "packetPos": [24, 118, 208, 290, 502, 2008, 3514, 4485, 4567, 4649, 5045, 5174, 5256, 5480, 5955, 6037, 6146, 6228, 6337, 6419, 6501, 6571, 6641], "protocol": ["tcp", "tls"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 4204}, "source": {"bytes": 1513, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 14, "port": 50754}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "8080010301005700", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["cert:certificate-authority"], "tagsCnt": 1, "tcpflags": {"ack": 8, "dstZero": 0, "fin": 2, "psh": 8, "rst": 3, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 3565031166}, "tls": {"cipher": ["TLS_RSA_WITH_RC4_128_SHA"], "cipherCnt": 1, "dstSessionId": ["4fc128aa12f6c10f1b6f72c0d4447366fe600b41efff60c865e496ed7f838ba6"], "ja3s": ["280ca4511bfaa384b2e931c058e8816e"], "ja3sCnt": 1, "ja3sstring": ["769,5,65281"], "ja3sstringCnt": 1, "version": ["TLSv1"], "versionCnt": 1}, "totDataBytes": 4817}, "header": {"index": {"_index": "tests_sessions3-131125"}}}]}