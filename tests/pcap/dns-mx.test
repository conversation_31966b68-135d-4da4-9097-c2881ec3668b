{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 24}, "destination": {"bytes": 216, "ip": "**********", "mac": ["00:00:0c:07:ac:01", "00:d0:2b:d1:76:00"], "mac-cnt": 2, "packets": 1, "port": 53}, "dns": [{"answers": [{"class": "IN", "mx": "cluster5.us.messagelabs.com", "name": "mx.com", "priority": 10, "ttl": 3600, "type": "MX"}, {"class": "IN", "mx": "cluster5a.us.messagelabs.com", "name": "mx.com", "priority": 20, "ttl": 3600, "type": "MX"}, {"class": "IN", "name": "mx.com", "nameserver": "dns2.stabletransit.com", "ttl": 3600, "type": "NS"}, {"class": "IN", "name": "mx.com", "nameserver": "dns1.stabletransit.com", "ttl": 3600, "type": "NS"}, {"class": "IN", "ip": "***********", "name": "dns2.stabletransit.com", "ttl": 1302, "type": "A"}, {"class": "IN", "ip": "**********", "name": "dns1.stabletransit.com", "ttl": 3559, "type": "A"}], "answersCnt": 6, "headerFlags": ["RA", "RD"], "host": ["mx.com"], "hostCnt": 1, "mailserverHost": ["cluster5.us.messagelabs.com", "cluster5a.us.messagelabs.com"], "mailserverHostCnt": 2, "nameserverASN": ["AS15395 Rackspace Ltd.", "AS27357 Rackspace Hosting"], "nameserverGEO": ["US", "US"], "nameserverHost": ["dns1.stabletransit.com", "dns2.stabletransit.com"], "nameserverHostCnt": 2, "nameserverIp": ["***********", "**********"], "nameserverIpCnt": 2, "nameserverRIR": ["ARIN", "ARIN"], "opcode": "QUERY", "qc": "IN", "qt": "MX", "queryHost": "mx.com", "status": "NOERROR"}], "dnsCnt": 1, "dstOui": ["Cisco Systems, Inc", "Jetcell, Inc."], "dstOuiCnt": 2, "dstPayload8": "4c17818000010002", "dstTTL": [60], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386104996973, "ipProtocol": 17, "lastPacket": 1386104997055, "length": 83, "network": {"bytes": 282, "community_id": "1:ba6AVEG/hzl+V9BnSJZnWVTUR6U=", "packets": 2}, "node": "test", "packetLen": [82, 232], "packetPos": [24, 106], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 174}, "source": {"bytes": 66, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 1, "port": 51427}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "4c17010000010000", "srcTTL": [64], "srcTTLCnt": 1, "totDataBytes": 198}, "header": {"index": {"_index": "tests_sessions3-131203"}}}]}