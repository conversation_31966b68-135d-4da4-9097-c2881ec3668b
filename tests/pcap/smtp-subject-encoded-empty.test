{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 350}, "destination": {"as": {"full": "AS1 Cool Beans!", "number": 1, "organization": {"name": "Cool Beans!"}}, "bytes": 1688, "geo": {"country_iso_code": "CA"}, "ip": "********", "mac": ["00:00:0c:07:ac:01", "00:d0:2b:d1:76:00"], "mac-cnt": 2, "packets": 14, "port": 25}, "dstOui": ["Cisco Systems, Inc", "Jetcell, Inc."], "dstOuiCnt": 2, "dstPayload8": "3232302d78787878", "dstRIR": "TEST", "dstTTL": [50], "dstTTLCnt": 1, "email": {"dst": ["<EMAIL>"], "dstCnt": 1, "header": ["from", "subject", "to"], "headerCnt": 3, "smtpHello": ["xxxxxxxxxxxxx.xxx.com"], "smtpHelloCnt": 1, "src": ["<EMAIL>"], "srcCnt": 1, "subject": ["Can this 10 Second Trick Help Prevent YOUR Heart Attack?"], "subjectCnt": 1}, "ethertype": 2048, "fileId": [], "firstPacket": 1398431422481, "initRTT": 1, "ipProtocol": 6, "lastPacket": 1398431453159, "length": 30678, "network": {"bytes": 3304, "community_id": "1:0o5odgrjHPw4qdrjG2+biLbiEoY=", "packets": 33}, "node": "test", "packetLen": [94, 90, 82, 391, 82, 110, 82, 290, 82, 109, 122, 82, 107, 125, 82, 88, 132, 82, 103, 82, 316, 82, 85, 82, 138, 82, 88, 132, 82, 82, 82, 82, 82], "packetPos": [24, 118, 208, 290, 681, 763, 873, 955, 1245, 1327, 1436, 1558, 1640, 1747, 1872, 1954, 2042, 2174, 2256, 2359, 2441, 2757, 2839, 2924, 3006, 3144, 3226, 3314, 3446, 3528, 3610, 3692, 3774], "protocol": ["smtp", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 756}, "source": {"as": {"full": "AS0 This is neat", "number": 0, "organization": {"name": "This is neat"}}, "bytes": 1616, "geo": {"country_iso_code": "RU"}, "ip": "********", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 19, "port": 62855}, "srcDscp": [4], "srcDscpCnt": 1, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "45484c4f20787878", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["dstip", "smtp:statuscode:250", "srcip"], "tagsCnt": 3, "tcpflags": {"ack": 14, "dstZero": 0, "fin": 2, "psh": 15, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 3620201231}, "test": {"ASN": ["AS0 This is neat"], "GEO": ["RU"], "RIR": [""], "ip": ["********"], "number": [33554442], "string.snow": ["16777226:62855,33554442:25"]}, "totDataBytes": 1106}, "header": {"index": {"_index": "tests_sessions3-140425"}}}]}