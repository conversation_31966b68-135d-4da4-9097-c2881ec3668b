{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 4083}, "destination": {"as": {"full": "AS4816 China Telecom (Group)", "number": 4816, "organization": {"name": "China Telecom (Group)"}}, "bytes": 5069, "geo": {"country_iso_code": "CN"}, "ip": "************", "packets": 9, "port": 80}, "dstPayload8": "485454502f312e31", "dstRIR": "APNIC", "dstTTL": [64], "dstTTLCnt": 1, "fileId": [], "firstPacket": 1523241953739, "http": {"bodyMagic": ["application/x-gzip", "text/html"], "bodyMagicCnt": 2, "clientVersion": ["1.1"], "clientVersionCnt": 1, "cookieKey": ["_ga", "fontsize", "pac_uid", "pgv_pvid"], "cookieKeyCnt": 4, "cookieValue": ["0_5a9d0be2ebd90", "9816415136", "GA1.2.1343945816.1520241679", "size_n"], "cookieValueCnt": 4, "host": ["qq.com"], "hostCnt": 1, "md5": ["1546e06a87abc6e183260c970e0c3a46", "1f7b84b70902489a0eb663ce31b8c58a", "a4a7b8b5eaf4c2cf356b1052133c6cdb", "c90e0392fdda6b9091a886229c14bf66"], "md5Cnt": 4, "method": ["GET", "POST"], "method-GET": 5, "method-POST": 2, "methodCnt": 2, "path": ["/", "/favicon.ico", "/post.php", "/static/css/base.css", "/static/css/index.css", "/static/images/banner.jpg"], "pathCnt": 6, "request-referer": ["http://qq.com/", "http://qq.com/static/css/index.css"], "request-refererCnt": 2, "requestBody": ["Username=Q&Passwd=B&Section=A", "Username=Qggh&Passwd=Bhhhh&Section=Auuu"], "requestHeader": ["accept", "accept-encoding", "accept-language", "cache-control", "connection", "content-length", "content-type", "cookie", "host", "if-modified-since", "if-none-match", "origin", "referer", "upgrade-insecure-requests", "user-agent"], "requestHeaderCnt": 15, "requestHeaderField": ["accept", "accept", "accept", "accept", "accept", "accept", "accept", "accept-encoding", "accept-encoding", "accept-encoding", "accept-encoding", "accept-encoding", "accept-encoding", "accept-encoding", "accept-language", "accept-language", "accept-language", "accept-language", "accept-language", "accept-language", "accept-language", "cache-control", "cache-control", "connection", "connection", "connection", "connection", "connection", "connection", "connection", "content-length", "content-length", "content-type", "content-type", "cookie", "cookie", "cookie", "cookie", "cookie", "cookie", "cookie", "if-modified-since", "if-modified-since", "if-modified-since", "if-modified-since", "if-modified-since", "if-none-match", "if-none-match", "if-none-match", "if-none-match", "if-none-match", "origin", "origin", "upgrade-insecure-requests", "upgrade-insecure-requests", "upgrade-insecure-requests"], "requestHeaderValue": ["\"59534776-2621\"", "\"59534776-41a8e\"", "\"59534819-630\"", "*/*", "1", "1", "1", "29", "39", "application/x-www-form-urlencoded", "application/x-www-form-urlencoded", "en-us,en;q=0.8", "en-us,en;q=0.8", "en-us,en;q=0.8", "en-us,en;q=0.8", "en-us,en;q=0.8", "en-us,en;q=0.8", "en-us,en;q=0.8", "gzip, deflate", "gzip, deflate", "gzip, deflate, sdch", "gzip, deflate, sdch", "gzip, deflate, sdch", "gzip, deflate, sdch", "gzip, deflate, sdch", "http://qq.com", "http://qq.com", "image/webp,*/*;q=0.8", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "max-age=0", "max-age=0", "pac_uid=0_5a9d0be2ebd90; _ga=ga1.2.1343945816.1520241679; fontsize=size_n; pgv_pvid=9816415136", "pac_uid=0_5a9d0be2ebd90; _ga=ga1.2.1343945816.1520241679; fontsize=size_n; pgv_pvid=9816415136", "pac_uid=0_5a9d0be2ebd90; _ga=ga1.2.1343945816.1520241679; fontsize=size_n; pgv_pvid=9816415136", "pac_uid=0_5a9d0be2ebd90; _ga=ga1.2.1343945816.1520241679; fontsize=size_n; pgv_pvid=9816415136", "pac_uid=0_5a9d0be2ebd90; _ga=ga1.2.1343945816.1520241679; fontsize=size_n; pgv_pvid=9816415136", "pac_uid=0_5a9d0be2ebd90; _ga=ga1.2.1343945816.1520241679; fontsize=size_n; pgv_pvid=9816415136", "pac_uid=0_5a9d0be2ebd90; _ga=ga1.2.1343945816.1520241679; fontsize=size_n; pgv_pvid=9816415136", "text/css,*/*;q=0.1", "text/css,*/*;q=0.1", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "w/\"5aa960fe-1459\"", "w/\"5aa960fe-1459\"", "wed, 14 mar 2018 17:50:54 gmt", "wed, 14 mar 2018 17:50:54 gmt", "wed, 28 jun 2017 06:06:46 gmt", "wed, 28 jun 2017 06:06:46 gmt", "wed, 28 jun 2017 06:09:29 gmt"], "requestHeaderValueCnt": 56, "responseHeader": ["connection", "content-encoding", "content-length", "content-type", "date", "etag", "last-modified", "server", "transfer-encoding"], "responseHeaderCnt": 9, "responseHeaderField": ["connection", "connection", "connection", "connection", "connection", "connection", "connection", "content-encoding", "content-encoding", "content-length", "content-length", "content-type", "content-type", "content-type", "content-type", "date", "date", "date", "date", "date", "date", "date", "etag", "etag", "etag", "etag", "etag", "last-modified", "last-modified", "last-modified", "last-modified", "last-modified", "server", "server", "server", "server", "server", "server", "server", "transfer-encoding", "transfer-encoding"], "responseHeaderValue": ["\"59534776-2621\"", "\"59534776-41a8e\"", "\"59534819-630\"", "575", "575", "chunked", "chunked", "gzip", "gzip", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "keep-alive", "mon, 09 apr 2018 02:45:53 gmt", "mon, 09 apr 2018 02:45:54 gmt", "mon, 09 apr 2018 02:45:54 gmt", "mon, 09 apr 2018 02:45:54 gmt", "mon, 09 apr 2018 02:45:54 gmt", "mon, 09 apr 2018 02:46:15 gmt", "mon, 09 apr 2018 02:46:24 gmt", "nginx/1.13.3", "nginx/1.13.3", "nginx/1.13.3", "nginx/1.13.3", "nginx/1.13.3", "nginx/1.13.3", "nginx/1.13.3", "text/html", "text/html", "text/html", "text/html", "w/\"5a9f7f28-5c6\"", "w/\"5a9f7f28-5c6\"", "wed, 07 mar 2018 05:56:56 gmt", "wed, 07 mar 2018 05:56:56 gmt", "wed, 28 jun 2017 06:06:46 gmt", "wed, 28 jun 2017 06:06:46 gmt", "wed, 28 jun 2017 06:09:29 gmt"], "responseHeaderValueCnt": 41, "serverVersion": ["1.1"], "serverVersionCnt": 1, "sha256": ["7877f668f7e4d55d477a74b3c28b1356edba16c99c0891d95286f31436f6101d", "87b94c6c763d2ae6e47cfb9c35e6d54d8a84bf125cbc9430082a9f1b3e592bec", "cf99a90815d23b4706f8ab84be471da819091ada6c659144af0fbe471049cf06", "fd2e303330f2cf92a9a4b32425eb42b199ebf9b4604f69f467835a4a271b0fcd"], "sha256Cnt": 4, "statuscode": [200, 304, 405], "statuscodeCnt": 3, "uri": ["qq.com/", "qq.com/favicon.ico", "qq.com/post.php", "qq.com/static/css/base.css", "qq.com/static/css/index.css", "qq.com/static/images/banner.jpg"], "uriCnt": 6, "useragent": ["Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5 Build/M4B30Z) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.133 Mobile Safari/537.36"], "useragentCnt": 1}, "initRTT": 5, "ipProtocol": 6, "lastPacket": 1523241984047, "length": 30309, "network": {"bytes": 11832, "community_id": "1:CtpkM8732ncl+urC+3Y3WY9cQPk=", "packets": 26}, "node": "test", "packetLen": [148, 148, 140, 720, 140, 1109, 140, 676, 321, 676, 320, 140, 704, 704, 322, 140, 140, 655, 1109, 140, 811, 872, 140, 821, 872, 140], "packetPos": [24, 172, 320, 460, 1180, 1320, 2429, 2569, 3245, 3566, 4242, 4562, 4702, 5406, 6110, 6432, 6572, 6712, 7367, 8476, 8616, 9427, 10299, 10439, 11260, 12132], "protocol": ["http", "tcp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 3945}, "source": {"as": {"full": "AS7018 AT&T Services, Inc.", "number": 7018, "organization": {"name": "AT&T Services, Inc."}}, "bytes": 6763, "geo": {"country_iso_code": "US"}, "ip": "***********", "packets": 17, "port": 49417}, "srcPayload8": "474554202f204854", "srcRIR": "ARIN", "srcTTL": [64], "srcTTLCnt": 1, "tags": ["http:password"], "tagsCnt": 1, "tcpflags": {"ack": 9, "dstZero": 0, "fin": 0, "psh": 15, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 2524188118}, "totDataBytes": 8028}, "header": {"index": {"_index": "tests_sessions3-180409"}}}]}