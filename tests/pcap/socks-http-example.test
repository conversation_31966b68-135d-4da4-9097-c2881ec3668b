{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 155}, "destination": {"bytes": 2003, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 6, "port": 1080}, "dstOui": ["Dell Inc."], "dstOuiCnt": 1, "dstPayload8": "005adfb20ab49cf9", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386004309468, "http": {"bodyMagic": ["text/html"], "bodyMagicCnt": 1, "clientVersion": ["1.1"], "clientVersionCnt": 1, "host": ["www.example.com"], "hostCnt": 1, "md5": ["09b9c392dc1f6e914cea287cb6be34b0"], "md5Cnt": 1, "method": ["GET"], "method-GET": 1, "methodCnt": 1, "path": ["/"], "pathCnt": 1, "requestHeader": ["accept", "host", "user-agent"], "requestHeaderCnt": 3, "requestHeaderField": ["accept"], "requestHeaderValue": ["*/*"], "requestHeaderValueCnt": 1, "responseHeader": ["accept-ranges", "cache-control", "content-length", "content-type", "date", "etag", "expires", "last-modified", "server", "x-cache", "x-ec-custom-error"], "responseHeaderCnt": 11, "responseHeaderField": ["accept-ranges", "cache-control", "content-length", "content-type", "date", "etag", "expires", "last-modified", "server", "x-cache", "x-ec-custom-error"], "responseHeaderValue": ["\"359670651\"", "1", "1270", "bytes", "ecs (iad/1984)", "fri, 09 aug 2013 23:54:35 gmt", "hit", "max-age=604800", "mon, 02 dec 2013 17:11:46 gmt", "mon, 09 dec 2013 17:11:46 gmt", "text/html"], "responseHeaderValueCnt": 11, "serverVersion": ["1.1"], "serverVersionCnt": 1, "sha256": ["3587cb776ce0e4e8237f215800b7dffba0f25865cb84550e87ea8bbac838c423"], "sha256Cnt": 1, "statuscode": [200], "statuscodeCnt": 1, "uri": ["www.example.com/"], "uriCnt": 1, "useragent": ["curl/7.24.0 (x86_64-apple-darwin12.0) libcurl/7.24.0 OpenSSL/0.9.8y zlib/1.2.5"], "useragentCnt": 1}, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1386004309478, "length": 10, "network": {"bytes": 2698, "community_id": "1:jOv8gtYKkFiC4/5gDoVA6WtkDQA=", "packets": 14}, "node": "test", "packetLen": [94, 90, 82, 91, 82, 90, 82, 228, 1530, 225, 82, 82, 82, 82], "packetPos": [24, 118, 208, 290, 381, 463, 553, 635, 863, 2393, 2618, 2700, 2782, 2864], "protocol": ["http", "socks", "tcp"], "protocolCnt": 3, "segmentCnt": 1, "server": {"bytes": 1599}, "socks": {"ASN": "AS15133 MCI Communications Services, Inc. d/b/a Verizon Business", "GEO": "US", "RIR": "RIPE", "ip": "**************", "port": 80}, "source": {"bytes": 695, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 8, "port": 53533}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "040100505db8d877", "srcTTL": [64], "srcTTLCnt": 1, "tcpflags": {"ack": 6, "dstZero": 0, "fin": 2, "psh": 4, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 2934517489}, "totDataBytes": 1754}, "header": {"index": {"_index": "tests_sessions3-131202"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 171}, "destination": {"bytes": 2069, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 7, "port": 1080}, "dstOui": ["Dell Inc."], "dstOuiCnt": 1, "dstPayload8": "005adfb30ab49cf9", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386004312331, "http": {"bodyMagic": ["text/html"], "bodyMagicCnt": 1, "clientVersion": ["1.1"], "clientVersionCnt": 1, "host": ["www.example.com"], "hostCnt": 1, "md5": ["09b9c392dc1f6e914cea287cb6be34b0"], "md5Cnt": 1, "method": ["GET"], "method-GET": 1, "methodCnt": 1, "path": ["/"], "pathCnt": 1, "requestHeader": ["accept", "host", "user-agent"], "requestHeaderCnt": 3, "requestHeaderField": ["accept"], "requestHeaderValue": ["*/*"], "requestHeaderValueCnt": 1, "responseHeader": ["accept-ranges", "cache-control", "content-length", "content-type", "date", "etag", "expires", "last-modified", "server", "x-cache", "x-ec-custom-error"], "responseHeaderCnt": 11, "responseHeaderField": ["accept-ranges", "cache-control", "content-length", "content-type", "date", "etag", "expires", "last-modified", "server", "x-cache", "x-ec-custom-error"], "responseHeaderValue": ["\"359670651\"", "1", "1270", "bytes", "ecs (iad/1984)", "fri, 09 aug 2013 23:54:35 gmt", "hit", "max-age=604800", "mon, 02 dec 2013 17:11:49 gmt", "mon, 09 dec 2013 17:11:49 gmt", "text/html"], "responseHeaderValueCnt": 11, "serverVersion": ["1.1"], "serverVersionCnt": 1, "sha256": ["3587cb776ce0e4e8237f215800b7dffba0f25865cb84550e87ea8bbac838c423"], "sha256Cnt": 1, "statuscode": [200], "statuscodeCnt": 1, "uri": ["www.example.com/"], "uriCnt": 1, "useragent": ["curl/7.24.0 (x86_64-apple-darwin12.0) libcurl/7.24.0 OpenSSL/0.9.8y zlib/1.2.5"], "useragentCnt": 1}, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1386004312384, "length": 53, "network": {"bytes": 2780, "community_id": "1:7mIgLO3MM/1td+keMk5CvXMEWiI=", "packets": 15}, "node": "test", "packetLen": [94, 90, 82, 107, 82, 90, 82, 228, 82, 1530, 225, 82, 82, 82, 82], "packetPos": [2946, 3040, 3130, 3212, 3319, 3401, 3491, 3573, 3801, 3883, 5413, 5638, 5720, 5802, 5884], "protocol": ["http", "socks", "tcp"], "protocolCnt": 3, "segmentCnt": 1, "server": {"bytes": 1599}, "socks": {"host": "www.example.com", "port": 80}, "source": {"bytes": 711, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 8, "port": 53534}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "0401005000000001", "srcTTL": [64], "srcTTLCnt": 1, "tcpflags": {"ack": 7, "dstZero": 0, "fin": 2, "psh": 4, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 3112233030}, "totDataBytes": 1770}, "header": {"index": {"_index": "tests_sessions3-131202"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 160}, "destination": {"bytes": 2073, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:13:72:c4:f1:e1"], "mac-cnt": 1, "packets": 7, "port": 1080}, "dstOui": ["Dell Inc."], "dstOuiCnt": 1, "dstPayload8": "0500050000010ab4", "dstTTL": [64], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1386004317979, "http": {"bodyMagic": ["text/html"], "bodyMagicCnt": 1, "clientVersion": ["1.1"], "clientVersionCnt": 1, "host": ["www.example.com"], "hostCnt": 1, "md5": ["09b9c392dc1f6e914cea287cb6be34b0"], "md5Cnt": 1, "method": ["GET"], "method-GET": 1, "methodCnt": 1, "path": ["/"], "pathCnt": 1, "requestHeader": ["accept", "host", "user-agent"], "requestHeaderCnt": 3, "requestHeaderField": ["accept"], "requestHeaderValue": ["*/*"], "requestHeaderValueCnt": 1, "responseHeader": ["accept-ranges", "cache-control", "content-length", "content-type", "date", "etag", "expires", "last-modified", "server", "x-cache", "x-ec-custom-error"], "responseHeaderCnt": 11, "responseHeaderField": ["accept-ranges", "cache-control", "content-length", "content-type", "date", "etag", "expires", "last-modified", "server", "x-cache", "x-ec-custom-error"], "responseHeaderValue": ["\"359670651\"", "1", "1270", "bytes", "ecs (iad/1984)", "fri, 09 aug 2013 23:54:35 gmt", "hit", "max-age=604800", "mon, 02 dec 2013 17:11:55 gmt", "mon, 09 dec 2013 17:11:55 gmt", "text/html"], "responseHeaderValueCnt": 11, "serverVersion": ["1.1"], "serverVersionCnt": 1, "sha256": ["3587cb776ce0e4e8237f215800b7dffba0f25865cb84550e87ea8bbac838c423"], "sha256Cnt": 1, "statuscode": [200], "statuscodeCnt": 1, "uri": ["www.example.com/"], "uriCnt": 1, "useragent": ["curl/7.24.0 (x86_64-apple-darwin12.0) libcurl/7.24.0 OpenSSL/0.9.8y zlib/1.2.5"], "useragentCnt": 1}, "initRTT": 0, "ipProtocol": 6, "lastPacket": 1386004317989, "length": 9, "network": {"bytes": 2905, "community_id": "1:Xad2GdfmsgABdCGXU/fkZWG9Kpg=", "packets": 17}, "node": "test", "packetLen": [94, 90, 82, 86, 82, 84, 82, 92, 92, 82, 228, 1530, 225, 82, 82, 82, 82], "packetPos": [5966, 6060, 6150, 6232, 6318, 6400, 6484, 6566, 6658, 6750, 6832, 7060, 8590, 8815, 8897, 8979, 9061], "protocol": ["http", "socks", "tcp"], "protocolCnt": 3, "segmentCnt": 1, "server": {"bytes": 1603}, "socks": {"ASN": "AS15133 MCI Communications Services, Inc. d/b/a Verizon Business", "GEO": "US", "RIR": "RIPE", "ip": "**************", "port": 80}, "source": {"bytes": 832, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 10, "port": 53535}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "0502000105010001", "srcTTL": [64], "srcTTLCnt": 1, "tcpflags": {"ack": 7, "dstZero": 0, "fin": 2, "psh": 6, "rst": 0, "srcZero": 0, "syn": 1, "syn-ack": 1, "urg": 0}, "tcpseq": {"dst": 0, "src": 992474166}, "totDataBytes": 1763}, "header": {"index": {"_index": "tests_sessions3-131202"}}}]}