{"sessions3": [{"body": {"@timestamp": "SET", "client": {"bytes": 32}, "destination": {"bytes": 254, "ip": "**********", "mac": ["00:00:0c:07:ac:01", "00:d0:2b:d1:76:00"], "mac-cnt": 2, "packets": 1, "port": 53}, "dns": [{"ASN": ["AS36459 GitHub, Inc."], "GEO": ["US"], "RIR": ["ARIN"], "answers": [{"class": "IN", "cname": "github.com", "name": "www.github.com", "ttl": 1980, "type": "CNAME"}, {"class": "IN", "ip": "**************", "name": "github.com", "ttl": 4, "type": "A"}, {"class": "IN", "name": "github.com", "nameserver": "ns3.p16.dynect.net", "ttl": 328, "type": "NS"}, {"class": "IN", "name": "github.com", "nameserver": "ns2.p16.dynect.net", "ttl": 328, "type": "NS"}, {"class": "IN", "name": "github.com", "nameserver": "ns4.p16.dynect.net", "ttl": 328, "type": "NS"}, {"class": "IN", "name": "github.com", "nameserver": "ns1.p16.dynect.net", "ttl": 328, "type": "NS"}, {"class": "IN", "ip": "**********", "name": "ns2.p16.dynect.net", "ttl": 3600, "type": "A"}, {"class": "IN", "ip": "**********", "name": "ns3.p16.dynect.net", "ttl": 3600, "type": "A"}, {"class": "IN", "ip": "**********", "name": "ns1.p16.dynect.net", "ttl": 3600, "type": "A"}, {"class": "IN", "ip": "**********", "name": "ns4.p16.dynect.net", "ttl": 3600, "type": "A"}], "answersCnt": 10, "headerFlags": ["RA", "RD"], "host": ["github.com", "www.github.com"], "hostCnt": 2, "ip": ["**************"], "ipCnt": 1, "nameserverASN": ["---"], "nameserverGEO": ["---"], "nameserverHost": ["ns1.p16.dynect.net", "ns2.p16.dynect.net", "ns3.p16.dynect.net", "ns4.p16.dynect.net"], "nameserverHostCnt": 4, "nameserverIp": ["**********"], "nameserverIpCnt": 1, "nameserverRIR": [""], "opcode": "QUERY", "qc": "IN", "qt": "A", "queryHost": "www.github.com", "status": "NOERROR"}], "dnsCnt": 1, "dstOui": ["Cisco Systems, Inc", "Jetcell, Inc."], "dstOuiCnt": 2, "dstPayload8": "e039818000010002", "dstTTL": [60], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1385400647217, "ipProtocol": 17, "lastPacket": 1385400647218, "length": 0, "network": {"bytes": 328, "community_id": "1:GTsD3mpAYuXO8KLDJlk5/Mpw/Wc=", "packets": 2}, "node": "test", "packetLen": [90, 270], "packetPos": [24, 114], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 212}, "source": {"bytes": 74, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 1, "port": 62563}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "e039010000010000", "srcTTL": [64], "srcTTLCnt": 1, "totDataBytes": 244}, "header": {"index": {"_index": "tests_sessions3-131125"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 32}, "destination": {"bytes": 254, "ip": "***********", "mac": ["00:00:0c:07:ac:01", "00:0e:d6:0b:98:80"], "mac-cnt": 2, "packets": 1, "port": 53}, "dns": [{"ASN": ["AS36459 GitHub, Inc."], "GEO": ["US"], "RIR": ["ARIN"], "answers": [{"class": "IN", "cname": "github.com", "name": "www.github.com", "ttl": 3600, "type": "CNAME"}, {"class": "IN", "ip": "**************", "name": "github.com", "ttl": 30, "type": "A"}, {"class": "IN", "name": "github.com", "nameserver": "ns2.p16.dynect.net", "ttl": 327, "type": "NS"}, {"class": "IN", "name": "github.com", "nameserver": "ns3.p16.dynect.net", "ttl": 327, "type": "NS"}, {"class": "IN", "name": "github.com", "nameserver": "ns4.p16.dynect.net", "ttl": 327, "type": "NS"}, {"class": "IN", "name": "github.com", "nameserver": "ns1.p16.dynect.net", "ttl": 327, "type": "NS"}, {"class": "IN", "ip": "**********", "name": "ns1.p16.dynect.net", "ttl": 3600, "type": "A"}, {"class": "IN", "ip": "**********", "name": "ns3.p16.dynect.net", "ttl": 3600, "type": "A"}, {"class": "IN", "ip": "**********", "name": "ns4.p16.dynect.net", "ttl": 3600, "type": "A"}, {"class": "IN", "ip": "**********", "name": "ns2.p16.dynect.net", "ttl": 3600, "type": "A"}], "answersCnt": 10, "headerFlags": ["RA", "RD"], "host": ["github.com", "www.github.com"], "hostCnt": 2, "ip": ["**************"], "ipCnt": 1, "nameserverASN": ["---"], "nameserverGEO": ["---"], "nameserverHost": ["ns1.p16.dynect.net", "ns2.p16.dynect.net", "ns3.p16.dynect.net", "ns4.p16.dynect.net"], "nameserverHostCnt": 4, "nameserverIp": ["**********"], "nameserverIpCnt": 1, "nameserverRIR": [""], "opcode": "QUERY", "qc": "IN", "qt": "A", "queryHost": "www.github.com", "status": "NOERROR"}], "dnsCnt": 1, "dstOui": ["Cisco Systems, Inc"], "dstOuiCnt": 1, "dstPayload8": "e039818000010002", "dstTTL": [60], "dstTTLCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1385400648218, "ipProtocol": 17, "lastPacket": 1385400648228, "length": 9, "network": {"bytes": 328, "community_id": "1:G0XeVsW8anRO3xlyUoJSRZQKxkk=", "packets": 2}, "node": "test", "packetLen": [90, 270], "packetPos": [384, 474], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 212}, "source": {"bytes": 74, "geo": {"country_iso_code": "US"}, "ip": "**************", "mac": ["00:1f:5b:ff:51:cb"], "mac-cnt": 1, "packets": 1, "port": 62416}, "srcOui": ["Apple, Inc."], "srcOuiCnt": 1, "srcPayload8": "e039010000010000", "srcTTL": [64], "srcTTLCnt": 1, "totDataBytes": 244}, "header": {"index": {"_index": "tests_sessions3-131125"}}}, {"body": {"@timestamp": "SET", "client": {"bytes": 54}, "destination": {"bytes": 0, "ip": "***********", "mac": ["20:4e:71:c5:11:c0"], "mac-cnt": 1, "packets": 0, "port": 53}, "dns": [{"host": ["************.in-addr.arpa"], "hostCnt": 1, "opcode": "QUERY", "qc": "IN", "qt": "ANY", "queryHost": "************.in-addr.arpa"}], "dnsCnt": 1, "dstOui": ["Juniper Networks"], "dstOuiCnt": 1, "ethertype": 2048, "fileId": [], "firstPacket": 1482767159342, "ipProtocol": 17, "lastPacket": 1482767159342, "length": 0, "network": {"bytes": 96, "community_id": "1:riWwYg6ibgUkITnqzx645Ry2+Ps=", "packets": 1}, "node": "test", "packetLen": [112], "packetPos": [744], "protocol": ["dns", "udp"], "protocolCnt": 2, "segmentCnt": 1, "server": {"bytes": 0}, "source": {"bytes": 96, "ip": "***********", "mac": ["ac:4b:c8:4c:9f:c1"], "mac-cnt": 1, "packets": 1, "port": 5353}, "srcOui": ["Juniper Networks"], "srcOuiCnt": 1, "srcPayload8": "caa6001000010000", "srcTTL": [59], "srcTTLCnt": 1, "totDataBytes": 54}, "header": {"index": {"_index": "tests_sessions3-161226"}}}]}