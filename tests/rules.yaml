---
# version: 1  - Must be set
# rules:
#   name:       - Friendly name for stats

#   when:       - must be one of the three
#     sessionSetup     - first few packets
#     afterClassify    - classify stage
#     fieldSet         - check when a field is set
#     beforeMiddleSave - before doing a mid save
#     beforeFinalSave  - before doing a final save
#     beforeBothSave    - before either a mid or final save
#
#One of the following 2:
#   bpf:               - A bpf expression
#   fields:            - A list of field expressions and values for each field,
#                        one from each field expression must be set
#   <field expression1>:
#     - <value1>
#     - <vaule2>
#   <field expression2>:
#     - <value1>
#     - <vaule2>
#
#   ops:                          - List of ops, 1 per line
#     _dontSaveSPI                - Don't save SPI data for session
#     _maxPacketsToSave           - Don't save more then X packets
#     _minPacketsBeforeSavingSPI  - Don't save SPI data unless this many packets
#     <fieldname>                 - Add value to field
#     -<fieldname>                - Remove value from field

version: 1
rules:
  - name: "Don't save fields example2"
    when: "fieldSet"
    fields:
      protocols:
        - tls
      host.http:
        - www.aol.com
        - mail.yandex.com
        - foo.bar.com
      host.http,endsWith:
        - ample.com
      host.http,contains:
        - b.c
      host.http,startsWith:
        - bl.a
#      mac.src:
#        - 00:11:f5:13:d7:a3
    ops:
      "protocols": "tlsrulestest"

  - name: "socks test"
    when: "fieldSet"
    fields:
      ip.socks:
        - **************
    ops:
      "protocols": "socksipset"

  - name: "ip test"
    when: "fieldSet"
    log: true
    fields:
      ip.src:
        - ***********/24
        - 2001:06f8::/32
      port.dst:
        - 400-500
        - 5353-5354
      port.dst:
        - 5300-5400
        - 50509-50510
      protocols.cnt: 1
    ops:
      "protocols": "iprulztest"
      _maxPacketsToSave: ">100"

  - name: "0 test"
    when: "beforeFinalSave"
    fields:
      packets.src: 2
      packets.dst: 0
      databytes.src: 176
    ops:
      tags: "src-2-dst-0"

  - name: "not 1 test"
    when: "beforeFinalSave"
    fields:
      packets.src: 2
      "!packets.dst": 1
      databytes.src: 176
    ops:
      tags: "src-2-not-dst-1"

  - name: "not 0 test"
    when: "beforeFinalSave"
    fields:
      "!packets.src": 0
      "!packets.dst": 1
      databytes.src: 176
    ops:
      tags: "not-src-0-not-dst-1"

  - name: "only syn"
    when: "beforeFinalSave"
    fields:
      tcpflags.syn: ${rules_value_2}
      packets.src: 1
      packets.dst: 0
    ops:
      _dontSaveSPI: 1

  - name: "communityId"
    when: "beforeFinalSave"
    fields:
      communityId: ${rules_communityId}
    ops:
      "tags": "communityidTest"

  - name: "cert.alt"
    when: "fieldSet"
    log: true
    fields:
      cert.alt: "bad.curveballtest.com"
      communityId: "YzavRzM2u5v8TNEv4SuCDe3OgWg="
      ip.dst: **************
      ip.src: **************
      protocols.cnt: 2
    ops:
      "tags": "cert.alt test"

  - name: "not rule"
    when: "beforeFinalSave"
    fields:
      "!communityId": "FuiRMncy2mhAkCJ4mYumMiRK0uw="
      "!protocols":
        - tcp
        - http
        - dns
      ip.src: ********
      ip.dst: ********
    ops:
      "tags": "communityId not test"

  - name: "flip test"
    when: "beforeFinalSave"
    fields:
      "protocols":
        - tcp
      ip.src: *************
      ip.dst: **************
      tcp.synSet: 0
    ops:
      _flipSrcDst: 1
      tags: "flip test"
