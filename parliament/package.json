{"name": "parliament", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/arkime/arkime.git"}, "scripts": {"bundle": "cd ../ && npm run parliament:bundle", "bundle:min": "cd ../ && npm run parliament:build", "dev": "cd ../ && npm run parliament:dev", "noauth": "cd ../ && npm run parliament:noauth", "lint": "cd ../ && npm run parliament:lint"}, "engines": {"node": ">= 18.15.0 < 21", "npm": ">= 3.0.0"}, "nodemonConfig": {"ignore": ["node_modules", "vueap<PERSON>", "*.json"], "watch": [".", "../common", "../tests/parliament.ini"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}