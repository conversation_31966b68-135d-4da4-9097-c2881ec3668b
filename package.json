{"name": "arkime", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/arkime/arkime.git"}, "dependencies": {"@aws-sdk/client-s3": "^3.621.0", "@clickhouse/client": "^1.0.2", "@databricks/sql": "^1.8.4", "@elastic/elasticsearch": "7.10.0", "@skhaz/zstd": "^1.0.21", "arkime-iptrie": "^0.0.9", "arkime-notifme-sdk": "^1.11.4", "async": "^3.2.6", "axios": "^1.8.3", "better-sqlite3": "^10.1.0", "body-parser": "^1.20.3", "bson": "^1.1.5", "chalk": "^4.1.2", "compression": "^1.7.5", "connect-timeout": "^1.9.0", "console-stamp": "^3.1.2", "csv": "^6.3.11", "express": "^4.21.2", "express-session": "^1.18.1", "font-awesome": "^4.7.0", "glob": "^10.4.5", "helmet": "^3.23.3", "ioredis": "^4.28.5", "ipaddr.js": "^2.2.0", "js-ini": "^1.6.0", "js-yaml": "^4.1.0", "lmdb": "^3.1.6", "lodash.template": "^4.5.0", "lru-cache": "^4.1.5", "memjs": "^1.3.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^2.0.1", "on-headers": "^1.0.2", "openid-client": "^5.7.0", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-http": "^0.3.0", "passport-local": "^1.0.0", "peek-stream": "^1.1.3", "pngjs": "^7.0.0", "pug": "^3.0.3", "re2": "^1.21.4", "serve-favicon": "^2.5.0", "splunk-sdk": "^1.12.1", "stylus": "^0.64.0", "through2": "^4.0.2", "unzipper": "^0.10.11", "uuid": "^9.0.1", "vue": "^2.6.12", "vue-server-renderer": "^2.6.12"}, "scripts": {"lint": "eslint --ext .js,.vue parliament viewer wiseService capture/plugins common cont3xt", "lint-fix": "eslint --fix --ext .js,.vue parliament viewer wiseService capture/plugins common cont3xt", "common:lint": "eslint --ext .js,.vue common", "cont3xt:addtestuser": "cd viewer && node addUser.js -c ../tests/cont3xt.ini -n cont3xt admin admin admin --admin", "cont3xt:build": "cd cont3xt && node vueapp/build/build.js", "cont3xt:bundle": "webpack --progress --config cont3xt/vueapp/build/webpack.dev.conf.js", "cont3xt:dev": "(cd cont3xt && NODE_ENV=development nodemon cont3xt.js -c ../tests/cont3xt.ini) & webpack --watch --progress --config cont3xt/vueapp/build/webpack.dev.conf.js", "cont3xt:doc": "jsdoc2md --partial jsdoc/*.hbs --helper jsdoc/dochelpers.js --files cont3xt/*.js > ../arkimeweb/_wiki/api_cont3xt_docs.md", "cont3xt:lint": "eslint --ext .js,.vue cont3xt common", "cont3xt:testui": "cd cont3xt && jest", "doc": "npm run common:doc && npm run cont3xt:doc && npm run viewer:doc && npm run wise:doc", "esproxy:dev": "cd viewer && NODE_ENV=development nodemon esProxy.js -c ../tests/config.test.ini -n esproxy", "viewer:addtestuser": "cd viewer && node addUser.js -c ../tests/config.test.ini -n testuser admin admin admin --admin --packetSearch", "viewer:build": "cd viewer && node vueapp/build/build.js", "viewer:bundle": "webpack --progress --config viewer/vueapp/build/webpack.dev.conf.js", "viewer:start": "cd viewer && node viewer.js -c ../config.ini", "previewer:start": "npm run viewer:build", "precont3xt:dev": "(cd viewer && node addUser.js -c ../tests/config.test.ini -n testuser admin admin admin --admin --packetSearch --createOnly)", "previewer:dev": "(cd viewer && node addUser.js -c ../tests/config.test.ini -n testuser admin admin admin --admin --packetSearch --createOnly)", "viewer:dev": "(cd viewer && NODE_ENV=development nodemon viewer.js -c ../tests/config.test.ini -n testuser) & webpack --watch --progress --config viewer/vueapp/build/webpack.dev.conf.js", "viewer:test": "(cd viewer && NODE_ENV=development nodemon viewer.js --debug -c ../tests/config.test.ini -n test) & webpack --watch --progress --config viewer/vueapp/build/webpack.dev.conf.js", "viewer:testuserdebug": "(cd viewer && NODE_ENV=development nodemon viewer.js -c ../tests/config.test.ini -n testuser --debug) & webpack --watch --progress --config viewer/vueapp/build/webpack.dev.conf.js", "viewer:multies": "cd viewer && NODE_ENV=development nodemon multies.js -c ../tests/config.test.ini -n all", "viewer:testmultiviewer": "(cd viewer && NODE_ENV=development nodemon viewer.js -c ../tests/config.test.ini -n all) & webpack --watch --progress --config viewer/vueapp/build/webpack.dev.conf.js", "viewer:all": "cd viewer && (NODE_ENV=development nodemon --trace-warnings multies.js -c ../tests/config.test.ini -n all & nodemon --trace-warnings viewer.js -c ../tests/config.test.ini -n test & nodemon --trace-warnings viewer.js -c ../tests/config.test.ini -n test2 & nodemon --trace-warnings viewer.js -c ../tests/config.test.ini -n all) & webpack --watch --progress --config viewer/vueapp/build/webpack.dev.conf.js", "viewer:doc": "jsdoc2md --partial jsdoc/*.hbs --helper jsdoc/dochelpers.js --files viewer/*.js > ../arkimeweb/_wiki/api_viewer_docs.md", "viewer:lint": "eslint --ext .js,.vue viewer capture/plugins", "viewer:lint-fix": "eslint --fix --ext .js,.vue viewer capture/plugins", "viewer:testui": "cd viewer && jest", "parliament:build": "cd parliament && node vueapp/build/build.js", "parliament:bundle": "webpack --progress --config parliament/vueapp/build/webpack.dev.conf.js", "parliament:start": "cd parliament && node parliament.js", "preparliament:start": "npm run parliament:build", "parliament:dev": "(cd parliament && NODE_ENV=development nodemon parliament.js -c ../tests/parliament.ini) & webpack --watch --progress --config parliament/vueapp/build/webpack.dev.conf.js", "parliament:debug": "(cd parliament && NODE_ENV=development nodemon parliament.js-c ../tests/parliament.ini) & webpack --watch --progress --config parliament/vueapp/build/webpack.dev.conf.js", "parliament:lint": "eslint --ext .js,.vue parliament common", "parliament:lint-fix": "eslint --fix --ext .js,.vue parliament common", "parliament:noauth": "(cd parliament && NODE_ENV=development nodemon parliament.js -c ../tests/parliament.ini) & webpack --watch --progress --config parliament/vueapp/build/webpack.dev.conf.js", "wise:build": "cd wiseService && node vueapp/build/build.js", "wise:bundle": "webpack --progress --config wiseService/vueapp/build/webpack.dev.conf.js", "wise:start": "cd wiseService && node wiseService.js -c ../tests/config.test.ini", "wise:lint": "eslint --ext .js,.vue wiseService", "wise:lint-fix": "eslint --fix --ext .js,.vue wiseService", "prewise:start": "npm run wise:build", "wise:doc": "jsdoc2md --partial jsdoc/*.hbs --helper jsdoc/dochelpers.js --files wiseService/*.js > ../arkimeweb/_wiki/api_wise_docs.md", "wise:dev": "(cd wiseService && NODE_ENV=development nodemon wiseService.js -c ../tests/config.test.json --webconfig) & webpack --watch --progress --config wiseService/vueapp/build/webpack.dev.conf.js", "wise:devdebug": "(cd wiseService && NODE_ENV=development nodemon wiseService.js -c ../tests/config.test.json --webconfig --debug --debug) & webpack --watch --progress --config wiseService/vueapp/build/webpack.dev.conf.js", "common:doc": "jsdoc2md --partial jsdoc/*.hbs --helper jsdoc/dochelpers.js --files common/*.js > ../arkimeweb/_wiki/api_common_docs.md", "common:testui": "cd common && jest", "test": "(cd common && jest) && (cd viewer && jest) && (cd cont3xt && jest)"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^13.5.0", "@testing-library/vue": "^5.9.0", "@vue/vue2-jest": "^29.2.6", "autoprefixer": "^10.2.5", "bootstrap": "^4.6.2", "bootstrap-vue": "^2.18.1", "copy-webpack-plugin": "^7.0.0", "country-code-emoji": "^2.3.0", "css-loader": "^5.0.2", "css-minimizer-webpack-plugin": "^3.0.0", "defang-refang": "0.0.5", "eslint": "^8.28.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^7.6.0", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jsdoc-to-markdown": "^9.0.5", "mini-css-extract-plugin": "^1.3.8", "moment-timezone": "^0.5.45", "node-notifier": "^8.0.1", "nodemon": "^3.1.4", "ora": "^5.1.0", "postcss": "^8.4.31", "postcss-import": "^14.0.0", "postcss-loader": "^5.0.0", "postcss-url": "^10.1.3", "qs": "^6.13.0", "sanitize-html": "^2.13.1", "sass": "~1.26.11", "sass-loader": "^12.4.0", "semver": "^7.5.2", "sortablejs": "^1.13.0", "style-loader": "^3.2.1", "terser-webpack-plugin": "^5.1.1", "url-loader": "^4.1.1", "vue-axios": "^3.2.4", "vue-bootstrap-datetimepicker": "^5.0.1", "vue-clickaway": "^2.2.2", "vue-clipboard2": "^0.3.3", "vue-color": "^2.8.1", "vue-json-editor": "^1.4.3", "vue-json-pretty": "^1.7.1", "vue-loader": "^15.9.6", "vue-moment": "^4.1.0", "vue-router": "^3.5.1", "vue-template-compiler": "^2.6.12", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.9.0"}, "engines": {"node": ">= 18.15.0 < 21", "npm": ">= 3.0.0"}}