
<script>
    let safehref = window.location.href.replace(/%3[cC]/g, '%26lt;');
    if (window.location.href !== safehref) {
      console.log("Hacker", window.location.href, safehref);
      window.location.href = safehref;
    }
</script>
<!--
    CyberChef - The Cyber Swiss Army Knife

    @copyright Crown Copyright 2016-2024
    @license Apache-2.0

      Copyright 2016-2024 Crown Copyright

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<!DOCTYPE html><html lang="en" class="classic"><head>
<base href="./cyberchef/" /><meta name="referrer" content="no-referrer">
<meta charset="UTF-8"><title>CyberChef</title><meta name="copyright" content="Crown Copyright 2016-2024"><meta name="description" content="The Cyber Swiss Army Knife - a web app for encryption, encoding, compression and data analysis"><meta name="keywords" content="base64, hex, decode, encode, encrypt, decrypt, compress, decompress, regex, regular expressions, hash, crypt, hexadecimal, user agent, url, certificate, x.509, parser, JSON, gzip,  md5, sha1, aes, des, blowfish, xor"><link rel="icon" type="image/ico" href="assets/aecc661b69309290f600.ico"><script type="application/javascript">"use strict";try{document.querySelector(":root").className=(JSON.parse(localStorage.getItem("options"))||{}).theme}catch(e){}var loadingMsgs=["Proving P = NP...","Computing 6 x 9...","Mining bitcoin...","Dividing by 0...","Initialising Skynet...","[REDACTED]","Downloading more RAM...","Ordering 1s and 0s...","Navigating neural network...","Importing machine learning...","Issuing Alice and Bob one-time pads...","Mining bitcoin cash...","Generating key material by trying to escape vim...","for i in range(additional): Pylon()","(creating unresolved tension...","Symlinking emacs and vim to ed...","Training branch predictor...","Timing cache hits...","Speculatively executing recipes...","Adding LLM hallucinations...","Decompressing malware..."];for(let e=loadingMsgs.length-1;e>0;--e){var j=Math.floor(Math.random()*(e+1)),temp=loadingMsgs[e];loadingMsgs[e]=loadingMsgs[j],loadingMsgs[j]=temp}function changeLoadingMsg(){var e=loadingMsgs.shift();loadingMsgs.push(e);try{var n=document.getElementById("preloader-msg");n.classList.contains("loading")||n.classList.add("loading"),n.innerHTML=e}catch(e){setTimeout(changeLoadingMsg,1e3)}}function loadingErrorHandler(e){function n(e){var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;"};return e.replace(/[&<>"'/`]/g,(function(e){return n[e]}))}var r=e.message+(e.filename?"\nFilename: "+e.filename:"")+(e.lineno?"\nLine: "+e.lineno:"")+(e.colno?"\nColumn: "+e.colno:"")+(e.error?"\nError: "+e.error:"")+"\nUser-Agent: "+navigator.userAgent+"\nCyberChef version: 10.19.2";clearInterval(window.loadingMsgsInt),document.getElementById("preloader").remove(),document.getElementById("preloader-msg").remove(),document.getElementById("preloader-error").innerHTML="CyberChef encountered an error while loading.<br><br>The following browser versions are supported:<ul><li>Google Chrome 50+</li><li>Mozilla Firefox 38+</li></ul>Your user agent is:<br>"+n(navigator.userAgent)+"<br><br>If your browser is supported, please <a href='https://github.com/gchq/CyberChef/issues/new/choose'>raise an issue</a> including the following details:<br><br><pre>"+n(r)+"</pre>"}changeLoadingMsg(),window.loadingMsgsInt=setInterval(changeLoadingMsg,2e3*Math.random()+1500),window.addEventListener("error",loadingErrorHandler)</script><script defer="defer" src="assets/main.js"></script><link href="assets/main.css" rel="stylesheet"></head><body><div id="loader-wrapper"><div id="preloader" class="loader"></div><div id="preloader-msg" class="loading-msg"></div><div id="preloader-error" class="loading-error"></div></div><button type="button" aria-label="Edit Favourites" class="btn btn-warning bmd-btn-icon" id="edit-favourites" data-toggle="tooltip" title="Edit favourites"><i class="material-icons" aria-hidden="true">star</i></button><div id="content-wrapper"><div id="banner" class="row"><div class="col" style="text-align:left;padding-left:10px"><a href="#" data-toggle="modal" data-target="#download-modal" data-help-title="Downloading CyberChef" data-help="<p>CyberChef can be downloaded to run locally or hosted within your own network. It has no server-side component so all that is required is that the ZIP file is uncompressed and the files are accessible.</p><p>As a user, it is worth noting that unofficial versions of CyberChef could have been modified to introduce Input and/or Recipe exfiltration. We recommend always using the official, open source, up-to-date version of CyberChef hosted at <a href='https://gchq.github.io/CyberChef'>https://gchq.github.io/CyberChef</a> if accessible.</p><p>The Network tab in your browser's Developer console (F12) can be used to inspect the network requests made by a website. This can confirm that no data is uploaded when a CyberChef recipe is baked.</p>">Download CyberChef <i class="material-icons">file_download</i></a></div><div class="col-md-6" id="notice-wrapper"><span id="notice"><script type="text/javascript">navigator.userAgent&&navigator.userAgent.match(/Trident/)&&(document.getElementById("notice").innerHTML+="Internet Explorer is not supported, please use Firefox or Chrome instead",alert("Internet Explorer is not supported, please use Firefox or Chrome instead"))</script><noscript>JavaScript is not enabled. Good luck.</noscript></span></div><div class="col" style="text-align:right;padding-right:0"><a href="#" id="options" data-help-title="Options and Settings" data-help="Configurable options to change how CyberChef behaves. These settings are stored in your browser's local storage, meaning they will persist between sessions that use the same browser profile.">Options <i class="material-icons">settings</i></a> <a href="#" id="support" data-toggle="modal" data-target="#support-modal" data-help-title="About / Support" data-help="This pane provides information about the CyberChef web app, how to use some of the features, and how to raise bug reports.">About / Support <i class="material-icons">help</i></a></div></div><div id="workspace-wrapper"><div id="operations" class="split split-horizontal no-select"><div class="title no-select" data-help-title="Operations list" data-help="<p>The Operations list contains all the operations in CyberChef arranged into categories. Some operations may be present in multiple categories. You can search for operations using the search box.</p><p>To use an operation, either double click it, or drag it into the Recipe pane. You will then be able to configure its arguments (or 'Ingredients' in CyberChef terminology).</p>">Operations <span class="op-count"></span></div><input id="search" type="search" class="form-control" placeholder="Search..." autocomplete="off" tabindex="2" data-help-title="Searching for operations" data-help="<p>Use the search box to find useful operations.</p><p>Both operation names and descriptions are queried using a fuzzy matching algorithm.</p>"><ul id="search-results" class="op-list"></ul><div id="categories" class="panel-group no-select"></div></div><div id="recipe" class="split split-horizontal no-select" data-help-title="Recipe pane" data-help="<p>The Recipe pane is where your chosen Operations are configured. If you are a programmer, think of these as functions. If you are not a programmer, these are like steps in a cake recipe. The Input data will be processed based on the Operations in your Recipe.</p><ul><li>To reorder, simply drag and drop the Operations into the order your require</li><li>To remove an operation, either double click it, or drag it outside of the Recipe pane</li></ul><p>The arguments (or 'Ingredients' in CyberChef terminology) can be configured to change how an Operation processes the data.</p>"><div class="title no-select">Recipe <span class="pane-controls hide-on-maximised-output"><button type="button" aria-label="Hide arguments" class="btn btn-primary bmd-btn-icon" id="hide-icon" data-toggle="tooltip" title="Hide arguments" hide-args="false" data-help-title="Hiding every Operation's argument view in a Recipe" data-help="Clicking 'Hide arguments' will hide all the argument views for every Operation in the Recipe, to save space when you have too many Operation in your Recipe"><i class="material-icons">keyboard_arrow_up</i></button> <button type="button" aria-label="Save recipe" class="btn btn-primary bmd-btn-icon" id="save" data-toggle="tooltip" title="Save recipe" data-help-title="Saving a recipe" data-help="<p>Recipes can be represented in a few different formats and saved for use at a later date. You can either copy the Recipe configuration and save it somewhere offline for later use, or use your browser's local storage.</p><ul><li><b>Deep link:</b> The easiest way to share a CyberChef Recipe is to copy the deep link, either from the address bar (which is updated as the Recipe or Input changes), or from the 'Save recipe' pane. When you visit this link, the Recipe and Input will be populated from where you left off.</li><li><b>Chef format:</b> This custom format is designed to be compact and easily readable. It is the format used in CyberChef's URL, so it largely uses characters that do not have to be escaped in URL encoding, making it a little easier to understand what a CyberChef URL contains.</li><li><b>Clean JSON:</b> This JSON format uses whitespace and indentation in a way that makes the Recipe easy to read.</li><li><b>Compact JSON:</b> This is the most compact way that the Recipe can be represented in JSON.</li><li><b>Local storage:</b> Alternatively, you can enter a name into the 'Recipe name' field and save to your browser's local storage. The Recipe will then be available to load from the 'Load Recipe' pane as long as you are using the same browser profile. Be aware that if your browser profile is cleaned, you may lose this data.</li></ul>"><i class="material-icons" aria-hidden="true">save</i></button> <button type="button" aria-label="Load recipe" class="btn btn-primary bmd-btn-icon" id="load" data-toggle="tooltip" title="Load recipe" data-help-title="Loading a recipe" data-help="<p>Saved recipes can be loaded using one of the following methods:</p><ul><li>If you have a CyberChef deep link, simply visit that link and the Recipe and Input will be populated automatically.</li><li>If you have a Recipe string in any of the accepted formats, paste it into the 'Load recipe' pane textbox and click 'Load'.</li><li>If you have saved a Recipe to your browser's local storage, it should be available in the dropdown menu in the 'Load recipe' pane. If it is not there, you may not be using the same browser profile, or your profile may have been cleared.</li></ul>"><i class="material-icons" aria-hidden="true">folder</i></button> <button type="button" aria-label="Clear recipe" class="btn btn-primary bmd-btn-icon" id="clr-recipe" data-toggle="tooltip" title="Clear recipe" data-help-title="Clearing a recipe" data-help="Clicking the 'Clear recipe' button will remove all operations from the Recipe. It will not clear the Input, but it will trigger a Bake if Auto-bake is turned on, which will change the value of the Output."><i class="material-icons" aria-hidden="true">delete</i></button></span></div><ul id="rec-list" class="list-area no-select"></ul><div id="controls" class="no-select hide-on-maximised-output"><div id="controls-content"><button type="button" class="mx-2 btn btn-lg btn-secondary" id="step" data-toggle="tooltip" title="Step through the recipe" data-help-title="Stepping through the Recipe" data-help="<p>The Step button allows you to execute one operation at a time, rather than running the whole Recipe from beginning to end.</p><p>Step allows you to inspect the data at each stage of the Recipe and understand what is being passed to the next operation.</p>">Step</button> <button type="button" class="mx-2 btn btn-lg btn-success btn-raised btn-block" id="bake" data-help-title="Baking" data-help="<p>Baking causes CyberChef to run the Recipe against your data. This involves three steps:</p><ol><li>The data in the Input is encoded into bytes using the character encoding selected in the Input status bar.</li><li>The data is run through each of the operations in the Recipe in turn with the output of one operation being fed into the next operation as its input.</li><li>The outcome of the final operation in the Recipe is decoded into Output text using the character encoding selected in the Output status bar.</li></ol><p>If there are multiple Inputs, the Bake button causes every Input to be baked simultaneously.</p>"><img aria-hidden="true" src="images/cook_male-32x32.png" alt="Chef Icon"> <span>Bake!</span></button><div class="form-group" style="display:contents"><div class="mx-1 checkbox" data-help-title="Auto-bake" data-help="<p>When Auto-bake is turned on, CyberChef will bake the Input using the Recipe whenever anything in the Input or Recipe changes.</p>This includes:<ul><li>Adding or removing operations</li><li>Modifying operation arguments</li><li>Editing the Input</li><li>Changing the Input character encoding</li></ul><p>If there are multiple inputs, only the currently active tab will be baked when Auto-bake triggers. You can bake all inputs manually using the Bake button.</p>"><label id="auto-bake-label"><input type="checkbox" checked="checked" id="auto-bake"><br>Auto Bake</label></div></div></div></div></div><div class="split split-horizontal" id="IO"><div id="input" class="split no-select" data-help-title="Input pane" data-help="<p>Input data can be entered by typing it in, pasting it in, dragging it in, or using the 'Load file' or 'Load folder' buttons.</p><p>CyberChef does its best to represent data as accurately as possible to ensure you know exactly what you are working with. Non-printable characters are represented using control character pictures, for example a null byte (0x00) is displayed like this: <span title='Control character null' aria-label='Control character null' class='cm-specialChar'>␀</span>.</p>"><div class="title no-select"><label for="input-text">Input</label> <span class="pane-controls"><div class="io-info" id="input-files-info"></div><button type="button" aria-label="Add new input tab" class="btn btn-primary bmd-btn-icon" id="btn-new-tab" data-toggle="tooltip" title="Add a new input tab" data-help-title="Tabs" data-help="<p>New tabs can be created to support multiple Inputs. These tabs have their own associated character encodings and EOL separators, as defined in their status bars.</p><p>The deep link in the URL bar only contains information about the currently active tab.</p>"><i class="material-icons" aria-hidden="true">add</i></button> <button type="button" aria-label="Open folder as input" class="btn btn-primary bmd-btn-icon" id="btn-open-folder" data-toggle="tooltip" title="Open folder as input" data-help-title="Opening a folder" data-help="<p>You can open a whole folder into CyberChef, which will result in each file being loaded into a separate Input tab.</p><p>CyberChef can handle lots of Input files, but be aware that performance may suffer, especially if the files are large in size.</p><p>Folders can also be loaded by dragging them over the Input pane and dropping them.</p>"><i class="material-icons" aria-hidden="true">folder_open</i> <input type="file" id="open-folder" style="display:none" multiple="multiple" directory webkitdirectory></button> <button type="button" aria-label="Open file as input" class="btn btn-primary bmd-btn-icon" id="btn-open-file" data-toggle="tooltip" title="Open file as input" data-help-title="Opening a file" data-help="<p>Files can be loaded into CyberChef individually or in groups, either using the 'Open file as input' button, or by dragging and dropping them over the Input pane.</p><p>CyberChef can handle reasonably large files (at least 500MB, depending on hardware), but performance may be impacted and some Operations will run very slowly over large Inputs.</p>"><i class="material-icons" aria-hidden="true">input</i> <input type="file" id="open-file" style="display:none" multiple="multiple"></button> <button type="button" aria-label="Clear input and output" class="btn btn-primary bmd-btn-icon" id="clr-io" data-toggle="tooltip" title="Clear input and output" data-help-title="Clearing the Input and Output" data-help="Clicking the 'Clear input and output' button will remove all Inputs and Outputs. It will not clear the Recipe."><i class="material-icons" aria-hidden="true">delete</i></button> <button type="button" aria-label="Reset pane layout" class="btn btn-primary bmd-btn-icon" id="reset-layout" data-toggle="tooltip" title="Reset pane layout" data-help-title="Resetting the pane layout" data-help="CyberChef's panes can be resized to suit your area of focus. This button will reset the pane sizes to their default configuration."><i class="material-icons" aria-hidden="true">view_compact</i></button></span></div><div id="input-wrapper" class="no-select"><div id="input-tabs-wrapper" style="display:none" class="no-select" data-help-proxy="#btn-new-tab"><span id="btn-previous-input-tab" class="input-tab-buttons">&lt; </span><span id="btn-input-tab-dropdown" class="input-tab-buttons" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">···</span><div class="dropdown-menu" aria-labelledby="btn-input-tab-dropdown"><a id="btn-go-to-input-tab" class="dropdown-item">Go to tab </a><a id="btn-find-input-tab" class="dropdown-item">Find tab </a><a id="btn-close-all-tabs" class="dropdown-item">Close all tabs</a></div><span id="btn-next-input-tab" class="input-tab-buttons">&gt;</span><ul id="input-tabs"></ul></div><div id="input-text"></div></div></div><div id="output" class="split" data-help-title="Output pane" data-help="<p>This pane displays the results of the Recipe after it has processed your Input.</p><p>CyberChef does its best to represent data as accurately as possible to ensure you know exactly what you are working with. Non-printable characters are represented using control character pictures, for example a null byte (0x00) is displayed like this: <span title='Control character null' aria-label='Control character null' class='cm-specialChar'>␀</span>.</p><p>When copying these characters from the Output, the original byte value will be copied into your clipboard, rather than the control character picture itself.</p>"><div class="title no-select"><label for="output-text">Output</label> <span class="pane-controls"><div class="io-info" id="bake-info"></div><button type="button" class="btn btn-primary bmd-btn-icon" id="save-all-to-file" data-toggle="tooltip" title="Save all outputs to a zip file" style="display:none" data-help-title="Saving all outputs to a zip file" data-help="<p>When operating with multiple tabbed Inputs and Outputs, you can use this button to save off all the Outputs at once in a ZIP file.</p><p>Use the 'Bake' button to bake all Inputs at once.</p><p>You will be given the choice to specify the file extension for the Outputs, or you can let CyberChef attempt to detect the filetype of each one. If an Output's type is not clear, CyberChef will use the '.dat' extension.</p>"><i class="material-icons">archive</i></button> <button type="button" aria-label="save" class="btn btn-primary bmd-btn-icon" id="save-to-file" data-toggle="tooltip" title="Save output to file" data-help-title="Saving output to a file" data-help="The currently active Output can be saved to a file. You will be asked to specify a filename. CyberChef will attempt to guess the correct file extension based on the data. If a file type cannot be detected, the extension defaults to '.dat' but can be changed manually."><i class="material-icons" aria-hidden="true">save</i></button> <button type="button" aria-label="copy content" class="btn btn-primary bmd-btn-icon" id="copy-output" data-toggle="tooltip" title="Copy raw output to the clipboard" data-help-title="Copying raw output to the clipboard" data-help="<p>Data can be copied from the Output in the normal way by selecting text and copying it. This button provides a quick way of copying the entire output to the clipboard without having to select it. It directly copies the raw data rather than selecting text in the Output editor. Each method will have the same result, but the button may be more efficient for large Outputs as it does not require any DOM interaction.</p>"><i class="material-icons" aria-hidden="true">content_copy</i></button> <button type="button" aria-label="replace input with output" class="btn btn-primary bmd-btn-icon" id="switch" data-toggle="tooltip" title="Replace input with output" data-help-title="Replacing input with output" data-help="<p>This button moves the currently active Output data into the currently active Input tab, overwriting whatever data was already there.</p><p>The Input character encoding and EOL sequence will be changed to match the current Output values, so that the data is interpreted correctly.</p>"><i class="material-icons" aria-hidden="true">open_in_browser</i></button> <button type="button" aria-label="maximise output pane" class="btn btn-primary bmd-btn-icon" id="maximise-output" data-toggle="tooltip" title="Maximise output pane" data-help-title="Maximising the Output pane" data-help="This button allows you to view the Output pane at maximum size, hiding the Operations, Recipe and Input panes. You can restore the pane to its normal size by clicking the same button again."><i class="material-icons" aria-hidden="true">fullscreen</i></button> </span><button type="button" class="btn btn-primary bmd-btn-icon hidden" id="magic" data-toggle="tooltip" title="Magic!" data-html="true" data-help-title="CyberChef Magic!" data-help="<p>One of CyberChef's best features is its ability to automatically detect which Operations might make more sense of your data. The Magic button appears when CyberChef has a suggested Operation for you based on the data in the Output.</p><p>Clicking on the button will add the suggested Operation(s) to your Recipe.</p><p>This background Magic detection will inspect your Output up to three levels deep and attempt to unwrap it using a range of techniques. For more control, use the 'Magic' operation, which allows you to configure greater depth and filter based on various parameters.</p><p>Further information about CyberChef Magic can be found <a href='https://github.com/gchq/CyberChef/wiki/Automatic-detection-of-encoded-data-using-CyberChef-Magic'>here</a>.</p>"><svg width="22" height="22" viewBox="0 0 24 24"><path d="M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z"/></svg></button> <span id="stale-indicator" class="hidden" data-toggle="tooltip" title="The output is stale. The input or recipe has changed since this output was generated. Bake again to get the new value." data-help-title="Staleness indicator" data-help="The staleness indicator is displayed when the Recipe or Input has changed but the Output has not yet been updated to reflect this. It is most commonly displayed when Auto-bake is turned off and indicates that you need to Bake in order to see an accurate Output."><i class="material-icons">access_time</i></span></div><div id="output-wrapper" class="no-select"><div id="output-tabs-wrapper" style="display:none" class="no-select"><span id="btn-previous-output-tab" class="output-tab-buttons">&lt; </span><span id="btn-output-tab-dropdown" class="output-tab-buttons" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">···</span><div class="dropdown-menu" aria-labelledby="btn-input-tab-dropdown"><a id="btn-go-to-output-tab" class="dropdown-item">Go to tab </a><a id="btn-find-output-tab" class="dropdown-item">Find tab</a></div><span id="btn-next-output-tab" class="output-tab-buttons">&gt;</span><ul id="output-tabs"></ul></div><div id="output-text"></div><div id="output-loader"><div id="output-loader-animation"><object id="bombe" data="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLQogICAgVHVyaW5nLVdlbGNobWFuIEJvbWJlIFNWRyBhbmltYXRpb24KCiAgICBAYXV0aG9yIG4xNDc0MzM1IFtuMTQ3NDMzNUBnbWFpbC5jb21dCiAgICBAY29weXJpZ2h0IENyb3duIENvcHlyaWdodCAyMDE5CiAgICBAbGljZW5zZSBBcGFjaGUtMi4wCi0tPgo8c3ZnIHZlcnNpb249IjEuMiIgYmFzZVByb2ZpbGU9InRpbnkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiCiAgICB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjU1MHB4IiBoZWlnaHQ9IjM1MHB4IiB2aWV3Qm94PSIwIDAgNTUwIDM1MCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgb25sb2FkPSJzZXR1cChldnQpIj4KPHNjcmlwdCB0eXBlPSJ0ZXh0L2VjbWFzY3JpcHQiPgogICAgPCFbQ0RBVEFbCiAgICBmdW5jdGlvbiBzZXR1cChldnQpIHsKICAgICAgICBjb25zdCBhbGxSb3RvcnMgPSBldnQudGFyZ2V0Lm93bmVyRG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLnJvdG9yJyk7CiAgICAgICAgY29uc3Qgcm90b3JzID0gW107CiAgICAgICAgY29uc3QgaW5pdFRpbWUgPSBEYXRlLm5vdygpOwogICAgICAgIGNvbnN0IHRpY2sgPSAzNjAvMjY7CiAgICAgICAgY29uc3Qgc3BlZWQgPSAxMDAwOyAvKiBUaW1lIGZvciBvbmUgZnVsbCByb3RhdGlvbiBvZiB0aGUgZmFzdCByb3RvciAqLwogCiAgICAgICAgZm9yIChjb25zdCByb3RvciBvZiBhbGxSb3RvcnMpIHsKICAgICAgICAgICAgY29uc3Qgcm93ID0gcGFyc2VJbnQocm90b3IuY2xhc3NMaXN0LnZhbHVlLm1hdGNoKC9yb3coXGQpLylbMV0sIDEwKTsKICAgICAgICAgICAgY29uc3Qgc3RhcnRQb3MgPSByb3cgPT09IDIgPyB0aWNrICogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpKjI2KSA6IDA7CiAgICAgICAgICAgIGNvbnN0IGJib3ggPSByb3Rvci5nZXRCQm94KCk7CiAgICAgICAgICAgIGNvbnN0IHggPSBiYm94LndpZHRoLzIgKyBiYm94Lng7CiAgICAgICAgICAgIGNvbnN0IHkgPSBiYm94LmhlaWdodC8yICsgYmJveC55OwogICAgICAgICAgICBjb25zdCB3YWl0ID0gcm93ID09PSAwID8gc3BlZWQvMjYvMS41IDogcm93ID09PSAxID8gc3BlZWQgOiBzcGVlZCoyNjsKIAogICAgICAgICAgICByb3Rvci5zZXRBdHRyaWJ1dGUoInRyYW5zZm9ybSIsICJyb3RhdGUoIiArIHN0YXJ0UG9zICsgIiwgIiArIHggKyAiLCAiICsgeSArICIpIik7CiAKICAgICAgICAgICAgcm90b3JzLnB1c2goewogICAgICAgICAgICAgICAgZWw6IHJvdG9yLAogICAgICAgICAgICAgICAgcG9zOiBzdGFydFBvcywKICAgICAgICAgICAgICAgIHg6IHgsCiAgICAgICAgICAgICAgICB5OiB5LAogICAgICAgICAgICAgICAgbGFzdDogaW5pdFRpbWUsCiAgICAgICAgICAgICAgICB3YWl0OiB3YWl0CiAgICAgICAgICAgIH0pOwogICAgICAgIH0KIAogICAgICAgIHNldEludGVydmFsKGZ1bmN0aW9uKCkgewogICAgICAgICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpOwogICAgICAgICAgICBmb3IgKGNvbnN0IHJvdG9yIG9mIHJvdG9ycykgewogICAgICAgICAgICAgICAgaWYgKG5vdyA+IChyb3Rvci5sYXN0ICsgcm90b3Iud2FpdCkpIHsKICAgICAgICAgICAgICAgICAgICBjb25zdCBudW1UaWNrcyA9IE1hdGguZmxvb3IoKG5vdyAtIHJvdG9yLmxhc3QpIC8gcm90b3Iud2FpdCk7CiAgICAgICAgICAgICAgICAgICAgcm90b3IucG9zID0gKHJvdG9yLnBvcyArIHRpY2sgKiBudW1UaWNrcykgJSAzNjA7CiAgICAgICAgICAgICAgICAgICAgcm90b3IubGFzdCA9IHJvdG9yLmxhc3QgKyByb3Rvci53YWl0ICogbnVtVGlja3M7CiAgICAgICAgICAgICAgICAgICAgcm90b3IuZWwuc2V0QXR0cmlidXRlKCJ0cmFuc2Zvcm0iLCAicm90YXRlKCIgKyByb3Rvci5wb3MgKyAiLCAiICsgcm90b3IueCArICIsICIgKyByb3Rvci55ICsgIikiKTsKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgLyogRG9uJ3QgYm90aGVyIGxvb2tpbmcgYXQgdGhlIHJlc3QgKi8KICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgIH0sIHNwZWVkLzI2LzEuNSAtIDUpOwogICAgfQogICAgXV0+Cjwvc2NyaXB0Pgo8c3R5bGU+CiAgICAucm93MCB7LS1wcmltYXJ5LWNvbG9yOiAjZTVkNDFiO30KICAgIC5yb3cxIHstLXByaW1hcnktY29sb3I6ICNiZTFlMmQ7fQogICAgLnJvdzIgey0tcHJpbWFyeS1jb2xvcjogIzMzN2YyNDt9Cjwvc3R5bGU+CiAKPHN5bWJvbCBpZD0icm90b3IiPgogICAgPGcgdHJhbnNmb3JtPSJzY2FsZSgwLjEpIj4KICAgICAgICA8Y2lyY2xlIGlkPSJjYXNpbmciIGNsYXNzPSJyaW5nLWNvbG9yIiBzdHlsZT0iZmlsbDogdmFyKC0tcHJpbWFyeS1jb2xvciwgI2JlMWUyZCkiIGN4PSI2OTIiIGN5PSI2NzQiIHI9IjUwNSIvPgogICAgICAgIDxjaXJjbGUgaWQ9ImFscGhhYmV0LXJpbmciIGZpbGw9IiM3YTUzNDAiIGN4PSI2OTIiIGN5PSI2NzQiIHI9IjQ3NyIvPgogICAgICAgIDxjaXJjbGUgaWQ9ImZhY2UiIGZpbGw9IiM0MTI2MTIiIGN4PSI2OTIiIGN5PSI2NzQiIHI9IjQxMiIvPgogICAgICAgIDxjaXJjbGUgaWQ9InBsYXRlIiBmaWxsPSIjRjFGMkYyIiBjeD0iNjkyIiBjeT0iNjc0IiByPSIxODUiLz4KICAgICAgICA8ZyBpZD0iYWxwaGFiZXQiIGZpbGw9IiNmZmZmZmYiIGZvbnQtZmFtaWx5PSJzYW5zLXNlcmlmIiBmb250LXNpemU9IjM2Ij4KICAgICAgICAgICAgPHRleHQgdHJhbnNmb3JtPSJtYXRyaXgoMC45NzMxIDAuMjMwMyAtMC4yMzAzIDAuOTczMSA3NzkuODg0OCAyNTYuNTQ4OCkiPlo8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KDAuODkwMyAwLjQ1NTQgLTAuNDU1NCAwLjg5MDMgODc1LjIwMjEgMjg4LjY5NDgpIj5ZPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgwLjc1NjEgMC42NTQ1IC0wLjY1NDUgMC43NTYxIDk2MS44MzExIDM0My42MzcyKSI+WDwvdGV4dD4KICAgICAgICAgICAgPHRleHQgdHJhbnNmb3JtPSJtYXRyaXgoMC41Njk2IDAuODIxOSAtMC44MjE5IDAuNTY5NiAxMDMzLjAxNDYgNDE3LjQ2MTkpIj5XPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgwLjM0NTQgMC45Mzg1IC0wLjkzODUgMC4zNDU0IDEwODguMTEwNCA1MTUuNDYzNCkiPlY8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KDAuMTA3OCAwLjk5NDIgLTAuOTk0MiAwLjEwNzggMTExNC40Njc4IDYxNC41ODk0KSI+VTwvdGV4dD4KICAgICAgICAgICAgPHRleHQgdHJhbnNmb3JtPSJtYXRyaXgoLTAuMTMwMiAwLjk5MTUgLTAuOTkxNSAtMC4xMzAyIDExMTYuMTUzMyA3MTkuMTUyMykiPlQ8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KC0wLjM2MjMgMC45MzIxIC0wLjkzMjEgLTAuMzYyMyAxMDkzLjg5ODQgODE3LjIzNzMpIj5TPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgtMC41NzY3IDAuODE3IC0wLjgxNyAtMC41NzY3IDEwNDguMDYzNSA5MDguOTkxMikiPlI8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KC0wLjc1ODggMC42NTE0IC0wLjY1MTQgLTAuNzU4OCA5ODAuMjAwMiA5ODguNTM0MikiPlE8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KC0wLjg5NDIgMC40NDc2IC0wLjQ0NzYgLTAuODk0MiA4OTMuMzE1NCAxMDUwLjE0MTYpIj5QPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgtMC45NzY2IDAuMjE1IC0wLjIxNSAtMC45NzY2IDc5Ny43NDcxIDEwODcuMzk2NSkiPk88L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KC0wLjk5OTYgLTAuMDI5OCAwLjAyOTggLTAuOTk5NiA2OTIuMDQwNSAxMTAwLjU2ODQpIj5OPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgtMC45NjEgLTAuMjc2NSAwLjI3NjUgLTAuOTYxIDU4OC4yODMyIDEwODcuOTQ0MykiPk08L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KC0wLjg2NTQgLTAuNTAxMSAwLjUwMTEgLTAuODY1NCA0ODcuMzAwMyAxMDQ4LjI0NzEpIj5MPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgtMC43MjQ0IC0wLjY4OTQgMC42ODk0IC0wLjcyNDQgNDA2LjgxNCA5OTEuMTg5NSkiPks8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KC0wLjU0NTYgLTAuODM4IDAuODM4IC0wLjU0NTYgMzM5LjM0MTggOTEzLjg4MDkpIj5KPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgtMC4zNTA4IC0wLjkzNjQgMC45MzY0IC0wLjM1MDggMjk0LjM0OTEgODI4LjI0NDYpIj5JPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgtMC4xMjk1IC0wLjk5MTYgMC45OTE2IC0wLjEyOTUgMjcwLjkyMzMgNzQyLjY1MTkpIj5IPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgwLjExNTMgLTAuOTkzMyAwLjk5MzMgMC4xMTUzIDI2Ni44Nzg0IDYzOC4xOTU4KSI+RzwvdGV4dD4KICAgICAgICAgICAgPHRleHQgdHJhbnNmb3JtPSJtYXRyaXgoMC4zNTI2IC0wLjkzNTggMC45MzU4IDAuMzUyNiAyODguOTk3NiA1MzMuOTg0OSkiPkY8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KDAuNTY0NSAtMC44MjU1IDAuODI1NSAwLjU2NDUgMzMzLjAxOTUgNDQzLjUzMTcpIj5FPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgwLjc0NTkgLTAuNjY2IDAuNjY2IDAuNzQ1OSAzOTguNDQwOSAzNjQuNTA3MykiPkQ8L3RleHQ+CiAgICAgICAgICAgIDx0ZXh0IHRyYW5zZm9ybT0ibWF0cml4KDAuODg1MyAtMC40NjUxIDAuNDY1MSAwLjg4NTMgNDgyLjQ4MjQgMzAyLjM0MTgpIj5DPC90ZXh0PgogICAgICAgICAgICA8dGV4dCB0cmFuc2Zvcm09Im1hdHJpeCgwLjk3MTYgLTAuMjM2NSAwLjIzNjUgMC45NzE2IDU3OS4xMzk2IDI2Mi41NDc5KSI+QjwvdGV4dD4KICAgICAgICAgICAgPHRleHQgdHJhbnNmb3JtPSJtYXRyaXgoMC45OTk5IDAuMDE2MiAtMC4wMTYyIDAuOTk5OSA2ODAuNTU4MSAyNDcuNDMyMSkiPkE8L3RleHQ+CiAgICAgICAgPC9nPgogICAgICAgIDxnIGlkPSJob2xlcyI+CiAgICAgICAgICAgIDxjaXJjbGUgc3Ryb2tlPSIjQzQ5QTZDIiBjeD0iNjkyIiBjeT0iNDM4Ljc4MiIgcj0iNDAuODE2Ii8+CiAgICAgICAgICAgIDxjaXJjbGUgc3Ryb2tlPSIjQzQ5QTZDIiBjeD0iOTI3LjIxOSIgY3k9IjY3NCIgcj0iNDAuODE2Ii8+CiAgICAgICAgICAgIDxjaXJjbGUgc3Ryb2tlPSIjQzQ5QTZDIiBjeD0iNjkyIiBjeT0iOTA5LjIxOSIgcj0iNDAuODE2Ii8+CiAgICAgICAgICAgIDxjaXJjbGUgc3Ryb2tlPSIjQzQ5QTZDIiBjeD0iNDU2Ljc4MSIgY3k9IjY3NCIgcj0iNDAuODE2Ii8+CiAgICAgICAgICAgIDxjaXJjbGUgc3Ryb2tlPSIjQzQ5QTZDIiBjeD0iNTc0LjM5MSIgY3k9IjQ3MC4yOTUiIHI9IjQwLjgxNiIvPgogICAgICAgICAgICA8Y2lyY2xlIHN0cm9rZT0iI0M0OUE2QyIgY3g9Ijg5NS43MDYiIGN5PSI1NTYuMzkiIHI9IjQwLjgxNiIvPgogICAgICAgICAgICA8Y2lyY2xlIHN0cm9rZT0iI0M0OUE2QyIgY3g9IjgwOS42MDkiIGN5PSI4NzcuNzA2IiByPSI0MC44MTYiLz4KICAgICAgICAgICAgPGNpcmNsZSBzdHJva2U9IiNDNDlBNkMiIGN4PSI0ODguMjk1IiBjeT0iNzkxLjYwOSIgcj0iNDAuODE2Ii8+CiAgICAgICAgICAgIDxjaXJjbGUgc3Ryb2tlPSIjQzQ5QTZDIiBjeD0iNDg4LjI5NSIgY3k9IjU1Ni4zOSIgcj0iNDAuODE2Ii8+CiAgICAgICAgICAgIDxjaXJjbGUgc3Ryb2tlPSIjQzQ5QTZDIiBjeD0iODA5LjYwOSIgY3k9IjQ3MC4yOTMiIHI9IjQwLjgxNiIvPgogICAgICAgICAgICA8Y2lyY2xlIHN0cm9rZT0iI0M0OUE2QyIgY3g9Ijg5NS43MDYiIGN5PSI3OTEuNjA5IiByPSI0MC44MTYiLz4KICAgICAgICAgICAgPGNpcmNsZSBzdHJva2U9IiNDNDlBNkMiIGN4PSI1NzQuMzkxIiBjeT0iODc3LjcwNSIgcj0iNDAuODE2Ii8+CiAgICAgICAgPC9nPgogICAgICAgIDxnIGlkPSJwbGF0ZS1zY3Jld3MiPgogICAgICAgICAgICA8Zz4KICAgICAgICAgICAgICAgIDxjaXJjbGUgZmlsbD0iI0JDQkVDMCIgc3Ryb2tlPSIjODA4Mjg1IiBzdHJva2Utd2lkdGg9IjIiIGN4PSI2OTMuMjIzIiBjeT0iNTQzLjUyMSIgcj0iMjUuMzQyIi8+CiAgICAgICAgICAgICAgICA8bGluZSBmaWxsPSIjOTM5NTk4IiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iNyIgeDE9IjY5My40NDYiIHkxPSI1MTkuNzI5IiB4Mj0iNjkzIiB5Mj0iNTY3LjMxMSIvPgogICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDxnPgogICAgICAgICAgICAgICAgPGNpcmNsZSBmaWxsPSIjQkNCRUMwIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgY3g9IjgyMi40NzkiIGN5PSI2NzUuMjIxIiByPSIyNS4zNDIiLz4KICAgICAgICAgICAgICAgIDxsaW5lIGZpbGw9IiM5Mzk1OTgiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSI3IiB4MT0iNzk4LjY4OSIgeTE9IjY3NC45OTkiIHgyPSI4NDYuMjcxIiB5Mj0iNjc1LjQ0NSIvPgogICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDxnPgogICAgICAgICAgICAgICAgPGNpcmNsZSBmaWxsPSIjQkNCRUMwIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgY3g9IjU2Mi42MDUiIGN5PSI2NzMuODg2IiByPSIyNS4zNDEiLz4KICAgICAgICAgICAgICAgIDxsaW5lIGZpbGw9IiM5Mzk1OTgiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSI3IiB4MT0iNTM4LjgxNCIgeTE9IjY3My42NjMiIHgyPSI1ODYuMzk2IiB5Mj0iNjc0LjEwOCIvPgogICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDxnPgogICAgICAgICAgICAgICAgPGNpcmNsZSBmaWxsPSIjQkNCRUMwIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgY3g9IjY5MS44NjMiIGN5PSI4MDUuNTg3IiByPSIyNS4zNDEiLz4KICAgICAgICAgICAgICAgIDxsaW5lIGZpbGw9IiM5Mzk1OTgiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSI3IiB4MT0iNjkyLjA4NiIgeTE9Ijc4MS43OTgiIHgyPSI2OTEuNjQiIHkyPSI4MjkuMzc5Ii8+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICAgICAgPHBhdGggaWQ9InBpbiIgZmlsbC1ydWxlPSJldmVub2RkIiBmaWxsPSIjRDFEM0Q0IiBzdHJva2U9IiM5Mzk1OTgiCiAgICAgICAgICAgIGQ9Ik05NTYuMjc1LDQ0OC43MWMtMC45NjksMC4zODktMS45MjQsMC44MzYtMi44NDgsMS4zMDIKICAgICAgICAgICAgYy01Ljg3NSwyLjk2Mi0xMC45NjUsNy4xOTctMTYuMTY4LDExLjE1MmMtNS44ODUsNC40NzUtMTEuOTMsOC43MzktMTcuODM0LDEzLjE4N2MtMTAuNjg4LDguMDQ5LTIxLjUzMywxNS44ODgtMzIuMjQsMjMuOTA3CiAgICAgICAgICAgIGMtMi4xOTksMS42NDMtNC40MzYsMy4yMzgtNi42MDksNC45MTJjLTE0LjUyNSwxMS4xMzktMjguODY3LDIyLjUzNC00My41NTksMzMuNDUyYy05LjQyOCw3LjAwNC0xOS40MzYsMTMuMzQ2LTI4LjM1NCwyMS4wMDUKICAgICAgICAgICAgYy0xMi40NTksMTAuNjk0LTI0LjcyMywyMi41OTItMzUuODY5LDM0LjY1Yy01LjI4MSw1LjcxMS0xMC42NTYsMTEuMjk3LTE2LjI0MywxNi43MTFjLTMuMDYzLDIuOTY3LTUuODc0LDUuMzgyLTguMTE0LDguOTk3CiAgICAgICAgICAgIGMtMi4yNTYsMy42NDYtNC41ODksNy41NTgtNi4wNTksMTEuNTg2Yy0yLjc1Nyw3LjU2NSwwLjk5OSwxNC4xODksMy40MTMsMjEuMjQxYzUuNTMzLDE2LjE2MS0wLjU2LDMyLjI4OC0xMS40Miw0NC42NzUKICAgICAgICAgICAgYy02Ljk4OSw3Ljk3NC0xNS4zOSwxNS45MzItMjUuMjQ3LDIwLjE2Yy01LjQ1LDIuMzM3LTEyLjA1NywzLjk2NS0xOC4wMTIsNC4xMDVjLTYuMTU5LDAuMTQ4LTExLjkxNC0xLjUzLTE3LjU2OC0zLjgwMgogICAgICAgICAgICBjLTUuMjE1LTIuMDk0LTE0LjkzNi03Ljg3OS0yMC4wMjktMy43NThjLTQuNTI5LDMuNjY3LTguOTM3LDcuNTktMTMuNTAyLDExLjI1MWMtMS4zNTksMS4wODgtMi45NjEsMi4wNDMtNC4xNSwzLjMzCiAgICAgICAgICAgIGMwLjAwMSwwLDE2LjIyNC0xNy41NDUsMTYuNTk2LTE3Ljk0OGMyLjg2LTMuMDkyLDAuMTY4LTkuMjQ2LTEuMDY2LTEyLjQ4NmMtMi4wODgtNS40NzEtMy4xOTktMTAuOTUxLTQuNjMzLTE2LjYxMQogICAgICAgICAgICBjLTEuMDItNC4wMjMtMS44NDEtOC4wNDQtMS41NDgtMTIuMjE1YzAuNjM3LTkuMDkzLDMuOTgtMTkuNjk4LDguOTE4LTI3LjM1NmM2LjQtOS45MjUsMTYuODM0LTE4LjA2MSwyNy41MjctMjIuODc5CiAgICAgICAgICAgIGMxNC44MzEtNi42ODQsMjkuNTQzLTMuMjUyLDQ0LjEzMywyLjIzYzUuNDQxLDIuMDQ0LDEyLjI4NS0yLjIwNiwxNi44MjktNC44MzFjNi4xMTYtMy41MzQsMTEuNTQyLTguMTcxLDE2LjExNy0xMy41NDcKICAgICAgICAgICAgYzkuMTA5LTEwLjcwNywxOS41MDUtMjAuMTE5LDI5LjA4OS0zMC4zNjhjNC45NDUtNS4yODgsMTAuMjI5LTEwLjI5NSwxNS4zMTYtMTUuNDVsMjUuNTg2LTI5Ljg4NGwzMS45NjMtNDMuOTc5CiAgICAgICAgICAgIGMwLDAsMjkuMDI1LTM4LjI4OCwyOS4xMTMtMzguNDA5YzkuMDM3LTExLjkxNywyNC44MjItMjIuOTQsMjUuNTg4LTM5LjE2MWMwLjYxNy0xMy4wMjQtMTQuMjctMTcuMTg0LTI0LjcyNy0xNi44NDEKICAgICAgICAgICAgYy03LjQxLDAuMjQyLTE2LjMxMSwwLjg5NC0yMy4xMTcsNC4xNjFjLTE1LjEsNy4yNDgtMjguMzQyLDE1LjYxNi0zNC42NzYsMzEuOTc5Yy0yLjUwNCw2LjQ2NC00Ljg2NSwxMi42NzEtNi43NiwxOS4zMTkKICAgICAgICAgICAgYy0yLjA1MSw3LjIwOC01LjUzOSwxMS4yMTItOS44MjYsMTcuMDg4Yy0xMC43NzksMTQuNzc4LTI0LjM4OSwyNC43My00MC45OTgsMzIuMWMtNC43NCwyLjEwNC05LjIyOSw0LjI5My0xNC4wOCw2LjEyOQogICAgICAgICAgICBjLTMuOTYxLDEuNS05LjcwNiwzLjEwNC0xMi45MSw1Ljc0N2MtNS45NDgsNC45MDctMTAuMzM0LDE0LjIxNC0xMy4zNTcsMjEuMjA1Yy0xLjkxMSw0LjQxOC0zLjI3OCw5LjA0Ni01LjAwOSwxMy41MzUKICAgICAgICAgICAgYy0yLjA2OSw1LjM3LTIuNTMyLDExLjMyNi00Ljg4LDE2LjUwN2MtMS4zMywyLjkzNS0xLjkxLDUuOTk0LTQuMTA0LDguNDE0Yy0yLjYwOSwyLjg3Ny00LjYyMyw0LjkzOS04LjE1OSw2LjY5MwogICAgICAgICAgICBjLTMuNDUsMS43MTMtNi40ODcsMy45OTctMTAuMzA1LDQuNzM2Yy0yLjcxNywwLjUyOC01LjI3NywxLjQxOC04LjAyMywxLjc5NGMtOC4yMDMsMS4xMjctMTYuNTQsMS43My0yNC42OTUsMy4xNTkKICAgICAgICAgICAgYy0zLjk5NCwwLjctNy45NDcsMi4yODMtMTEuNzkyLDMuNTM0Yy01LjE2NywxLjY4MS0xMC4xMTYsNS45NzItMTQuODQ2LDguNzhjLTEwLjMsNi4xMTktMjAuMDA3LDE1LjAwNC0yNy40NzksMjQuMjc3CiAgICAgICAgICAgIGMtNS4zMzcsNi42MjUtOC45NzYsMTQuMzItMTEuOTI2LDIyLjI1MWMtMi4xNjksNS44MzMtNC4zNTcsMTEuNzU0LTUuMDYxLDE3Ljk3N2MtMC41NjQsNS4wMDEtMC4wNzQsMTAuMDYyLTAuNTAyLDE1LjA3NwogICAgICAgICAgICBjLTAuNzA2LDguMjYtMy4yMDMsMTcuNDctOS4yOTQsMjMuNDE0Yy01LjM2Myw1LjIzNC0xNC4xNzQsMTAuODM0LTIxLjY2NiwxMi4wNDNjLTcuNjA3LDEuMjI2LTE1LjAxNiwwLjExOC0yMC42OTctNS40MDcKICAgICAgICAgICAgYy01LjA5Mi00Ljk1NC05LjI3Ny0xMS4zMDQtMTUuODE2LTE0LjUzOWMtMy44NzMtMS45MTctOC4xMTYtMi4zNTctMTIuMzUxLTEuNTg4Yy0xMC44MiwxLjk2NS0xNy43NjcsNy4zNzQtMTguNDI4LDE4LjYzNwogICAgICAgICAgICBjLTAuNTQ1LDkuMzI1LDEuOTk5LDE1LjE3MSw2LjczMSwyMi45NDdjNC4zMjMsNy4xMDMsNS4zMTUsMTUuNDU2LDkuMjU1LDIyLjc1NmM0LjA1Miw3LjUwMyw3LjgyNSwxNS4yNDgsMTIuMTY5LDIyLjU4MwogICAgICAgICAgICBjMy4wNSw1LjE1Niw2LjgzMiw5LjY2NCwxMC43NDksMTQuMTc2YzEuNzE3LDEuOTc4LDMuNTU0LDQuOTAxLDUuNzMyLDYuMzc4YzUuNjM5LDMuODI3LDEwLjc4NCwzLjMwNSwxNy4wMzIsMS45NTEKICAgICAgICAgICAgYzIuMTc1LTAuNDczLDMuMjMzLDAuMDQ3LDQuNjk0LDEuNjc5YzEuNTU3LDEuNzQsMS4zOTksMS42MDksMC41MDUsMy42OGMtMi43MzIsNi4zMjktNC41NzMsMTIuMDg1LTAuMSwxOC4xOTkKICAgICAgICAgICAgYzMuNDIxLDQuNjc1LDguNzI4LDkuMDEsMTMuNTMxLDEyLjI3MWM3LjE2NSw0Ljg2NSwxNC43OTksOC44MzUsMjIuNDE0LDEyLjkzM2M4Ljk0LDQuODA4LDE4LjQ4OSw4LjE4OCwyNy45NjMsMTEuNzY1CiAgICAgICAgICAgIGM2LjU5NywyLjQ5MSwxMS4wNjgsNy45OTcsMTcuMjI5LDExLjE4NmM2Ljk0NSwzLjU5NSwxMy43NzUsMS4wMzIsMTkuNjkxLTMuMzUzYzUuNjg4LTQuMjE2LDkuNjM0LTkuNTc4LDEwLjA2Ni0xNi44MDQKICAgICAgICAgICAgYzAuNDE1LTYuOTM4LTEuMjM5LTE0LjUwMS01LjUxLTIwLjA4MmMtNC4xNjMtNS40MzktMTAuNzUxLTguOTk2LTEzLjIyOS0xNS42NjRjLTIuNTA2LTYuNzQxLTAuMjk2LTE0LjU5NywxLjMxMy0yMS4zCiAgICAgICAgICAgIGMxLjYwNi02LjY4NywzLjc5OC0xMi42NDIsOS4yMjctMTcuMTdjNS40NTgtNC41NTQsMTIuNDktNy42NTMsMTkuNTgzLTguMjk0YzcuOTU0LTAuNzIxLDE1Ljk4NS0wLjEwNSwyMy45MTItMS4xNjIKICAgICAgICAgICAgYzcuOS0xLjA1MiwxNS44NTUtNC4wNzQsMjIuOTE4LTcuNjk2YzUuMTA0LTIuNjE2LDkuMTA1LTYuOTc5LDEzLjMwOS0xMC43ODljOC44NzUtOC4wNTIsMTguMS0xNi43NTksMjMuNzM1LTI3LjQ1OQogICAgICAgICAgICBjNC4xMjUtNy44MzQsOC41MjEtMTUuNjc1LDExLjAxNi0yNC4yMjJjMS4xNTQtMy45NjIsMi4wOTgtOC4wODMsMi4zMTYtMTIuMjA0YzAuNDI0LTcuODg2LTEuNjg2LTE2LjE3NiwyLjU2NC0yMy4zOTEKICAgICAgICAgICAgYzUuNjQ1LTkuNTgyLDE0Ljg2OS0xNy40MDgsMjUuNTYzLTIwLjU2MWM4LjcyNy0yLjU3MSwxNy42OTctNC42MjQsMjUuOTYzLTguNTIyYzcuMjM0LTMuNDEzLDE2LTcuNjg2LDIwLjE4Mi0xNC44MzMKICAgICAgICAgICAgYzEuODIyLTMuMTE2LDMuMTA5LTYuNzc1LDQuMzYxLTEwLjE1OGMxLjc1Mi00LjcxOSwzLjY0OC05LjM4OSw1LjQtMTQuMTA4YzIuMDgyLTUuNjI1LDQuMDE2LTEwLjg5OCw2Ljg4Ny0xNi4xNDYKICAgICAgICAgICAgYzIuNTUxLTQuNjU2LDYuMDcyLTcuODQ5LDkuNDcxLTExLjg2NGMyLjUwNC0yLjk1Niw0LjUzOS01LjgxNSw3Ljc3My04LjAzMWMzLjIyOS0yLjIwOCw2LjgwNS0zLjgzNSwxMC4wODgtNS45NTIKICAgICAgICAgICAgYzMuNDY5LTIuMjM3LDYuOTU1LTQuNDcsMTAuNTc4LTYuNDQ1YzQuMjQyLTIuMzEyLDguNTU3LTMuNzE2LDEzLjIwNy00LjkyYzEwLjE3Ni0yLjY0MywxOS41OTItNi4zNzYsMjYuOTU5LTE0LjEzNAogICAgICAgICAgICBjNi45NzctNy4zNDksMTMuODItMTUuNzQ3LDE2LjgxNi0yNS41NjZjMi45MzgtOS42MzQsMy45NjctMjAuMTQ3LDIuMDg2LTMwLjA3Yy0wLjk3My01LjEyNC0yLjI5MS0xMS4zMzEtNS44MjQtMTUuMzY3CiAgICAgICAgICAgIEM5NjQuODczLDQ0Ni40NTcsOTYwLjQzMiw0NDcuMDQyLDk1Ni4yNzUsNDQ4LjcxeiIvPgogICAgICAgIDxjaXJjbGUgaWQ9ImNlbnRlci1udXQiIGZpbGw9IiNkMWEyNmEiIHN0cm9rZT0iI2E4OGU3NSIgc3Ryb2tlLXdpZHRoPSIyNSIgY3g9IjY5MiIgY3k9IjY3NCIgcj0iNjAiLz4KICAgICAgICA8ZyBpZD0icGluLXNjcmV3cyI+CiAgICAgICAgICAgIDxjaXJjbGUgZmlsbD0iI0JDQkVDMCIgc3Ryb2tlPSIjNTg1OTVCIiBjeD0iNzY4LjE3NCIgY3k9IjU0NS40NjgiIHI9IjE4LjQ4NSIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSIjQkNCRUMwIiBzdHJva2U9IiM5Mzk1OTgiIHN0cm9rZS13aWR0aD0iNSIgeDE9Ijc1MC4wNzkiIHkxPSI1NDUuMjk4IiB4Mj0iNzg2LjI3MyIgeTI9IjU0NS42MzUiLz4KICAgICAgICAgICAgPHBhdGggZmlsbD0iI0JDQkVDMCIgc3Ryb2tlPSIjNTg1OTVCIiBkPSJNODE5LjgzNCw1NzkuNDM5Yy0xMC4yMTEtMC4wOTQtMTguNTY0LDguMTAzLTE4LjY2LDE4LjMxMwogICAgICAgICAgICAgICAgYy0wLjA5NCwxMC4yMDgsOC4xMDIsMTguNTYyLDE4LjMxMywxOC42NTdjMTAuMjA1LDAuMDk1LDE4LjU2My04LjEwMiwxOC42NTYtMTguMzEyCiAgICAgICAgICAgICAgICBDODM4LjI0LDU4Ny44ODksODMwLjA0MSw1NzkuNTM1LDgxOS44MzQsNTc5LjQzOXoiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0iI0JDQkVDMCIgc3Ryb2tlPSIjOTM5NTk4IiBzdHJva2Utd2lkdGg9IjUiIHgxPSI4MTkuNDkiIHkxPSI2MTYuMDIiIHgyPSI4MTkuODI2IiB5Mj0iNTc5LjgyNiIvPgogICAgICAgICAgICA8Y2lyY2xlIGZpbGw9IiNCQ0JFQzAiIHN0cm9rZT0iIzU4NTk1QiIgY3g9IjYyNi4zNTEiIGN5PSI3MzYuNDYzIiByPSIxOC40ODYiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0iI0JDQkVDMCIgc3Ryb2tlPSIjOTM5NTk4IiBzdHJva2Utd2lkdGg9IjUiIHgxPSI2MzkuMDI2IiB5MT0iNzQ5LjM3OCIgeDI9IjYxMy42NzIiIHkyPSI3MjMuNTQzIi8+CiAgICAgICAgICAgIDxjaXJjbGUgZmlsbD0iI0JDQkVDMCIgc3Ryb2tlPSIjNTg1OTVCIiBjeD0iNTI2LjY2OCIgY3k9IjcwOS4xNTciIHI9IjE4LjQ4NSIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSIjQkNCRUMwIiBzdHJva2U9IiM5Mzk1OTgiIHN0cm9rZS13aWR0aD0iNSIgeDE9IjUyNi40OTgiIHkxPSI3MjcuMjUyIiB4Mj0iNTI2LjgzNyIgeTI9IjY5MS4wNTkiLz4KICAgICAgICAgICAgPGNpcmNsZSBmaWxsPSIjQkNCRUMwIiBzdHJva2U9IiM1ODU5NUIiIGN4PSI2NTQuODM5IiBjeT0iODM5Ljc1MiIgcj0iMTguNDg2Ii8+CiAgICAgICAgICAgIDxsaW5lIGZpbGw9IiNCQ0JFQzAiIHN0cm9rZT0iIzkzOTU5OCIgc3Ryb2tlLXdpZHRoPSI1IiB4MT0iNjM2Ljc0NCIgeTE9IjgzOS41ODMiIHgyPSI2NzIuOTM3IiB5Mj0iODM5LjkyMiIvPgogICAgICAgIDwvZz4KICAgICAgICA8ZyBpZD0icGxhdGUtbWluaS1zY3Jld3MiPgogICAgICAgICAgICA8Y2lyY2xlIGZpbGw9IiNFNkU3RTgiIHN0cm9rZT0iI0E3QTlBQyIgY3g9Ijc4Ni4yMDYiIGN5PSI3NjkuOTg3IiByPSIxNS4zMzIiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0iI0U2RTdFOCIgc3Ryb2tlPSIjQTdBOUFDIiBzdHJva2Utd2lkdGg9IjUiIHgxPSI3NzUuNDk0IiB5MT0iNzgwLjUiIHgyPSI3OTYuOTIiIHkyPSI3NTkuNDcyIi8+CiAgICAgICAgICAgIDxjaXJjbGUgZmlsbD0iI0U2RTdFOCIgc3Ryb2tlPSIjQTdBOUFDIiBjeD0iNTk5Ljk2NiIgY3k9IjU4MC4yMjciIHI9IjE1LjMzMyIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSIjRTZFN0U4IiBzdHJva2U9IiNBN0E5QUMiIHN0cm9rZS13aWR0aD0iNSIgeDE9IjU4OS4yNTQiIHkxPSI1OTAuNzQiIHgyPSI2MTAuNjgyIiB5Mj0iNTY5LjcxMiIvPgogICAgICAgIDwvZz4KICAgICAgICA8ZyBpZD0ic3ByaW5nIj4KICAgICAgICAgICAgPGxpbmUgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjODA4Mjg1IiBzdHJva2Utd2lkdGg9IjIiIHgxPSI1NjEuMzA3IiB5MT0iNzIyLjE2OSIgeDI9IjUzNC41OTIiIHkyPSI3MzkuNTE1Ii8+CiAgICAgICAgICAgIDxsaW5lIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSIyIiB4MT0iNTY1Ljc0NCIgeTE9IjcyNi42ODkiIHgyPSI1MzkuMDI4IiB5Mj0iNzQ0LjAzNCIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjU3MC4xNzkiIHkxPSI3MzEuMjEiIHgyPSI1NDMuNDY1IiB5Mj0iNzQ4LjU1NSIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjU3NC42MTYiIHkxPSI3MzUuNzMiIHgyPSI1NDcuOTAxIiB5Mj0iNzUzLjA3NCIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjU3OS4wNTIiIHkxPSI3NDAuMjUiIHgyPSI1NTIuMzM2IiB5Mj0iNzU3LjU5NiIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjU4My43MjIiIHkxPSI3NDUuMDA4IiB4Mj0iNTU3LjAwNyIgeTI9Ijc2Mi4zNTQiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjODA4Mjg1IiBzdHJva2Utd2lkdGg9IjIiIHgxPSI1ODguMTU4IiB5MT0iNzQ5LjUyOSIgeDI9IjU2MS40NDMiIHkyPSI3NjYuODczIi8+CiAgICAgICAgICAgIDxsaW5lIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSIyIiB4MT0iNTkyLjU5NSIgeTE9Ijc1NC4wNDciIHgyPSI1NjUuODc5IiB5Mj0iNzcxLjM5MyIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjU5Ny4wMyIgeTE9Ijc1OC41NjgiIHgyPSI1NzAuMzE1IiB5Mj0iNzc1LjkxMyIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjYwMS40NjYiIHkxPSI3NjMuMDg4IiB4Mj0iNTc0Ljc1MSIgeTI9Ijc4MC40MzQiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjODA4Mjg1IiBzdHJva2Utd2lkdGg9IjIiIHgxPSI2MDUuOTAyIiB5MT0iNzY3LjYwOCIgeDI9IjU3OS4xODgiIHkyPSI3ODQuOTUzIi8+CiAgICAgICAgICAgIDxsaW5lIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSIyIiB4MT0iNjEwLjMzOCIgeTE9Ijc3Mi4xMjgiIHgyPSI1ODMuNjIzIiB5Mj0iNzg5LjQ3NCIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjYxNC43NzUiIHkxPSI3NzYuNjQ3IiB4Mj0iNTg4LjA2IiB5Mj0iNzkzLjk5NCIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjYxOS4yMTEiIHkxPSI3ODEuMTY5IiB4Mj0iNTkyLjQ5NiIgeTI9Ijc5OC41MTMiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjODA4Mjg1IiBzdHJva2Utd2lkdGg9IjIiIHgxPSI2MjMuNjQ4IiB5MT0iNzg1LjY4OCIgeDI9IjU5Ni45MzMiIHkyPSI4MDMuMDM0Ii8+CiAgICAgICAgICAgIDxsaW5lIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSIyIiB4MT0iNjI4LjA4NCIgeTE9Ijc5MC4yMDkiIHgyPSI2MDEuMzY5IiB5Mj0iODA3LjU1MyIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjYzMi41MiIgeTE9Ijc5NC43MjgiIHgyPSI2MDUuODA2IiB5Mj0iODEyLjA3NCIvPgogICAgICAgICAgICA8bGluZSBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iMiIgeDE9IjYzNi45NTYiIHkxPSI3OTkuMjQ5IiB4Mj0iNjEwLjI0MSIgeTI9IjgxNi41OTMiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjODA4Mjg1IiBzdHJva2Utd2lkdGg9IjIiIHgxPSI2NDEuMzkzIiB5MT0iODAzLjc3IiB4Mj0iNjE0LjY3OCIgeTI9IjgyMS4xMTMiLz4KICAgICAgICAgICAgPGxpbmUgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjODA4Mjg1IiBzdHJva2Utd2lkdGg9IjIiIHgxPSI2NDUuODMiIHkxPSI4MDguMjg4IiB4Mj0iNjE5LjExNCIgeTI9IjgyNS42MzQiLz4KICAgICAgICA8L2c+CiAgICAgICAgPGcgaWQ9ImZhY2UtbnV0cyI+CiAgICAgICAgICAgIDxnPgogICAgICAgICAgICAgICAgPHBvbHlnb24gZmlsbC1ydWxlPSJldmVub2RkIiBmaWxsPSIjRTZFN0U4IiBzdHJva2U9IiM5Mzk1OTgiIHBvaW50cz0iMzQwLjYxNyw3MTUuNjU3IDMwMC40MjMsNzA0Ljg4OAogICAgICAgICAgICAgICAgICAgIDI4OS42NTMsNjY0LjY5MyAzMTkuMDc3LDYzNS4yNyAzNTkuMjcxLDY0Ni4wNCAzNzAuMDQxLDY4Ni4yMzMgICAiLz4KICAgICAgICAgICAgICAgIDxwYXRoIGZpbGw9IiNCQ0JFQzAiIHN0cm9rZT0iIzU4NTk1QiIgc3Ryb2tlLXdpZHRoPSIzIiBkPSJNMzA2Ljc1OSw2OTguMTQ0CiAgICAgICAgICAgICAgICAgICAgYy0xMi41MTYtMTIuNzU1LTEyLjMyNi0zMy4yMzYsMC40MjgtNDUuNzUyYzEyLjc1Mi0xMi41MTYsMzMuMjM0LTEyLjMyNCw0NS43NSwwLjQzMWMxMi41MTYsMTIuNzUsMTIuMzI0LDMzLjIzMy0wLjQyOCw0NS43NDgKICAgICAgICAgICAgICAgICAgICBDMzM5Ljc1NSw3MTEuMDg3LDMxOS4yNzUsNzEwLjg5NSwzMDYuNzU5LDY5OC4xNDR6Ii8+CiAgICAgICAgICAgICAgICA8bGluZSBmaWxsPSIjOTM5NTk4IiBzdHJva2U9IiM4MDgyODUiIHN0cm9rZS13aWR0aD0iNyIgeDE9IjM1MS41MjciIHkxPSI2NTQuMjA4IiB4Mj0iMzA4LjE3MSIgeTI9IjY5Ni43NTciLz4KICAgICAgICAgICAgPC9nPgogICAgICAgICAgICA8Zz4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGZpbGwtcnVsZT0iZXZlbm9kZCIgZmlsbD0iI0U2RTdFOCIgc3Ryb2tlPSIjOTM5NTk4IiBwb2ludHM9IjcwMi43NywzNTQuODYgNjYyLjU3NiwzNDQuMDkxCiAgICAgICAgICAgICAgICAgICAgNjUxLjgwNiwzMDMuODk2IDY4MS4yMywyNzQuNDczIDcyMS40MjQsMjg1LjI0MyA3MzIuMTk0LDMyNS40MzcgICIvPgogICAgICAgICAgICAgICAgPHBhdGggZmlsbD0iIzYwMzkxMyIgc3Ryb2tlPSIjM0MyNDE1IiBzdHJva2Utd2lkdGg9IjMiIGQ9Ik02NjguOTEyLDMzNy4zNDdjLTEyLjUxNi0xMi43NTUtMTIuMzI1LTMzLjIzNiwwLjQyOC00NS43NTJjMTIuNzUyLTEyLjUxNiwzMy4yMzUtMTIuMzI0LDQ1Ljc1LDAuNDMxCiAgICAgICAgICAgICAgICAgICAgYzEyLjUxNiwxMi43NSwxMi4zMjQsMzMuMjMzLTAuNDI4LDQ1Ljc0OEM3MDEuOTA5LDM1MC4yOSw2ODEuNDI4LDM1MC4wOTgsNjY4LjkxMiwzMzcuMzQ3eiIvPgogICAgICAgICAgICAgICAgPGxpbmUgZmlsbD0iIzkzOTU5OCIgc3Ryb2tlPSIjM0MyNDE1IiBzdHJva2Utd2lkdGg9IjciIHgxPSI3MTMuNjgiIHkxPSIyOTMuNDExIiB4Mj0iNjcwLjMyNCIgeTI9IjMzNS45NiIvPgogICAgICAgICAgICAgICAgPGxpbmUgZmlsbD0iIzkzOTU5OCIgc3Ryb2tlPSIjM0MyNDE1IiBzdHJva2Utd2lkdGg9IjciIHgxPSI2NzAuMzI0IiB5MT0iMjkzLjM5MiIgeDI9IjcxMy42OCIgeTI9IjMzNS45NDEiLz4KICAgICAgICAgICAgPC9nPgogICAgICAgICAgICA8Zz4KICAgICAgICAgICAgICAgIDxwb2x5Z29uIGZpbGwtcnVsZT0iZXZlbm9kZCIgZmlsbD0iI0U2RTdFOCIgc3Ryb2tlPSIjOTM5NTk4IiBwb2ludHM9IjcwMi43NywxMDcyLjcyMyA2NjIuNTc2LDEwNjEuOTUzCiAgICAgICAgICAgICAgICAgICAgNjUxLjgwNiwxMDIxLjc1OSA2ODEuMjMsOTkyLjMzNSA3MjEuNDI0LDEwMDMuMTA1IDczMi4xOTMsMTA0My4yOTkgICAiLz4KICAgICAgICAgICAgICAgIDxwYXRoIGZpbGw9IiM2MDM5MTMiIHN0cm9rZT0iIzNDMjQxNSIgc3Ryb2tlLXdpZHRoPSIzIiBkPSJNNjY4LjkxMiwxMDU1LjIxYy0xMi41MTYtMTIuNzU2LTEyLjMyNS0zMy4yMzYsMC40MjgtNDUuNzUyYzEyLjc1Mi0xMi41MTYsMzMuMjM1LTEyLjMyNCw0NS43NSwwLjQzMQogICAgICAgICAgICAgICAgICAgIGMxMi41MTYsMTIuNzUsMTIuMzI0LDMzLjIzMy0wLjQyOCw0NS43NDdDNzAxLjkwOSwxMDY4LjE1Miw2ODEuNDI4LDEwNjcuOTYsNjY4LjkxMiwxMDU1LjIxeiIvPgogICAgICAgICAgICAgICAgPGxpbmUgZmlsbD0iIzkzOTU5OCIgc3Ryb2tlPSIjM0MyNDE1IiBzdHJva2Utd2lkdGg9IjciIHgxPSI3MTMuNjgiIHkxPSIxMDExLjI3MiIgeDI9IjY3MC4zMjQiIHkyPSIxMDUzLjgyMiIvPgogICAgICAgICAgICAgICAgPGxpbmUgZmlsbD0iIzkzOTU5OCIgc3Ryb2tlPSIjM0MyNDE1IiBzdHJva2Utd2lkdGg9IjciIHgxPSI2NzAuMzI0IiB5MT0iMTAxMS4yNTQiIHgyPSI3MTMuNjgiIHkyPSIxMDUzLjgwNCIvPgogICAgICAgICAgICA8L2c+CiAgICAgICAgICAgIDxnPgogICAgICAgICAgICAgICAgPHBvbHlnb24gZmlsbC1ydWxlPSJldmVub2RkIiBmaWxsPSIjRTZFN0U4IiBzdHJva2U9IiM5Mzk1OTgiIHBvaW50cz0iMTAzOC41NTYsNzE1LjY1OCAxMDc4Ljc0OSw3MDQuODg4CiAgICAgICAgICAgICAgICAgICAgMTA4OS41MjEsNjY0LjY5NCAxMDYwLjA5Nyw2MzUuMjcgMTAxOS45MDEsNjQ2LjA0MSAxMDA5LjEzMiw2ODYuMjM0ICAiLz4KICAgICAgICAgICAgICAgIDxwYXRoIGZpbGw9IiNCQ0JFQzAiIHN0cm9rZT0iIzU4NTk1QiIgc3Ryb2tlLXdpZHRoPSIzIiBkPSJNMTA3Mi40MTMsNjk4LjE0NQogICAgICAgICAgICAgICAgICAgIGMxMi41MTYtMTIuNzU1LDEyLjMyNi0zMy4yMzYtMC40MjgtNDUuNzUyYy0xMi43NTItMTIuNTE2LTMzLjIzNC0xMi4zMjQtNDUuNzUsMC40MzFjLTEyLjUxNiwxMi43NS0xMi4zMjQsMzMuMjMzLDAuNDI4LDQ1Ljc0OAogICAgICAgICAgICAgICAgICAgIEMxMDM5LjQxNyw3MTEuMDg3LDEwNTkuODk3LDcxMC44OTYsMTA3Mi40MTMsNjk4LjE0NXoiLz4KICAgICAgICAgICAgICAgIDxsaW5lIGZpbGw9IiM5Mzk1OTgiIHN0cm9rZT0iIzgwODI4NSIgc3Ryb2tlLXdpZHRoPSI3IiB4MT0iMTAyNy42NDYiIHkxPSI2NTQuMjA4IiB4Mj0iMTA3MS4wMDEiIHkyPSI2OTYuNzU3Ii8+CiAgICAgICAgICAgIDwvZz4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ltYm9sPgogCjxnIGNsYXNzPSJyb3RvciByb3cwIj48dXNlIHhsaW5rOmhyZWY9IiNyb3RvciIgeD0iMCIgeT0iMCIgLz48L2c+CjxnIGNsYXNzPSJyb3RvciByb3cwIj48dXNlIHhsaW5rOmhyZWY9IiNyb3RvciIgeD0iMTA1IiB5PSIwIiAvPjwvZz4KPGcgY2xhc3M9InJvdG9yIHJvdzAiPjx1c2UgeGxpbms6aHJlZj0iI3JvdG9yIiB4PSIyMTAiIHk9IjAiIC8+PC9nPgo8ZyBjbGFzcz0icm90b3Igcm93MCI+PHVzZSB4bGluazpocmVmPSIjcm90b3IiIHg9IjMxNSIgeT0iMCIgLz48L2c+CjxnIGNsYXNzPSJyb3RvciByb3cwIj48dXNlIHhsaW5rOmhyZWY9IiNyb3RvciIgeD0iNDIwIiB5PSIwIiAvPjwvZz4KPGcgY2xhc3M9InJvdG9yIHJvdzEiPjx1c2UgeGxpbms6aHJlZj0iI3JvdG9yIiB4PSIwIiB5PSIxMDUiIC8+PC9nPgo8ZyBjbGFzcz0icm90b3Igcm93MSI+PHVzZSB4bGluazpocmVmPSIjcm90b3IiIHg9IjEwNSIgeT0iMTA1IiAvPjwvZz4KPGcgY2xhc3M9InJvdG9yIHJvdzEiPjx1c2UgeGxpbms6aHJlZj0iI3JvdG9yIiB4PSIyMTAiIHk9IjEwNSIgLz48L2c+CjxnIGNsYXNzPSJyb3RvciByb3cxIj48dXNlIHhsaW5rOmhyZWY9IiNyb3RvciIgeD0iMzE1IiB5PSIxMDUiIC8+PC9nPgo8ZyBjbGFzcz0icm90b3Igcm93MSI+PHVzZSB4bGluazpocmVmPSIjcm90b3IiIHg9IjQyMCIgeT0iMTA1IiAvPjwvZz4KPGcgY2xhc3M9InJvdG9yIHJvdzIiPjx1c2UgeGxpbms6aHJlZj0iI3JvdG9yIiB4PSIwIiB5PSIyMTAiIC8+PC9nPgo8ZyBjbGFzcz0icm90b3Igcm93MiI+PHVzZSB4bGluazpocmVmPSIjcm90b3IiIHg9IjEwNSIgeT0iMjEwIiAvPjwvZz4KPGcgY2xhc3M9InJvdG9yIHJvdzIiPjx1c2UgeGxpbms6aHJlZj0iI3JvdG9yIiB4PSIyMTAiIHk9IjIxMCIgLz48L2c+CjxnIGNsYXNzPSJyb3RvciByb3cyIj48dXNlIHhsaW5rOmhyZWY9IiNyb3RvciIgeD0iMzE1IiB5PSIyMTAiIC8+PC9nPgo8ZyBjbGFzcz0icm90b3Igcm93MiI+PHVzZSB4bGluazpocmVmPSIjcm90b3IiIHg9IjQyMCIgeT0iMjEwIiAvPjwvZz4KPC9zdmc+Cg==" width="100%" height="100%" data-help-title="Loading animation" data-help="This loading animation shows an accurate representation of how rotors moved on The Bombe, an electro-mechanical device built at Bletchley Park in 1939 by Alan Turing with refinements by Gordon Welchman in 1940. The Bombe was used by the Government Code and Cipher School (the precursor to GCHQ) to discover daily settings of Enigma machines used by the German military in World War 2.<br><br>More information can be found on <a href='https://wikipedia.org/wiki/Bombe'>Wikipedia</a>."></object></div><div class="loading-msg"></div></div></div></div></div></div></div><div class="modal fade" id="save-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content" data-help-proxy="#save"><div class="modal-header"><h5 class="modal-title">Save recipe</h5></div><div class="modal-body"><div class="form-group"><ul class="nav nav-tabs" role="tablist"><li class="nav-item"><a class="nav-link active" href="#chef-format" role="tab" data-toggle="tab">Chef format</a></li><li class="nav-item"><a class="nav-link" href="#clean-json" role="tab" data-toggle="tab">Clean JSON</a></li><li class="nav-item"><a class="nav-link" href="#compact-json" role="tab" data-toggle="tab">Compact JSON</a></li></ul><div class="tab-content" id="save-texts"><div role="tabpanel" class="tab-pane active" id="chef-format"><textarea class="form-control" id="save-text-chef" rows="5"></textarea></div><div role="tabpanel" class="tab-pane" id="clean-json"><textarea class="form-control" id="save-text-clean" rows="5"></textarea></div><div role="tabpanel" class="tab-pane" id="compact-json"><textarea class="form-control" id="save-text-compact" rows="5"></textarea></div></div></div><div class="form-group"><label for="save-name" class="bmd-label-floating">Recipe name</label> <input type="text" class="form-control" id="save-name"> <span class="bmd-help">Save your recipe to local storage using this name, or copy it to load later</span></div></div><div class="modal-footer" id="save-footer"><button type="button" class="btn btn-primary" id="save-button" data-dismiss="modal">Save</button> <button type="button" class="btn btn-secondary" data-dismiss="modal">Done</button></div><div class="modal-body"><div class="form-group" id="save-link-group"><h6 style="display:inline">Deep link</h6><div class="save-link-options"><label class="checkbox-inline"><input type="checkbox" id="save-link-recipe-checkbox" checked="checked"> Include recipe</label> <label class="checkbox-inline"><input type="checkbox" id="save-link-input-checkbox" checked="checked"> Include input</label></div><br><br><a id="save-link" style="word-wrap:break-word"></a></div></div></div></div></div><div class="modal fade" id="load-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content" data-help-proxy="#load"><div class="modal-header"><h5 class="modal-title">Load recipe</h5></div><div class="modal-body"><div class="form-group"><label for="load-name" class="bmd-label-floating">Recipe name</label> <select class="form-control" id="load-name"></select> <span class="bmd-help">Load your recipe from local storage by selecting its name from the drop-down</span></div><div class="form-group"><label for="load-text" class="bmd-label-floating">Recipe</label> <textarea class="form-control" id="load-text" rows="5"></textarea> <span class="bmd-help">Load your recipe by pasting it into this box</span></div></div><div class="modal-footer"><button type="button" class="btn btn-primary" id="load-button" data-dismiss="modal">Load</button> <button type="button" class="btn btn-danger" id="load-delete-button">Delete</button> <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button></div></div></div></div><div class="modal fade" id="options-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content" data-help-proxy="#options"><div class="modal-header"><h5 class="modal-title">Options</h5></div><div class="modal-body" id="options-body"><p style="font-weight:700">Please note that these options will persist between sessions.</p><div class="form-group option-item"><label for="theme" class="bmd-label-floating">Theme (only supported in modern browsers)</label> <select class="form-control" option="theme" id="theme"><option value="classic">Classic</option><option value="dark">Dark</option><option value="geocities">GeoCities</option><option value="solarizedDark">Solarized Dark</option><option value="solarizedLight">Solarized Light</option></select></div><div class="form-group option-item"><label for="logLevel" class="bmd-label-floating">Console logging level</label> <select class="form-control" option="logLevel" id="logLevel"><option value="silent">Silent</option><option value="error">Error</option><option value="warn">Warn</option><option value="info">Info</option><option value="debug">Debug</option><option value="trace">Trace</option></select></div><div class="checkbox option-item"><label for="updateUrl"><input type="checkbox" option="updateUrl" id="updateUrl" checked="checked"> Update the URL when the input or recipe changes</label></div><div class="checkbox option-item"><label for="showHighlighter"><input type="checkbox" option="showHighlighter" id="showHighlighter" checked="checked"> Highlight selected bytes in output and input (when possible)</label></div><div class="checkbox option-item"><label for="wordWrap"><input type="checkbox" option="wordWrap" id="wordWrap" checked="checked"> Word wrap the input and output</label></div><div class="checkbox option-item mb-0"><label for="showErrors"><input type="checkbox" option="showErrors" id="showErrors" checked="checked"> Show errors from operations (recommended)</label></div><div class="form-group option-item"><label for="errorTimeout" class="bmd-label-floating">Operation error timeout in ms (0 for never)</label> <input type="number" class="form-control" option="errorTimeout" id="errorTimeout"></div><div class="checkbox option-item"><label for="useMetaKey"><input type="checkbox" option="useMetaKey" id="useMetaKey"> Use meta key for keybindings (Windows ⊞/Command ⌘)</label></div><div class="checkbox option-item"><label for="autoMagic"><input type="checkbox" option="autoMagic" id="autoMagic"> Attempt to detect encoded data automagically</label></div><div class="checkbox option-item"><label for="imagePreview"><input type="checkbox" option="imagePreview" id="imagePreview"> Render a preview of the input if it's detected to be an image</label></div><div class="checkbox option-item"><label for="syncTabs"><input type="checkbox" option="syncTabs" id="syncTabs"> Keep the current tab in sync between the input and output</label></div><div class="checkbox option-item"><label for="showCatCount"><input type="checkbox" option="showCatCount" id="showCatCount"> Show the number of operations in each category</label></div></div><div class="modal-footer"><button type="button" class="btn btn-secondary" id="reset-options">Reset options to default</button> <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button></div></div></div></div><div class="modal fade" id="favourites-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content" data-help-proxy="a[data-target='#catFavourites']"><div class="modal-header"><h5 class="modal-title">Edit Favourites</h5></div><div class="modal-body" id="favourites-body"><ul><li><span style="font-weight:700">To add:</span> drag the operation over the favourites category and drop it</li><li><span style="font-weight:700">To reorder:</span> drag up and down in the list below</li><li><span style="font-weight:700">To remove:</span> hit the delete button or drag out of the list below</li></ul><br><ul id="edit-favourites-list" class="op-list"></ul><div class="option-item"></div></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-dismiss="modal" id="reset-favourites">Reset favourites to default</button> <button type="button" class="btn btn-success" data-dismiss="modal" id="save-favourites">Save</button> <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button></div></div></div></div><div class="modal fade" id="support-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content" data-help-proxy="#support"><div class="modal-header"><h5 class="modal-title">CyberChef - The Cyber Swiss Army Knife</h5></div><div class="modal-body"><img aria-hidden="true" class="about-img-left" src="images/cyberchef-128x128.png" alt="CyberChef Logo"><p class="subtext">Version 10.19.2<br>Compile time: 14/08/2024 17:26:43 UTC</p><p>&copy; Crown Copyright 2016-2024.</p><p>Released under the Apache Licence, Version 2.0.</p><p><a href="https://gitter.im/gchq/CyberChef"><img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI5MiIgaGVpZ2h0PSIyMCI+PGxpbmVhckdyYWRpZW50IGlkPSJiIiB4Mj0iMCIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCIgc3RvcC1jb2xvcj0iI2JiYiIgc3RvcC1vcGFjaXR5PSIuMSIvPjxzdG9wIG9mZnNldD0iMSIgc3RvcC1vcGFjaXR5PSIuMSIvPjwvbGluZWFyR3JhZGllbnQ+PG1hc2sgaWQ9ImEiPjxyZWN0IHdpZHRoPSI5MiIgaGVpZ2h0PSIyMCIgcng9IjMiIGZpbGw9IiNmZmYiLz48L21hc2s+PGcgbWFzaz0idXJsKCNhKSI+PHBhdGggZmlsbD0iIzU1NSIgZD0iTTAgMGgzNHYyMEgweiIvPjxwYXRoIGZpbGw9IiM0NkJDOTkiIGQ9Ik0zNCAwaDU4djIwSDM0eiIvPjxwYXRoIGZpbGw9InVybCgjYikiIGQ9Ik0wIDBoOTJ2MjBIMHoiLz48L2c+PGcgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkRlamFWdSBTYW5zLFZlcmRhbmEsR2VuZXZhLHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTEiPjx0ZXh0IHg9IjE3IiB5PSIxNSIgZmlsbD0iIzAxMDEwMSIgZmlsbC1vcGFjaXR5PSIuMyI+Y2hhdDwvdGV4dD48dGV4dCB4PSIxNyIgeT0iMTQiPmNoYXQ8L3RleHQ+PHRleHQgeD0iNjIiIHk9IjE1IiBmaWxsPSIjMDEwMTAxIiBmaWxsLW9wYWNpdHk9Ii4zIj5vbiBnaXR0ZXI8L3RleHQ+PHRleHQgeD0iNjIiIHk9IjE0Ij5vbiBnaXR0ZXI8L3RleHQ+PC9nPjwvc3ZnPg=="></a></p><ul class="nav nav-tabs" role="tablist"><li class="nav-item" role="presentation"><a class="nav-link active" href="#faqs" aria-controls="profile" role="tab" data-toggle="tab">FAQs</a></li><li class="nav-item" role="presentation"><a class="nav-link" href="#report-bug" aria-controls="messages" role="tab" data-toggle="tab">Report a bug</a></li><li class="nav-item" role="presentation"><a class="nav-link" href="#about" aria-controls="messages" role="tab" data-toggle="tab">About</a></li><li class="nav-item" role="presentation"><a class="nav-link" href="#keybindings" aria-controls="messages" role="tab" data-toggle="tab">Keybindings</a></li></ul><div class="tab-content"><div role="tabpanel" class="tab-pane active" id="faqs" data-help-title="FAQ pane" data-help="The Frequently Asked Questions pane provides answers to some of the most common queries people have about CyberChef."><br><a class="btn btn-primary" data-toggle="collapse" data-target="#faq-contextual-help">How does X feature work?</a><div class="collapse" id="faq-contextual-help"><p>CyberChef has a contextual help feature. Just hover your cursor over a feature that you want to learn more about and press <code>F1</code> on your keyboard to get some information about it. Give it a try by hovering over this text and pressing <code>F1</code> now!</p></div><br><a class="btn btn-primary" data-toggle="collapse" data-target="#faq-examples">What sort of things can I do with CyberChef?</a><div class="collapse" id="faq-examples"><p>There are <span class="num-ops">hundreds of</span> operations in CyberChef allowing you to carry out simple and complex tasks easily. Here are some examples:</p><ul><li><a href="#recipe=From_Base64('A-Za-z0-9%2B/%3D',true)&input=VTI4Z2JHOXVaeUJoYm1RZ2RHaGhibXR6SUdadmNpQmhiR3dnZEdobElHWnBjMmd1">Decode a Base64-encoded string</a></li><li><a href="#recipe=Translate_DateTime_Format('Standard%20date%20and%20time','DD/MM/YYYY%20HH:mm:ss','UTC','dddd%20Do%20MMMM%20YYYY%20HH:mm:ss%20Z%20z','Australia/Queensland')&input=MTUvMDYvMjAxNSAyMDo0NTowMA">Convert a date and time to a different time zone</a></li><li><a href="#recipe=Parse_IPv6_address()&input=MjAwMTowMDAwOjQxMzY6ZTM3ODo4MDAwOjYzYmY6M2ZmZjpmZGQy">Parse a Teredo IPv6 address</a></li><li><a href="#recipe=From_Hexdump()Gunzip()&input=MDAwMDAwMDAgIDFmIDhiIDA4IDAwIDEyIGJjIGYzIDU3IDAwIGZmIDBkIGM3IGMxIDA5IDAwIDIwICB8Li4uLi6881cu/y7HwS4uIHwKMDAwMDAwMTAgIDA4IDA1IGQwIDU1IGZlIDA0IDJkIGQzIDA0IDFmIGNhIDhjIDQ0IDIxIDViIGZmICB8Li7QVf4uLdMuLsouRCFb/3wKMDAwMDAwMjAgIDYwIGM3IGQ3IDAzIDE2IGJlIDQwIDFmIDc4IDRhIDNmIDA5IDg5IDBiIDlhIDdkICB8YMfXLi6%2BQC54Sj8uLi4ufXwKMDAwMDAwMzAgIDRlIGM4IDRlIDZkIDA1IDFlIDAxIDhiIDRjIDI0IDAwIDAwIDAwICAgICAgICAgICB8TshObS4uLi5MJC4uLnw">Convert data from a hexdump, then decompress</a></li><li><a href="#recipe=RC4(%7B'option':'UTF8','string':'secret'%7D,'Hex','Hex')Disassemble_x86('64','Full%20x86%20architecture',16,0,true,true)&input=MjFkZGQyNTQwMTYwZWU2NWZlMDc3NzEwM2YyYTM5ZmJlNWJjYjZhYTBhYWJkNDE0ZjkwYzZjYWY1MzEyNzU0YWY3NzRiNzZiM2JiY2QxOTNjYjNkZGZkYmM1YTI2NTMzYTY4NmI1OWI4ZmVkNGQzODBkNDc0NDIwMWFlYzIwNDA1MDcxMzhlMmZlMmIzOTUwNDQ2ZGIzMWQyYmM2MjliZTRkM2YyZWIwMDQzYzI5M2Q3YTVkMjk2MmMwMGZlNmRhMzAwNzJkOGM1YTZiNGZlN2Q4NTlhMDQwZWVhZjI5OTczMzYzMDJmNWEwZWMxOQ">Decrypt and disassemble shellcode</a></li><li><a href="#recipe=Fork('%5C%5Cn','%5C%5Cn',false)From_UNIX_Timestamp('Seconds%20(s)')&input=OTc4MzQ2ODAwCjEwMTI2NTEyMDAKMTA0NjY5NjQwMAoxMDgxMDg3MjAwCjExMTUzMDUyMDAKMTE0OTYwOTYwMA">Display multiple timestamps as full dates</a></li><li><a href="#recipe=Fork('%5C%5Cn','%5C%5Cn',false)Conditional_Jump('1',false,'base64',10)To_Hex('Space')Return()Label('base64')To_Base64('A-Za-z0-9%2B/%3D')&input=U29tZSBkYXRhIHdpdGggYSAxIGluIGl0ClNvbWUgZGF0YSB3aXRoIGEgMiBpbiBpdA">Carry out different operations on data of different types</a></li><li><a href="#recipe=Register('key%3D(%5B%5C%5Cda-f%5D*)',true,false)Find_/_Replace(%7B'option':'Regex','string':'.*data%3D(.*)'%7D,'$1',true,false,true)RC4(%7B'option':'Hex','string':'$R0'%7D,'Hex','Latin1')&input=aHR0cDovL21hbHdhcmV6LmJpei9iZWFjb24ucGhwP2tleT0wZTkzMmE1YyZkYXRhPThkYjdkNWViZTM4NjYzYTU0ZWNiYjMzNGUzZGIxMQ">Use parts of the input as arguments to operations</a></li></ul></div><br><a class="btn btn-primary" data-toggle="collapse" data-target="#faq-load-files">Can I load input directly from files?</a><div class="collapse" id="faq-load-files"><p>Yes! Just drag your file over the input box and drop it.</p><p>CyberChef can handle files up to around 2GB (depending on your browser), however some of the operations may take a very long time to run over this much data.</p><p>If the output is larger than a certain threshold (default <a href="#recipe=Multiply('Line%20feed')Convert_data_units('Bytes%20(B)','Mebibytes%20(MiB)')&input=MTAyNAoxMDI0">1MiB</a>), it will be presented to you as a file available for download. Slices of the file can be viewed in the output if you need to inspect them.</p></div><br><a class="btn btn-primary" data-toggle="collapse" data-target="#faq-fork">How do I run operation X over multiple inputs at once?</a><div class="collapse" id="faq-fork"><p>Maybe you have 10 timestamps that you want to parse or 16 encoded strings that all have the same key.</p><p>The 'Fork' operation (found in the 'Flow control' category) splits up the input line by line and runs all subsequent operations on each line separately. Each output is then displayed on a separate line. These delimiters can be changed, so if your inputs are separated by commas, you can change the split delimiter to a comma instead.</p><p><a href="#recipe=Fork('%5C%5Cn','%5C%5Cn',false)From_UNIX_Timestamp('Seconds%20(s)')&input=OTc4MzQ2ODAwCjEwMTI2NTEyMDAKMTA0NjY5NjQwMAoxMDgxMDg3MjAwCjExMTUzMDUyMDAKMTE0OTYwOTYwMA">Click here</a> for an example.</p></div><br><a class="btn btn-primary" data-toggle="collapse" data-target="#faq-magic">How does the 'Magic' operation work?</a><div class="collapse" id="faq-magic"><p>The 'Magic' operation uses a number of methods to detect encoded data and the operations which can be used to make sense of it. A technical description of these methods can be found <a href="https://github.com/gchq/CyberChef/wiki/Automatic-detection-of-encoded-data-using-CyberChef-Magic">here</a>.</p></div></div><div role="tabpanel" class="tab-pane" id="report-bug"><br><p>If you find a bug in CyberChef, please raise an issue in our GitHub repository explaining it in as much detail as possible. Copy and include the following information if relevant.</p><br><pre id="report-bug-info"></pre><br><a class="btn btn-primary" href="https://github.com/gchq/CyberChef/issues/new/choose" role="button">Raise issue on GitHub</a></div><div role="tabpanel" class="tab-pane" id="about" style="padding:20px"><h5><strong>What</strong></h5><p>A simple, intuitive web app for analysing and decoding data without having to deal with complex tools or programming languages. CyberChef encourages both technical and non-technical people to explore data formats, encryption and compression.</p><br><h5><strong>Why</strong></h5><p>Digital data comes in all shapes, sizes and formats in the modern world – CyberChef helps to make sense of this data all on one easy-to-use platform.</p><br><h5><strong>How</strong></h5><p>The interface is designed with simplicity at its heart. Complex techniques are now as trivial as drag-and-drop. Simple functions can be combined to build up a "recipe", potentially resulting in complex analysis, which can be shared with other users and used with their input.</p><p>For those comfortable writing code, CyberChef is a quick and efficient way to prototype solutions to a problem which can then be scripted once proven to work.</p><br><h5><strong>Who</strong></h5><p>It is expected that CyberChef will be useful for cybersecurity and antivirus companies. It should also appeal to the academic world and any individuals or companies involved in the analysis of digital data, be that software developers, analysts, mathematicians or casual puzzle solvers.</p><br><h5><strong>Aim</strong></h5><p>It is hoped that by releasing CyberChef through <a href="https://github.com/gchq/CyberChef">GitHub</a>, contributions can be added which can be rolled out into future versions of the tool.</p><br><br><p>There are <span class="num-ops">hundreds of</span> useful operations in CyberChef for anyone working on anything vaguely Internet-related, whether you just want to convert a timestamp to a different format, decompress gzipped data, create a SHA3 hash, or parse an X.509 certificate to find out who issued it.</p><p>It’s the Cyber Swiss Army Knife.</p></div><div role="tabpanel" class="tab-pane" id="keybindings" style="padding:20px"><table class="table table-condensed table-bordered table-hover" id="keybList"></table></div></div></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button></div><a href="https://github.com/gchq/CyberChef"><img aria-hidden="true" style="position:absolute;top:0;right:0;border:0" src="images/fork_me.png" alt="Fork me on GitHub"></a></div></div></div><div class="modal fade" id="confirm-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="confirm-title"></h5></div><div class="modal-body" id="confirm-body"></div><div class="modal-footer"><button type="button" class="btn btn-success" id="confirm-yes">Yes</button> <button type="button" class="btn btn-danger" id="confirm-no" data-dismiss="modal">No</button></div></div></div></div><div class="modal fade" id="input-tab-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title">Find Input Tab</h5></div><div class="modal-body" id="input-tab-body"><h6>Load Status</h6><div id="input-find-options"><ul id="input-find-options-checkboxes"><li class="checkbox input-find-option"><label for="input-show-pending"><input type="checkbox" id="input-show-pending" checked=""> Pending</label></li><li class="checkbox input-find-option"><label for="input-show-loading"><input type="checkbox" id="input-show-loading" checked=""> Loading</label></li><li class="checkbox input-find-option"><label for="input-show-loaded"><input type="checkbox" id="input-show-loaded" checked=""> Loaded</label></li></ul></div><div class="form-group input-group"><div class="toggle-string"><label for="input-filter" class="bmd-label-floating toggle-string">Filter (regex)</label> <input type="text" class="form-control toggle-string" id="input-filter"></div><div class="input-group-append"><button class="btn btn-secondary dropdown-toggle" id="input-filter-button" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">CONTENT</button><div class="dropdown-menu toggle-dropdown"><a class="dropdown-item" id="input-filter-content">Content</a> <a class="dropdown-item" id="input-filter-filename">Filename</a></div></div></div><div class="form-group input-find-option" id="input-num-results-container"><label for="input-num-results" class="bmd-label-floating">Number of results</label> <input type="number" class="form-control" id="input-num-results" value="20" min="1"></div><div style="clear:both"></div><h6>Results</h6><ul id="input-search-results"></ul></div><div class="modal-footer"><button type="button" class="btn btn-primary" id="input-filter-refresh">Refresh</button> <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button></div></div></div></div><div class="modal fade" id="output-tab-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title">Find Output Tab</h5></div><div class="modal-body" id="output-tab-body"><h6>Bake Status</h6><div id="output-find-options"><ul id="output-find-options-checkboxes"><li class="checkbox output-find-option"><label for="output-show-pending"><input type="checkbox" id="output-show-pending" checked=""> Pending</label></li><li class="checkbox output-find-option"><label for="output-show-baking"><input type="checkbox" id="output-show-baking" checked=""> Baking</label></li><li class="checkbox output-find-option"><label for="output-show-baked"><input type="checkbox" id="output-show-baked" checked=""> Baked</label></li><li class="checkbox output-find-option"><label for="output-show-stale"><input type="checkbox" id="output-show-stale" checked=""> Stale</label></li><li class="checkbox output-find-option"><label for="output-show-errored"><input type="checkbox" id="output-show-errored" checked=""> Errored</label></li></ul><div class="form-group output-find-option"><label for="output-content-filter" class="bmd-label-floating">Content filter (regex)</label> <input type="text" class="form-control" id="output-content-filter"></div><div class="form-group output-find-option" id="output-num-results-container"><label for="output-num-results" class="bmd-label-floating">Number of results</label> <input type="number" class="form-control" id="output-num-results" value="20"></div></div><br><h6>Results</h6><ul id="output-search-results"></ul></div><div class="modal-footer"><button type="button" class="btn btn-primary" id="output-filter-refresh">Refresh</button> <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button></div></div></div></div><div class="modal fade" id="download-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content" data-help-proxy="a[data-target='#download-modal']"><div class="modal-header"><h5 class="modal-title">Download CyberChef</h5></div><div class="modal-body"><p>CyberChef runs entirely within your browser with no server-side component, meaning that your Input data and Recipe configuration are not sent anywhere, whether you use the live, official version of CyberChef or a downloaded, standalone version (assuming it is unmodified).</p><p>There are three operations that make calls to external services, those being the 'Show on map' operation which downloads map tiles from wikimedia.org, the 'DNS over HTTPS' operation which resolves DNS requests using either Google or Cloudflare services, and the 'HTTP request' operation that calls out to the configured URL you enter. You can confirm what network requests are made using your browser's developer console (F12) and viewing the Network tab.</p><p>If you would like to download your own standalone copy of CyberChef to run in a segregated network or where there is limited or no Internet connectivity, you can get a ZIP file containing the whole web app below. This can be run locally or hosted on a web server with no configuration required.</p><p>Be aware that the standalone version will never update itself, meaning it will not receive bug fixes or new features until you re-download newer versions manually.</p><h6>CyberChef v10.19.2</h6><ul><li>Build time: 14/08/2024 17:26:43 UTC</li><li>The changelog for this version can be viewed <a href="https://github.com/gchq/CyberChef/blob/v10.19.2/CHANGELOG.md">here</a></li><li>&copy; Crown Copyright 2016-2024</li><li>Released under the Apache Licence, Version 2.0</li><li>SHA256 hash: DOWNLOAD_HASH_PLACEHOLDER</li></ul><a href="CyberChef_v10.19.2.zip" download class="btn btn-outline-primary">Download ZIP file</a></div><div class="modal-footer"><button type="button" class="btn btn-primary" data-dismiss="modal">Ok</button></div></div></div></div><div class="modal fade" id="help-modal" tabindex="-1" role="dialog"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"><i class="material-icons modal-icon">info_outline</i> <span id="help-title"></span></h5></div><div class="modal-body"></div><div class="modal-footer"><button type="button" class="btn btn-primary" id="help-ok" data-dismiss="modal">Ok</button></div></div></div></div>
  <script>
    let href = window.location.href;
    let search = href.split('?')[1];
    let params = search.split('&');
    let node, session, type = 'src';
    for (let param of params) {
      if (param.startsWith('node')) {
        node = param.split('=')[1];
      } else if (param.startsWith('session')) {
        session = param.split('=')[1];
      } else if (param.startsWith('type')) {
        type = param.split('=')[1];
      }
    }

    let data;
    let interval;

    // fetch the data to populate the input
    fetch(`${node}/session/${session}?type=${type}`)
      .then((response) => {
        if (response.ok) {
          return response.json();
        } else {
          throw new Error('Error retrieving data');
        }
      })
      .then((result) => {
        data = result.data;
      })
      .catch((error) => {
        console.log('error', error);
      }).finally(() => {
        interval = setInterval(() => {
          if (typeof app !== 'undefined') {
            if (data) {
              app.manager.recipe.addOperation('From Hex');
              app.setInput(data);
            }

            clearInterval(interval);
          }
        }, 100);
      });

    setTimeout(() => {
      // reset the route params because cyberchef removes them
      // so a user can reload the page
      window.history.replaceState({ id: 'CyberChef' }, 'CyberChef', href);
    }, 2000);
  </script>
</body></html>
