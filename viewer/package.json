{"name": "arkime", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/arkime/arkime.git"}, "devDependencies": {"d3": "^7.7.0", "jquery": "^3.6.0", "save-svg-as-png": "^1.4.17"}, "scripts": {"bundle": "cd ../ && npm run viewer:bundle", "bundle:min": "cd ../ && npm run viewer:build", "addtestuser": "cd ../ && npm run viewer:addtestuser", "dev": "cd ../ && npm run viewer:dev", "doc": "cd ../ && npm run viewer:doc", "lint": "cd ../ && npm run viewer:lint", "test": "cd ../ && npm run viewer:testui"}, "engines": {"node": ">= 18.15.0 < 21", "npm": ">= 3.0.0"}, "nodemonConfig": {"ext": "js,json,jade,pug,styl", "ignore": ["node_modules", "vueap<PERSON>"], "watch": [".", "./views/", "../common/", "../tests/config.test.ini", "../capture/parsers/"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}