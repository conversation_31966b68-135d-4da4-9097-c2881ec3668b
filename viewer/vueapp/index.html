<!DOCTYPE html>
<html>
  <head>
    <base href="{{ path }}">
    <meta charset="utf-8">
    <meta name="referrer" content="no-referrer">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta property="csp-nonce" content="{{ nonce }}">
    <link rel="stylesheet" href="font-awesome/css/font-awesome.min.css">
    <title>Arkime</title>
    <link rel="stylesheet" href="{{ themeUrl }}">
    <script nonce="{{ nonce }}">
      const TITLE_CONFIG = '{{ titleConfig }}';
      const FOOTER_CONFIG = '{{ footerConfig }}';
      const DEMO_MODE = {{ demoMode }};
      const VERSION = '{{ version }}';
      const PATH = '{{ path }}';
      const MULTIVIEWER = {{ multiViewer }};
      const HASUSERSES = {{ hasUsersES }};
      const HUNTWARN = {{ huntWarn }};
      const HUNTLIMIT = {{ huntLimit }};
      const ANONYMOUS_MODE = {{ anonymousMode }};
      const BUSINESS_DAY_START = {{ businesDayStart }};
      const BUSINESS_DAY_END = {{ businessDayEnd }};
      const BUSINESS_DAYS = '{{ businessDays }}';
      const TURN_OFF_GRAPH_DAYS = {{ turnOffGraphDays }};
      const DISABLE_USER_PASSWORD_UI = {{ disableUserPasswordUI }};
      const LOGOUT_URL = '{{ logoutUrl }}';
      const DEFAULT_TIME_RANGE = '{{ defaultTimeRange }}';
      const SPIVIEW_CATEGORY_ORDER = '{{ spiViewCategoryOrder }}';
    </script>
  </head>
  <body class="{{ theme }}">
    <!--vue-ssr-outlet-->
  </body>
</html>
