/* parser generated by jison 0.4.18 */
/*
  Returns a Parser object of the following structure:

  Parser: {
    yy: {}
  }

  Parser.prototype: {
    yy: {},
    trace: function(),
    symbols_: {associative list: name ==> number},
    terminals_: {associative list: number ==> name},
    productions_: [...],
    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),
    table: [...],
    defaultActions: {...},
    parseError: function(str, hash),
    parse: function(input),

    lexer: {
        EOF: 1,
        parseError: function(str, hash),
        setInput: function(input),
        input: function(),
        unput: function(str),
        more: function(),
        less: function(n),
        pastInput: function(),
        upcomingInput: function(),
        showPosition: function(),
        test_match: function(regex_match_array, rule_index),
        next: function(),
        lex: function(),
        begin: function(condition),
        popState: function(),
        _currentRules: function(),
        topState: function(),
        pushState: function(condition),

        options: {
            ranges: boolean           (optional: true ==> token location info will include a .range[] member)
            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)
            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)
        },

        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),
        rules: [...],
        conditions: {associative list: name ==> set},
    }
  }


  token location info (@$, _$, etc.): {
    first_line: n,
    last_line: n,
    first_column: n,
    last_column: n,
    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)
  }


  the parseError function receives a 'hash' object with these members for lexer and parser errors: {
    text:        (matched text)
    token:       (the produced terminal token, if any)
    line:        (yylineno)
  }
  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {
    loc:         (yylloc)
    expected:    (string describing the set of expected tokens)
    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)
  }
*/
var arkimeparser = (function(){
var o=function(k,v,o,l){for(o=o||{},l=k.length;l--;o[k[l]]=v);return o},$V0=[1,6],$V1=[1,3],$V2=[1,4],$V3=[1,5],$V4=[1,8],$V5=[1,9],$V6=[5,18,19,23],$V7=[14,15,16,17];
var parser = {trace: function trace () { },
yy: {},
symbols_: {"error":2,"expressions":3,"e":4,"EOF":5,"OP":6,"lt":7,"lte":8,"gt":9,"gte":10,"==":11,"!=":12,"VALUE":13,"STR":14,"QUOTEDSTR":15,"REGEXSTR":16,"LIST":17,"&&":18,"||":19,"!":20,"-":21,"(":22,")":23,"EXISTS":24,"$accept":0,"$end":1},
terminals_: {2:"error",5:"EOF",7:"lt",8:"lte",9:"gt",10:"gte",11:"==",12:"!=",14:"STR",15:"QUOTEDSTR",16:"REGEXSTR",17:"LIST",18:"&&",19:"||",20:"!",21:"-",22:"(",23:")",24:"EXISTS"},
productions_: [0,[3,2],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[13,1],[13,1],[13,1],[13,1],[4,3],[4,3],[4,2],[4,2],[4,3],[4,3],[4,3],[4,3]],
performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {
/* this == yyval */

var $0 = $$.length - 1;
switch (yystate) {
case 1:
 return $$[$0-1]; 
break;
case 2:
this.$ = 'lt'
break;
case 3:
this.$ = 'lte'
break;
case 4:
this.$ = 'gt'
break;
case 5:
this.$ = 'gte'
break;
case 6:
this.$ = 'eq'
break;
case 7:
this.$ = 'ne'
break;
case 12:
this.$ = {bool: {filter: [$$[$0-2], $$[$0]]}};
break;
case 13:
this.$ = {bool: {should: [$$[$0-2], $$[$0]]}};
break;
case 14:
this.$ = {bool: {must_not: $$[$0]}};
break;
case 15:
this.$ = -$$[$0];
break;
case 16:
this.$ = $$[$0-1];
break;
case 17:
this.$ = formatExists(yy, $$[$0-2], "eq");
break;
case 18:
this.$ = formatExists(yy, $$[$0-2], "ne");
break;
case 19:
 this.$ = formatQuery(yy, $$[$0-2], $$[$0-1], $$[$0]);
          //console.log(util.inspect(this.$, false, 50));
        
break;
}
},
table: [{3:1,4:2,14:$V0,20:$V1,21:$V2,22:$V3},{1:[3]},{5:[1,7],18:$V4,19:$V5},{4:10,14:$V0,20:$V1,21:$V2,22:$V3},{4:11,14:$V0,20:$V1,21:$V2,22:$V3},{4:12,14:$V0,20:$V1,21:$V2,22:$V3},{6:15,7:[1,16],8:[1,17],9:[1,18],10:[1,19],11:[1,13],12:[1,14]},{1:[2,1]},{4:20,14:$V0,20:$V1,21:$V2,22:$V3},{4:21,14:$V0,20:$V1,21:$V2,22:$V3},o($V6,[2,14]),o($V6,[2,15]),{18:$V4,19:$V5,23:[1,22]},o($V7,[2,6],{24:[1,23]}),o($V7,[2,7],{24:[1,24]}),{13:25,14:[1,26],15:[1,27],16:[1,28],17:[1,29]},o($V7,[2,2]),o($V7,[2,3]),o($V7,[2,4]),o($V7,[2,5]),o($V6,[2,12]),o([5,19,23],[2,13],{18:$V4}),o($V6,[2,16]),o($V6,[2,17]),o($V6,[2,18]),o($V6,[2,19]),o($V6,[2,8]),o($V6,[2,9]),o($V6,[2,10]),o($V6,[2,11])],
defaultActions: {7:[2,1]},
parseError: function parseError (str, hash) {
    if (hash.recoverable) {
        this.trace(str);
    } else {
        var error = new Error(str);
        error.hash = hash;
        throw error;
    }
},
parse: function parse(input) {
    var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = '', yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;
    var args = lstack.slice.call(arguments, 1);
    var lexer = Object.create(this.lexer);
    var sharedState = { yy: {} };
    for (var k in this.yy) {
        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
            sharedState.yy[k] = this.yy[k];
        }
    }
    lexer.setInput(input, sharedState.yy);
    sharedState.yy.lexer = lexer;
    sharedState.yy.parser = this;
    if (typeof lexer.yylloc == 'undefined') {
        lexer.yylloc = {};
    }
    var yyloc = lexer.yylloc;
    lstack.push(yyloc);
    var ranges = lexer.options && lexer.options.ranges;
    if (typeof sharedState.yy.parseError === 'function') {
        this.parseError = sharedState.yy.parseError;
    } else {
        this.parseError = Object.getPrototypeOf(this).parseError;
    }
    function popStack(n) {
        stack.length = stack.length - 2 * n;
        vstack.length = vstack.length - n;
        lstack.length = lstack.length - n;
    }
    _token_stack:
        var lex = function () {
            var token;
            token = lexer.lex() || EOF;
            if (typeof token !== 'number') {
                token = self.symbols_[token] || token;
            }
            return token;
        };
    var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;
    while (true) {
        state = stack[stack.length - 1];
        if (this.defaultActions[state]) {
            action = this.defaultActions[state];
        } else {
            if (symbol === null || typeof symbol == 'undefined') {
                symbol = lex();
            }
            action = table[state] && table[state][symbol];
        }
                    if (typeof action === 'undefined' || !action.length || !action[0]) {
                var errStr = '';
                expected = [];
                for (p in table[state]) {
                    if (this.terminals_[p] && p > TERROR) {
                        expected.push('\'' + this.terminals_[p] + '\'');
                    }
                }
                if (lexer.showPosition) {
                    errStr = 'Parse error on line ' + (yylineno + 1) + ':\n' + lexer.showPosition() + '\nExpecting ' + expected.join(', ') + ', got \'' + (this.terminals_[symbol] || symbol) + '\'';
                } else {
                    errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : '\'' + (this.terminals_[symbol] || symbol) + '\'');
                }
                this.parseError(errStr, {
                    text: lexer.match,
                    token: this.terminals_[symbol] || symbol,
                    line: lexer.yylineno,
                    loc: yyloc,
                    expected: expected
                });
            }
        if (action[0] instanceof Array && action.length > 1) {
            throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);
        }
        switch (action[0]) {
        case 1:
            stack.push(symbol);
            vstack.push(lexer.yytext);
            lstack.push(lexer.yylloc);
            stack.push(action[1]);
            symbol = null;
            if (!preErrorSymbol) {
                yyleng = lexer.yyleng;
                yytext = lexer.yytext;
                yylineno = lexer.yylineno;
                yyloc = lexer.yylloc;
                if (recovering > 0) {
                    recovering--;
                }
            } else {
                symbol = preErrorSymbol;
                preErrorSymbol = null;
            }
            break;
        case 2:
            len = this.productions_[action[1]][1];
            yyval.$ = vstack[vstack.length - len];
            yyval._$ = {
                first_line: lstack[lstack.length - (len || 1)].first_line,
                last_line: lstack[lstack.length - 1].last_line,
                first_column: lstack[lstack.length - (len || 1)].first_column,
                last_column: lstack[lstack.length - 1].last_column
            };
            if (ranges) {
                yyval._$.range = [
                    lstack[lstack.length - (len || 1)].range[0],
                    lstack[lstack.length - 1].range[1]
                ];
            }
            r = this.performAction.apply(yyval, [
                yytext,
                yyleng,
                yylineno,
                sharedState.yy,
                action[1],
                vstack,
                lstack
            ].concat(args));
            if (typeof r !== 'undefined') {
                return r;
            }
            if (len) {
                stack = stack.slice(0, -1 * len * 2);
                vstack = vstack.slice(0, -1 * len);
                lstack = lstack.slice(0, -1 * len);
            }
            stack.push(this.productions_[action[1]][0]);
            vstack.push(yyval.$);
            lstack.push(yyval._$);
            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
            stack.push(newState);
            break;
        case 3:
            return true;
        }
    }
    return true;
}};

const util = require('util');
const moment = require('moment');

/* Given a field name, if prefixed with 'db:' return dbFieldsMap entry (i.e., looked up according to
 * the OpenSearch/Elasticsearch field name); otherwise return fieldsMap entry (see #1461)
 */
function getFieldInfo (yy, field) {
  let info = null;

  if (field.startsWith('db:')) {
    const dbField = field.substring(3);
    if (yy.dbFieldsMap[dbField]) {
      info = yy.dbFieldsMap[dbField];
    }
  } else if (yy.fieldsMap[field]) {
    info = yy.fieldsMap[field];
  }

  // console.log('getFieldInfo', field, info);
  return info;
}

/* Build a list of all the field infos for ip field types.
 * Can specify if a port field needs to be available for the type or not
 */
function getIpInfoList (yy, needPort) {
  const ors = [];
  const completed = {};

  for (const field in yy.fieldsMap) {
    const info = yy.fieldsMap[field];

    // If ip itself or not an ip field stop
    if (field === 'ip' || info.type !== 'ip') { continue; }

    // Already completed
    if (completed[info.dbField]) { continue; }
    completed[info.dbField] = 1;

    // If port specified then skip ips without ports
    if (needPort && !info.portField) {
      continue;
    }

    if (info.requiredRight && yy[info.requiredRight] !== true) {
      continue;
    }

    ors.push(info);
  }

  return ors.sort((a, b) => { return a.exp.localeCompare(b.exp); });
}

function getRegexInfoList (yy, info) {
  const regex = new RegExp(info.regex);
  const ors = [];
  const completed = [];
  for (const f in yy.fieldsMap) {
    if (f.match(regex) && !completed[yy.fieldsMap[f].dbField]) {
      if (yy.fieldsMap[f].requiredRight && yy[yy.fieldsMap[f].requiredRight] !== true) {
        continue;
      }
      ors.push(yy.fieldsMap[f]);
      completed[yy.fieldsMap[f].dbField] = 1;
    }
  }

  return ors.sort((a, b) => { return a.exp.localeCompare(b.exp); });
}

/* Do all the magic around ip field parsing.
 * Supports many formats such as
 * ip
 * ip/cidr
 * ip1 = ip1.0.0.0/24
 * ip1.ip2 = ip1.ip2.0.0/16
 * ip1.ip2.ip3 = ip1.ip2.ip3.0/8
 * All of the above with a :port(v4) or .port(v6) at the end
 * Arrays of all of the above
 */
function parseIpPort (yy, field, ipPortStr) {
  const dbField = getFieldInfo(yy, field).dbField;

  // Have just a single Ip, create obj for it
  function parseSingleIp (exp, singleDbField, singleIp, singlePort) {
    let singleObj;

    if (typeof (singlePort) === 'string' && singlePort.match(/[^0-9]/)) {
      throw singlePort + ' not a valid singlePort';
    }

    if (singleIp !== undefined) {
      const colon = singleIp.indexOf(':');
      if ((colon === -1 && singleIp.match(/[^.0-9/]/)) || // IP4
          (colon !== -1 && singleIp.match(/[^a-fA-F:0-9/]/)) || // IP6
          singleIp.match(/\/.*[^0-9]/)) { // CIDR
        throw singleIp + ' not a valid ip';
      }

      singleObj = { term: {} };
      singleObj.term[singleDbField] = singleIp;
    }

    if (singlePort !== -1) {
      const expInfo = getFieldInfo(yy, exp);
      if (expInfo.portField) {
        singleObj = { bool: { filter: [singleObj, { term: {} }] } };
        singleObj.bool.filter[1].term[expInfo.portField] = singlePort;
      } else {
        throw exp + " doesn't support port";
      }

      if (singleIp === undefined) {
        singleObj = singleObj.bool.filter[1];
      }
    }

    return singleObj;
  }

  // Special case of ip=
  function parseAllIp (allIp, allPort) {
    const infos = getIpInfoList(yy, allPort !== -1);

    const ors = [];
    for (const info of infos) {
      const allObj = parseSingleIp(info.exp, info.dbField, allIp, allPort);
      if (allObj) {
        ors.push(allObj);
      }
    }

    return { bool: { should: ors } };
  }

  let obj;

  ipPortStr = ipPortStr.trim();

  // We really have a list of them
  if (isArrayFull(ipPortStr)) {
    const list = ListToArrayMap(ipPortStr, str => parseIpPort(yy, field, str));

    // Optimize 1 item in array
    if (list.length === 1) {
      return list[0]
    }

    if (isArrayAND(ipPortStr)) {
      obj = { bool: { filter: list } };
    } else {
      obj = { bool: { should: list } };
    }
    return obj;
  }

  // Support ':80' and '.80'
  if ((ipPortStr[0] === ':' && ipPortStr[0] !== ':') ||
      (ipPortStr[0] === '.')) {
    if (dbField !== 'ipall') {
      return parseSingleIp(field, dbField, undefined, +ipPortStr.slice(1));
    } else {
      return parseAllIp(undefined, +ipPortStr.slice(1));
    }
  }

  // Support ip4: '***********' '10.10.10/16:80' '10.10.10:80' '10.10.10/16'
  // Support ip6: '1::2' '1::2/16.80' '1::2.80' '1::2/16'
  let ip;
  let port = -1;
  const colons = ipPortStr.split(':');

  if (ipPortStr.includes('::')) { // Double colon is ip6 with 0 filled
    // Everything after . is port
    const dots = ipPortStr.split('.');
    if (dots.length > 1 && dots[1] !== '') {
      port = dots[1];
    }
    // Everything before . is ip and slash
    ip = dots[0];
  } else if (colons.length > 2) { // More than 1 colon is ip6
    // Everything after . is port
    const dots = ipPortStr.split('.');
    if (dots.length > 1 && dots[1] !== '') {
      port = dots[1];
    }

    const slash = dots[0].split('/');
    const colons2 = slash[0].split(':');

    // If the last one is empty just pretend : isn't there, for auto complete
    if (colons2[colons2.length - 1] === '') {
      colons2.length--;
    }

    if (slash[1] === undefined) {
      console.log(colons2.length, colons2, colons2.length, 16*colons2.length);
      slash[1] = `${16*colons2.length}`;
    }

    if (colons2.length < 8) {
      ip = colons2.join(':') + '::';
    } else {
      ip = colons2.join(':');
    }


    // Add the slash back to the ip
    if (slash[1] && slash[1] !== '128') {
      ip = `${ip}/${slash[1]}`;
    }
  } else {
    // everything after : is port
    if (colons.length > 1 && colons[1] !== '') {
      port = colons[1];
    }

    // Have to do extra because we allow shorthand for /8, /16, /24
    const slash = colons[0].split('/');
    const dots = slash[0].split('.');

    // If the last one is empty just pretend . isn't there, for auto complete
    if (dots[dots.length - 1] === '') {
      dots.length--;
    }

    switch (dots.length) {
    case 4:
      ip = `${dots[0]}.${dots[1]}.${dots[2]}.${dots[3]}`;
      break;
    case 3:
      ip = `${dots[0]}.${dots[1]}.${dots[2]}.0`;
      if (slash[1] === undefined) { slash[1] = '24'; }
      break;
    case 2:
      ip = `${dots[0]}.${dots[1]}.0.0`;
      if (slash[1] === undefined) { slash[1] = '16'; }
      break;
    case 1:
      if (dots[0].length > 0) {
        ip = `${dots[0]}.0.0.0`;
        if (slash[1] === undefined) { slash[1] = '8'; }
      }
      break;
    }

    // Add the slash back to the ip
    if (slash[1] && slash[1] !== '32') {
      ip = `${ip}/${slash[1]}`;
    }
  }

  if (dbField !== 'ipall') {
    return parseSingleIp(field, dbField, ip, port);
  } else {
    return parseAllIp(ip, port);
  }
}

function stripQuotes (str) {
  if (str[0] === '"') {
    str = str.substring(1, str.length - 1);
  }
  return str;
}

function formatExists (yy, field, op) {
  const info = getFieldInfo(yy, field);
  if (!info) { throw 'Unknown field ' + field; }

  if (info.requiredRight && yy[info.requiredRight] !== true) {
    throw field + ' - permission denied, ask your Arkime admin to give you access using + on Users tab';
  }

  if (info.regex) {
    const regex = new RegExp(info.regex);
    const obj = [];
    const completed = [];
    for (const f in yy.fieldsMap) {
      if (f.match(regex) && !completed[yy.fieldsMap[f].dbField]) {
        if (yy.fieldsMap[f].requiredRight && yy[yy.fieldsMap[f].requiredRight] !== true) {
          continue;
        }
        obj.push(formatExists(yy, f, 'eq'));
        completed[yy.fieldsMap[f].dbField] = 1;
      }
    }
    if (op === 'ne') {
      return { bool: { must_not: obj } };
    } else {
      return { bool: { should: obj } };
    }
  }

  if (op === 'ne') {
    return { bool: { must_not: { exists: { field: field2Raw(yy, field) } } } };
  }

  return { exists: { field: field2Raw(yy, field) } };
}

function formatQuery (yy, field, op, value, completed) {
  completed ??= {};
  // console.log("field", field, "op", op, "value", value);
  // console.log("yy", util.inspect(yy, false, 50));
  if (value[0] === '/' && value[value.length - 1] === '/') {
    checkRegex(value);
  }

  const info = getFieldInfo(yy, field);
  if (!info) { throw 'Unknown field ' + field; }

  if (info.requiredRight && yy[info.requiredRight] !== true) {
    throw field + ' - permission denied, ask your Arkime admin to give you access using + on Users tab';
  }

  const nonShortcuts = ListToArray(value, true);

  const shortcutsObj = formatShortcutsQuery(yy, field, op, value);
  if (nonShortcuts.length === 0) { return shortcutsObj; }

  const normalObj = formatNormalQuery(yy, field, op, value, completed);
  if (!shortcutsObj) { return normalObj; }

  if (op === 'eq') { return { bool: { should: [normalObj, shortcutsObj] } }; } else { return { bool: { must_not: [normalObj, shortcutsObj] } }; }
}

function formatShortcutsQuery (yy, field, op, value, shortcutParent) {
  const shortcuts = ListToArrayShortcuts(yy, value);
  if (shortcuts.length === 0) { return undefined; }

  if (isArrayAND(value)) { throw value + ' - AND array not supported with shortcuts'; }

  let obj;
  const info = getFieldInfo(yy, field);

  if (op !== 'eq' && op !== 'ne') {
    throw 'Shortcuts only support == and !=';
  }

  obj = { bool: {} };

  let operation = 'should';
  if (op === 'ne') {
    operation = 'must_not';
  }
  obj.bool[operation] = [];

  shortcuts.forEach(function (shortcut) {
    shortcut = value = shortcut.substr(1); /* remove $ */
    if (!yy.shortcuts || !yy.shortcuts[shortcut]) {
      throw shortcut + ' - Shortcut not found';
    }

    shortcut = yy.shortcuts[shortcut];

    const type = info.type2 || info.type;
    const shortcutType = yy.shortcutTypeMap[type];

    if (!shortcutType) {
      throw 'Unsupported field type: ' + type;
    }

    if (!shortcut._source[shortcutType]) {
      throw 'shortcut must be of type ' + shortcutType;
    }

    const terms = {};

    switch (type) {
    case 'ip':
      if (field === 'ip') {
        const infos = getIpInfoList(yy, false);
        for (const ipInfo of infos) {
          const newObj = formatShortcutsQuery(yy, ipInfo.exp, op, '$' + value, obj);
          if (newObj) {
            obj.bool[operation].concat(newObj);
          }
        }
      } else {
        terms[info.dbField] = {
          index: `${yy.prefix}lookups`,
          id: shortcut._id,
          path: 'ip'
        };
        if (shortcutParent) {
          obj = shortcutParent;
        }
        obj.bool[operation].push({ terms });
      }
      break;
    case 'integer':
      terms[info.dbField] = {
        index: `${yy.prefix}lookups`,
        id: shortcut._id,
        path: 'number'
      };
      obj.bool[operation].push({ terms });
      break;
    case 'lotermfield':
    case 'lotextfield':
    case 'termfield':
    case 'textfield':
    case 'uptermfield':
    case 'uptextfield':
      if (info.regex) {
        const infos = getRegexInfoList(yy, info);
        for (const i of infos) {
          const terms = {};
          terms[i.dbField] = {
            index: `${yy.prefix}lookups`,
            id: shortcut._id,
            path: 'string'
          };
          obj.bool[operation].push({ terms });
        }
      } else {
        terms[info.dbField] = {
          index: `${yy.prefix}lookups`,
          id: shortcut._id,
          path: 'string'
        };
        obj.bool[operation].push({ terms });
      }
      break;
    default:
      throw 'Unsupported field type: ' + type;
    }
  });

  if (obj?.bool?.should?.length === 1) {
    obj = obj.bool.should[0];
  }

  return obj;
}

function formatNormalQuery (yy, field, op, value, completed) {
  let obj;
  const info = getFieldInfo(yy, field);

  if (info.regex) {
    const infos = getRegexInfoList(yy, info);
    obj = [];
    for (const i of infos) {
      if (completed[i.dbField]) { continue; }
      completed[i.dbField] = 1;
      obj.push(formatQuery(yy, i.exp, (op === 'ne' ? 'eq' : op), value, completed));
    }

    if (op === 'ne') {
      return { bool: { must_not: obj } };
    } else {
      return { bool: { should: obj } };
    }
  }

  switch (info.type2 || info.type) {
  case 'ip':
    if (value[0] === '/') { throw value + ' - Regex not supported for ip queries'; }

    if (value.indexOf('*') !== -1) { throw value + ' - Wildcard not supported for ip queries'; }

    if (value === 'ipv4') {
      value = '0.0.0.0/0';
    } if (value === 'ipv6') {
      value = '0.0.0.0/0';
      if (op === 'ne') { op = 'eq'; } else { op = 'ne'; }
    }

    if (op === 'eq') {
      return parseIpPort(yy, field, value);
    }
    if (op === 'ne') {
      return { bool: { must_not: parseIpPort(yy, field, value) } };
    }

    if (isArrayStart(value)) { throw value + ' - List queries not supported for gt/lt queries - ' + value; }

    obj = { range: {} };
    obj.range[info.dbField] = {};
    obj.range[info.dbField][op] = value;
    return obj;
  case 'integer':
    if (value[0] === '/') { throw value + ' - Regex queries not supported for integer queries'; }

    if (op === 'eq' || op === 'ne') {
      obj = termOrTermsInt(info.dbField, value);
      if (op === 'ne') {
        obj = { bool: { must_not: obj } };
      }
      return obj;
    }

    if (isArrayStart(value)) { throw value + ' - List queries not supported for gt/lt queries - ' + value; }

    obj = { range: {} };
    obj.range[info.dbField] = {};
    obj.range[info.dbField][op] = parseInt(value);
    return obj;
  case 'float':
    if (value[0] === '/') { throw value + ' - Regex queries not supported for float queries'; }

    if (op === 'eq' || op === 'ne') {
      obj = termOrTermsFloat(info.dbField, value);
      if (op === 'ne') {
        obj = { bool: { must_not: obj } };
      }
      return obj;
    }

    if (isArrayStart(value)) { throw value + ' - List queries not supported for gt/lt queries - ' + value; }

    obj = { range: {} };
    obj.range[info.dbField] = {};
    obj.range[info.dbField][op] = parseFloat(value);
    return obj;
  case 'lotermfield':
  case 'lotextfield':
    if (op === 'eq') { return stringQuery(yy, field, value.toLowerCase()); }
    if (op === 'ne') { return { bool: { must_not: stringQuery(yy, field, value.toLowerCase()) } }; }
    throw "Invalid operator '" + op + "' for " + field;
  case 'termfield':
  case 'textfield':
    if (op === 'eq') { return stringQuery(yy, field, value); }
    if (op === 'ne') { return { bool: { must_not: stringQuery(yy, field, value) } }; }
    throw "Invalid operator '" + op + "' for " + field;
  case 'uptermfield':
  case 'uptextfield':
    if (op === 'eq') { return stringQuery(yy, field, value.toUpperCase()); }
    if (op === 'ne') { return { bool: { must_not: stringQuery(yy, field, value.toUpperCase()) } }; }
    throw "Invalid operator '" + op + "' for " + field;
  case 'fileand':
    if (isArrayStart(value)) { throw value + ' - List queries not supported for file queries - ' + value; }

    if (op === 'eq') { return { fileand: stripQuotes(value) }; }
    if (op === 'ne') { return { bool: { must_not: { fileand: stripQuotes(value) } } }; }
    throw op + ' - not supported for file queries';
  case 'viewand':
    if (isArrayStart(value)) { throw value + ' - List queries not supported for view queries - ' + value; }

    value = stripQuotes(value);
    if (!yy.views || !yy.views[value]) { throw value + ' - View not found for user'; }

    if (op === 'eq') { return exports.parse(yy.views[value].expression); }
    if (op === 'ne') { return { bool: { must_not: exports.parse(yy.views[value].expression) } }; }
    throw op + ' - not supported for view queries';
  case 'seconds':
    if (value[0] === '/') { throw value + ' - Regex queries not supported for date queries'; }

    if (op === 'eq' || op === 'ne') {
      obj = termOrTermsSeconds(info.dbField, value);
      if (op === 'ne') {
        obj = { bool: { must_not: obj } };
      }
      return obj;
    }

    if (isArrayStart(value)) { throw value + ' - List queries not supported for gt/lt queries - ' + value; }

    obj = { range: {} };
    obj.range[info.dbField] = {};
    obj.range[info.dbField][op] = parseSeconds(stripQuotes(value));
    return obj;
  case 'date':
    if (value[0] === '/') { throw value + ' - Regex queries not supported for date queries'; }

    if (op === 'eq' || op === 'ne') {
      obj = termOrTermsDate(info.dbField, value);
      if (op === 'ne') {
        obj = { bool: { must_not: obj } };
      }
      return obj;
    }

    if (isArrayStart(value)) { throw value + ' - List queries not supported for gt/lt queries - ' + value; }

    obj = { range: {} };
    obj.range[info.dbField] = {};
    obj.range[info.dbField][op] = moment.unix(parseSeconds(stripQuotes(value))).format();
    return obj;
  default:
    throw 'Unknown field type: ' + info.type;
  }
}

function checkRegex (str) {
  let m;
  if ((m = str.match(/^\/(?:\\?.)*?\//)) && m[0].length !== str.length) {
    throw 'Must back slash any forward slashes in regexp query - ' + m[0];
  }
}

function field2Raw (yy, field) {
  const info = getFieldInfo(yy, field);
  const dbField = info.dbField;
  if (info.rawField) { return info.rawField; }

  if (dbField.indexOf('.snow', dbField.length - 5) === 0) { return dbField.substring(0, dbField.length - 5) + '.raw'; }

  return dbField;
}

function stringQuery (yy, field, str) {
  const info = getFieldInfo(yy, field);
  let dbField = info.dbField;
  let obj;

  if (str[0] === '/' && str[str.length - 1] === '/') {
    checkRegex(str);

    str = str.substring(1, str.length - 1);
    if (info.transform) {
      str = global.arkime[info.transform](str).replace(/2e/g, '.');
    }
    dbField = field2Raw(yy, field);
    obj = { regexp: {} };
    obj.regexp[dbField] = str.replace(/\\(.)/g, '$1');
    return obj;
  }

  let quoted = false;
  if (str[0] === '"' && str[str.length - 1] === '"') {
    str = str.substring(1, str.length - 1).replace(/\\(")/g, '$1');
    quoted = true;
  } else if (isArrayFull(str)) {
    const rawField = field2Raw(yy, field);
    const strs = ListToArrayMap(str, global.arkime[info.transform]);

    const items = [];
    let terms = null;
    strs.forEach(function (astr) {
      let item;

      if (typeof astr === 'string' && astr[0] === '/' && astr[astr.length - 1] === '/') {
        checkRegex(astr);

        item = { regexp: {} };
        item.regexp[rawField] = astr.substring(1, astr.length - 1);
        items.push(item);
      } else if (typeof astr === 'string' && astr.indexOf('*') !== -1) {
        if (astr === '*') {
          throw "Please use 'EXISTS!' instead of a '*' in expression";
        }

        item = { wildcard: {} };
        item.wildcard[rawField] = astr;
        items.push(item);
      } else {
        if (astr[0] === '"' && astr[astr.length - 1] === '"') {
          astr = astr.substring(1, astr.length - 1).replace(/\\(.)/g, '$1');
        }

        if (info.type.match(/termfield/)) {
          if (isArrayAND(str)) {
            item = { term: {} };
            item.term[dbField] = astr;
            items.push(item);
            return;
          }

          // Reuse same terms element for all terms query and add to items once
          if (terms === null) {
            terms = { terms: {} };
            terms.terms[dbField] = [];
            items.push(terms);
          }
          terms.terms[dbField].push(astr);
        } else {
          item = { match_phrase: {} };
          item.match_phrase[dbField] = astr;
          items.push(item);
        }
      }
    }); // forEach

    if (items.length === 1) {
      obj = items[0];
    } else if (isArrayAND(str)) {
      obj = { bool: { filter: items } };
    } else {
      obj = { bool: { should: items } };
    }

    return obj;
  }

  if (info.transform) {
    str = global.arkime[info.transform](str);
  }

  if (!isNaN(str) && !quoted) {
    obj = { term: {} };
    obj.term[dbField] = str;
  } else if (typeof str === 'string' && str.indexOf('*') !== -1) {
    if (str === '*') {
      throw "Please use 'EXISTS!' instead of a '*' in expression";
    }
    dbField = field2Raw(yy, field);
    obj = { wildcard: {} };
    obj.wildcard[dbField] = str;
  } else if (info.type.match(/textfield/)) {
    obj = { match_phrase: {} };
    obj.match_phrase[dbField] = str;
  } else if (info.type.match(/termfield/)) {
    obj = { term: {} };
    obj.term[dbField] = str;
  }

  return obj;
}

if (!global.arkime) global.arkime = {};
global.arkime.utf8ToHex = function (utf8) {
  let hex = Buffer.from(stripQuotes(utf8)).toString('hex').toLowerCase();
  hex = hex.replace(/2a/g, '*');
  return hex;
};

global.arkime.dash2Colon = function (str) {
  return str.replace(/-/g, ':');
};

const protocols = {
  icmp: 1,
  igmp: 2,
  tcp: 6,
  udp: 17,
  gre: 47,
  esp: 50,
  icmp6: 58,
  icmpv6: 58,
  ospf: 89,
  pim: 103,
  sctp: 132
};

global.arkime.ipProtocolLookup = function (text) {
  if (typeof text !== 'string') {
    for (let i = 0; i < text.length; i++) {
      if (!protocols[text[i]] && isNaN(text[i])) { throw ('Unknown protocol string ' + text); }
      text[i] = protocols[text[i]] || +text[i];
    }
    return text;
  } else {
    if (!protocols[text] && isNaN(text)) { throw ('Unknown protocol string ' + text); }
    return protocols[text] || +text;
  }
};

// Remove the "http://", "https://", etc from http.uri queries
global.arkime.removeProtocol = function (text) {
  if (text[0] === '/' && text[text.length - 1] === '/') {
    return text;
  }
  text = text.replace(/^[a-z]+:\/\//i, '');
  return text;
};

// Remove the "http://", "https://" and after the first slash, etc from host queries
global.arkime.removeProtocolAndURI = function (text) {
  if (text[0] === '/' && text[text.length - 1] === '/') {
    return text;
  }
  text = text.replace(/^[a-z]+:\/\//i, '');
  text = text.replace(/\/.*/, '');
  return text;
};

function isArrayAND(value) {
  return (value[0] === ']');
}

function isArrayStart(value) {
  if (value[0] === '[' || value[0] === ']') {
    return true;
  }
  return false;
}

function isArrayFull(value) {
  if (value[0] === '[' && value[value.length - 1] === ']') {
    return true;
  }
  if (value[0] === ']' && value[value.length - 1] === '[') {
    return true;
  }
  return false;
}


function ListToArray (text, always) {
  if (isArrayFull(text)) {
    text = text.substring(1, text.length - 1);
  } else if (!always) {
    return text;
  }

  // JS doesn't have negative look behind
  const strs = text.replace(/\\\\/g, '**BACKSLASH**').replace(/\\,/g, '**COMMA**').split(/\s*,\s*/).filter(part => part.trim()[0] !== '$');
  for (let i = 0; i < strs.length; i++) {
    strs[i] = strs[i].replace('**COMMA**', ',').replace('**BACKSLASH**', '\\');
  }
  return strs;
}

function ListToArrayMap (text, mapCb, always) {
  let list = ListToArray(text, always);

  if (!mapCb || !Array.isArray(list)) { return list; }

  return list.map(mapCb);
}

function ListToArrayShortcuts (yy, text) {
  if (isArrayFull(text)) {
    text = text.substring(1, text.length - 1);
  }

  // JS doesn't have negative look behind
  const strs = text.replace(/\\\\/g, '**BACKSLASH**').replace(/\\,/g, '**COMMA**').split(/\s*,\s*/).filter(part => part.trim()[0] === '$');
  const nstrs = [];
  for (let i = 0; i < strs.length; i++) {
    const str = strs[i].replace('**COMMA**', ',').replace('**BACKSLASH**', '\\');

    if (str.match(/[*?]/)) {
      const re = new RegExp('^' + str.substring(1).replace(/\*/g, '.*').replace(/\?/g, '.') + '$');
      Object.keys(yy.shortcuts).forEach((s) => {
        if (s.match(re)) { nstrs.push('$' + s); }
      });
    } else {
      nstrs.push(str);
    }
  }

  return nstrs;
}

function termOrTermsInt (dbField, str) {
  let obj = {};
  if (isArrayFull(str)) {
    if (isArrayAND(str)) {
      obj.bool = {
        filter: ListToArray(str).map(x => {
          const o = { term: {} };
          o.term[dbField] = parseInt(x);
          return o;
        })
      };
      return obj;
    }

    obj = { terms: {} };
    obj.terms[dbField] = ListToArray(str).map(x => parseInt(x));
  } else {
    str = stripQuotes(str);
    let match;
    if ((match = str.match(/(-?\d+)-(-?\d+)/))) {
      obj = { range: {} };
      obj.range[dbField] = { gte: parseInt(match[1]), lte: parseInt(match[2]) };
      return obj;
    } else if (str.match(/[^\d]+/)) {
      throw str + ' is not a number';
    }
    obj = { term: {} };
    obj.term[dbField] = parseInt(str);
  }
  return obj;
}

function termOrTermsFloat (dbField, str) {
  let obj = {};
  if (isArrayFull(str)) {
    if (isArrayAND(str)) {
      obj.bool = {
        filter: ListToArray(str).map(x => {
          const o = { term: {} };
          o.term[dbField] = parseFloat(x);
          return o;
        })
      };
      return obj;
    }

    obj = { terms: {} };
    obj.terms[dbField] = ListToArray(str).map(x => parseFloat(x));
  } else {
    str = stripQuotes(str);
    let match;
    if (str.match(/^-?\d+$/) || str.match(/^-?\d+\.\d+$/)) {
      // good non range
    } else if ((match = str.match(/(-?\d*\.?\d*)-(-?\d*\.?\d*)/))) {
      obj = { range: {} };
      obj.range[dbField] = { gte: parseFloat(match[1]), lte: parseFloat(match[2]) };
      return obj;
    } else if (!(str.match(/^-?\d+$/) || str.match(/^-?\d+\.\d+$/))) {
      throw str + ' is not a float';
    }
    obj = { term: {} };
    obj.term[dbField] = parseFloat(str);
  }
  return obj;
}

function termOrTermsSeconds (dbField, str) {
  let obj = {};
  if (isArrayFull(str)) {
    if (isArrayAND(str)) {
      obj.bool = {
        filter: ListToArray(str).map(x => {
          const o = { term: {} };
          o.term[dbField] = parseSeconds(x);
          return o;
        })
      };
      return obj;
    }

    obj = { terms: {} };
    obj.terms[dbField] = ListToArray(str).map(x => parseSeconds(x));
  } else {
    str = parseSeconds(stripQuotes(str));
    obj = { term: {} };
    obj.term[dbField] = str;
  }
  return obj;
}

// This uses weird gte/lte range of the same date because if you give a second
// date, you want everything that happen from 0ms-1000ms, not just at 0ms
function termOrTermsDate (dbField, str) {
  if (isArrayFull(str)) {
    const items = []
    ListToArray(str).forEach(function (astr) {
      const d = moment.unix(parseSeconds(stripQuotes(astr))).format();
      const r = { range: {} };
      r.range[dbField] = { gte: d, lte: d };
      items.push(r);
    });


    if (isArrayAND(str)) {
      return { bool: { filter: items } };
    } else {
      return { bool: { should: items } };
    }
  }

  const d = moment.unix(parseSeconds(stripQuotes(str))).format();
  const obj = { range: {} };
  obj.range[dbField] = { gte: d, lte: d };
  return obj;
}

function str2format (str) {
  if (str.match(/^(s|sec|secs|second|seconds)$/)) {
    return 'seconds';
  } else if (str.match(/^(m|min|mins|minute|minutes)$/)) {
    return 'minutes';
  } else if (str.match(/^(h|hr|hrs|hour|hours)$/)) {
    return 'hours';
  } else if (str.match(/^(d|day|days)$/)) {
    return 'days';
  } else if (str.match(/^(w|week|weeks)\d*$/)) {
    return 'weeks';
  } else if (str.match(/^(M|mon|mons|month|months)$/)) {
    return 'months';
  } else if (str.match(/^(q|qtr|qtrs|quarter|quarters)$/)) {
    return 'quarters';
  } else if (str.match(/^(y|yr|yrs|year|years)$/)) {
    return 'years';
  }
  return undefined;
}

function parseSeconds (str) {
  let m, n;
  if ((m = str.match(/^([+-])(\d*)([a-z]*)([@]*)([a-z0-9]*)/))) {
    const d = moment();
    const format = str2format(m[3]);
    const snap = str2format(m[5]);

    if (m[2] === '') {
      m[2] = 1;
    }

    if (snap) {
      d.startOf(snap);
      if ((n = m[5].match(/^(w|week|weeks)(\d+)$/))) {
        d.day(n[2]);
      }
    }

    d.add((m[1] === '-' ? -1 : 1) * m[2], format);
    return d.unix();
  }

  if ((m = str.match(/^@([a-z0-9]+)/))) {
    const d = moment();
    const snap = str2format(m[1]);

    d.startOf(snap);
    if ((n = m[1].match(/^(w|week|weeks)(\d+)$/))) {
      d.day(n[2]);
    }
    return d.unix();
  }

  return new moment(str, ['YYYY/MM/DD HH:mm:ss', 'YYYY/MM/DD HH:mm:ss Z', moment.ISO_8601]).unix();
}
/* generated by jison-lex 0.3.4 */
var lexer = (function(){
var lexer = ({

EOF:1,

parseError:function parseError(str, hash) {
        if (this.yy.parser) {
            this.yy.parser.parseError(str, hash);
        } else {
            throw new Error(str);
        }
    },

// resets the lexer, sets new input
setInput:function (input, yy) {
        this.yy = yy || this.yy || {};
        this._input = input;
        this._more = this._backtrack = this.done = false;
        this.yylineno = this.yyleng = 0;
        this.yytext = this.matched = this.match = '';
        this.conditionStack = ['INITIAL'];
        this.yylloc = {
            first_line: 1,
            first_column: 0,
            last_line: 1,
            last_column: 0
        };
        if (this.options.ranges) {
            this.yylloc.range = [0,0];
        }
        this.offset = 0;
        return this;
    },

// consumes and returns one char from the input
input:function () {
        var ch = this._input[0];
        this.yytext += ch;
        this.yyleng++;
        this.offset++;
        this.match += ch;
        this.matched += ch;
        var lines = ch.match(/(?:\r\n?|\n).*/g);
        if (lines) {
            this.yylineno++;
            this.yylloc.last_line++;
        } else {
            this.yylloc.last_column++;
        }
        if (this.options.ranges) {
            this.yylloc.range[1]++;
        }

        this._input = this._input.slice(1);
        return ch;
    },

// unshifts one char (or a string) into the input
unput:function (ch) {
        var len = ch.length;
        var lines = ch.split(/(?:\r\n?|\n)/g);

        this._input = ch + this._input;
        this.yytext = this.yytext.substr(0, this.yytext.length - len);
        //this.yyleng -= len;
        this.offset -= len;
        var oldLines = this.match.split(/(?:\r\n?|\n)/g);
        this.match = this.match.substr(0, this.match.length - 1);
        this.matched = this.matched.substr(0, this.matched.length - 1);

        if (lines.length - 1) {
            this.yylineno -= lines.length - 1;
        }
        var r = this.yylloc.range;

        this.yylloc = {
            first_line: this.yylloc.first_line,
            last_line: this.yylineno + 1,
            first_column: this.yylloc.first_column,
            last_column: lines ?
                (lines.length === oldLines.length ? this.yylloc.first_column : 0)
                 + oldLines[oldLines.length - lines.length].length - lines[0].length :
              this.yylloc.first_column - len
        };

        if (this.options.ranges) {
            this.yylloc.range = [r[0], r[0] + this.yyleng - len];
        }
        this.yyleng = this.yytext.length;
        return this;
    },

// When called from action, caches matched text and appends it on next action
more:function () {
        this._more = true;
        return this;
    },

// When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
reject:function () {
        if (this.options.backtrack_lexer) {
            this._backtrack = true;
        } else {
            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n' + this.showPosition(), {
                text: "",
                token: null,
                line: this.yylineno
            });

        }
        return this;
    },

// retain first n characters of the match
less:function (n) {
        this.unput(this.match.slice(n));
    },

// displays already matched input, i.e. for error messages
pastInput:function () {
        var past = this.matched.substr(0, this.matched.length - this.match.length);
        return (past.length > 20 ? '...':'') + past.substr(-20).replace(/\n/g, "");
    },

// displays upcoming input, i.e. for error messages
upcomingInput:function () {
        var next = this.match;
        if (next.length < 20) {
            next += this._input.substr(0, 20-next.length);
        }
        return (next.substr(0,20) + (next.length > 20 ? '...' : '')).replace(/\n/g, "");
    },

// displays the character position where the lexing error occurred, i.e. for error messages
showPosition:function () {
        var pre = this.pastInput();
        var c = new Array(pre.length + 1).join("-");
        return pre + this.upcomingInput() + "\n" + c + "^";
    },

// test the lexed token: return FALSE when not a match, otherwise return token
test_match:function(match, indexed_rule) {
        var token,
            lines,
            backup;

        if (this.options.backtrack_lexer) {
            // save context
            backup = {
                yylineno: this.yylineno,
                yylloc: {
                    first_line: this.yylloc.first_line,
                    last_line: this.last_line,
                    first_column: this.yylloc.first_column,
                    last_column: this.yylloc.last_column
                },
                yytext: this.yytext,
                match: this.match,
                matches: this.matches,
                matched: this.matched,
                yyleng: this.yyleng,
                offset: this.offset,
                _more: this._more,
                _input: this._input,
                yy: this.yy,
                conditionStack: this.conditionStack.slice(0),
                done: this.done
            };
            if (this.options.ranges) {
                backup.yylloc.range = this.yylloc.range.slice(0);
            }
        }

        lines = match[0].match(/(?:\r\n?|\n).*/g);
        if (lines) {
            this.yylineno += lines.length;
        }
        this.yylloc = {
            first_line: this.yylloc.last_line,
            last_line: this.yylineno + 1,
            first_column: this.yylloc.last_column,
            last_column: lines ?
                         lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length :
                         this.yylloc.last_column + match[0].length
        };
        this.yytext += match[0];
        this.match += match[0];
        this.matches = match;
        this.yyleng = this.yytext.length;
        if (this.options.ranges) {
            this.yylloc.range = [this.offset, this.offset += this.yyleng];
        }
        this._more = false;
        this._backtrack = false;
        this._input = this._input.slice(match[0].length);
        this.matched += match[0];
        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
        if (this.done && this._input) {
            this.done = false;
        }
        if (token) {
            return token;
        } else if (this._backtrack) {
            // recover context
            for (var k in backup) {
                this[k] = backup[k];
            }
            return false; // rule action called reject() implying the next rule should be tested instead.
        }
        return false;
    },

// return next match in input
next:function () {
        if (this.done) {
            return this.EOF;
        }
        if (!this._input) {
            this.done = true;
        }

        var token,
            match,
            tempMatch,
            index;
        if (!this._more) {
            this.yytext = '';
            this.match = '';
        }
        var rules = this._currentRules();
        for (var i = 0; i < rules.length; i++) {
            tempMatch = this._input.match(this.rules[rules[i]]);
            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                match = tempMatch;
                index = i;
                if (this.options.backtrack_lexer) {
                    token = this.test_match(tempMatch, rules[i]);
                    if (token !== false) {
                        return token;
                    } else if (this._backtrack) {
                        match = false;
                        continue; // rule action called reject() implying a rule MISmatch.
                    } else {
                        // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)
                        return false;
                    }
                } else if (!this.options.flex) {
                    break;
                }
            }
        }
        if (match) {
            token = this.test_match(match, rules[index]);
            if (token !== false) {
                return token;
            }
            // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)
            return false;
        }
        if (this._input === "") {
            return this.EOF;
        } else {
            return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\n' + this.showPosition(), {
                text: "",
                token: null,
                line: this.yylineno
            });
        }
    },

// return next match that has a token
lex:function lex () {
        var r = this.next();
        if (r) {
            return r;
        } else {
            return this.lex();
        }
    },

// activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
begin:function begin (condition) {
        this.conditionStack.push(condition);
    },

// pop the previously active lexer condition state off the condition stack
popState:function popState () {
        var n = this.conditionStack.length - 1;
        if (n > 0) {
            return this.conditionStack.pop();
        } else {
            return this.conditionStack[0];
        }
    },

// produce the lexer rule set which is active for the currently active lexer condition state
_currentRules:function _currentRules () {
        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
            return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
        } else {
            return this.conditions["INITIAL"].rules;
        }
    },

// return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
topState:function topState (n) {
        n = this.conditionStack.length - 1 - Math.abs(n || 0);
        if (n >= 0) {
            return this.conditionStack[n];
        } else {
            return "INITIAL";
        }
    },

// alias for begin(condition)
pushState:function pushState (condition) {
        this.begin(condition);
    },

// return the number of states currently on the stack
stateStackSize:function stateStackSize() {
        return this.conditionStack.length;
    },
options: {"flex":true},
performAction: function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {
var YYSTATE=YY_START;
switch($avoiding_name_collisions) {
case 0:/* skip whitespace */
break;
case 1:return 15
break;
case 2:return 16
break;
case 3:return 14
break;
case 4:return 17
break;
case 5:return 17
break;
case 6:return "EXISTS"
break;
case 7:return 8
break;
case 8:return 7
break;
case 9:return 10
break;
case 10:return 9
break;
case 11:return 12
break;
case 12:return 11
break;
case 13:return 11
break;
case 14:return 19
break;
case 15:return 19
break;
case 16:return 18
break;
case 17:return 18
break;
case 18:return 22
break;
case 19:return 23
break;
case 20:return 20
break;
case 21:return 5
break;
case 22:return 'INVALID'
break;
case 23:console.log(yy_.yytext);
break;
}
},
rules: [/^(?:\s+)/,/^(?:"(?:\\?.)*?")/,/^(?:\/(?:\\?.)*?\/)/,/^(?:[-+a-zA-Z0-9_.@:*?/$]+)/,/^(?:\[[^\]\\]*(?:\\.[^\]\\]*)*\])/,/^(?:\][^\[\\]*(?:\\.[^\[\\]*)*\[)/,/^(?:EXISTS!)/,/^(?:<=)/,/^(?:<)/,/^(?:>=)/,/^(?:>)/,/^(?:!=)/,/^(?:==)/,/^(?:=)/,/^(?:\|\|)/,/^(?:\|)/,/^(?:&&)/,/^(?:&)/,/^(?:\()/,/^(?:\))/,/^(?:!)/,/^(?:$)/,/^(?:.)/,/^(?:.)/],
conditions: {"INITIAL":{"rules":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],"inclusive":true}}
});
return lexer;
})();
parser.lexer = lexer;
function Parser () {
  this.yy = {};
}
Parser.prototype = parser;parser.Parser = Parser;
return new Parser;
})();


if (typeof require !== 'undefined' && typeof exports !== 'undefined') {
exports.parser = arkimeparser;
exports.Parser = arkimeparser.Parser;
exports.parse = function () { return arkimeparser.parse.apply(arkimeparser, arguments); };
exports.main = function commonjsMain (args) {
    if (!args[1]) {
        console.log('Usage: '+args[0]+' FILE');
        process.exit(1);
    }
    var source = require('fs').readFileSync(require('path').normalize(args[1]), "utf8");
    return exports.parser.parse(source);
};
if (typeof module !== 'undefined' && require.main === module) {
  exports.main(process.argv.slice(1));
}
}