//- Display a clickable value
mixin clickableValue(expr, value, parse, session)
  if (Array.isArray(value) && value.length > 0)
    - value=value[0]

  if (value !== undefined)
    arkime-session-field.detail-field(expr=expr, value=value, parse=parse, :field=`getField('${expr}')`, session=session, pull-left=true)
  else
    | &nbsp;

//- Method to add a menu to field labels
mixin clickableLabel(field, title)
  b-dropdown.clickable-label(size="sm", text=title, variant="default")
    b-dropdown-item(@click="exportUnique('" + field  + "', 0)")
      | Export Unique #{title}
    b-dropdown-item(@click="exportUnique('" + field  + "', 1)")
      | Export Unique #{title} with counts
    b-dropdown-item(@click="openSpiGraph('" + field  + "')")
      | Open #{title} SPI Graph
    b-dropdown-item(@click="toggleColVis('" + field  + "')")
      | Toggle #{title} column
    b-dropdown-item(@click="toggleInfoVis('" + field  + "')")
      | Toggle #{title} in Info column
    b-dropdown-item(@click="fieldExists('" + field  + "', '==')")
      | Add #{title} EXISTS! to query
    +clickableFieldActions(field, true)

//- Display a clickable field action in the field menu
mixin clickableFieldActions(fieldExpr, sep)
  field-actions(:expr=`'${fieldExpr}'`, :separator=`${sep}`)

mixin arrayPrint(container, field, expr)
  if ((container && container[field]))
    each value,i in container[field]
      if (value.length == 0)
        span &lt;empty&gt;
      else
        +clickableValue(expr, value)

//- Standard method to print an array of values
mixin arrayList(container, field, title, expr, extra, max, session)
  if (!max)
    - max = 25
  if ((container && container[field]) || extra)
    dt
      +clickableLabel(expr, title)
    dd
      if (container && container[field])
        - if (!Array.isArray(container[field])) { container[field] = [container[field]]; }
        each value,i in container[field]
          if (i == max)
            |<span style="display:none">
          if (value.length == 0)
            span &lt;empty&gt;
          else
            +clickableValue(expr, value, null, session)
        if (container[field].length > max)
          |<a class="cursor-pointer show-more-items" @click="showFewerItems($event)">less...</a></span>
          a.cursor-pointer.show-more-items(@click="showMoreItems($event)") more...
      if (extra)
        |!{extra}
      div(style="display:inline;") &nbsp;

//- Standard method to print ip, port, geo, asn, and rir info
mixin ipPrint(session, ipstr, port, geo, as, rir, expr, full, v6)
  span
    if (full)
      +clickableValue(expr, ipstr)
    else
      +clickableValue('ip.' + expr, ipstr)
    - if (port !== undefined)
      strong #{sep}
      if (full)
        +clickableValue(`${expr}.port`, port)
      else
        +clickableValue(`port.${expr}`, port)
    - if (geo)
      | (
      if (full)
        +clickableValue(`${expr}.country`, geo)
      else
        +clickableValue(`country.${expr}`, geo)
      |) &nbsp;
    - if (as)
      span(style="display:inline-flex")
        |[
      if (full)
        +clickableValue(expr + '.asn', as)
      else
        +clickableValue('asn.' + expr, as)
      span(style="display:inline-flex")
        |] &nbsp;
    - if (rir)
      | {
      if (full)
        +clickableValue(expr + '.rir', rir)
      else
        +clickableValue('rir.' + expr, rir)
      |}

//- Standard method to print ip info with commas
mixin ipArrayList(container, field, title, expr, max)
  if (!max)
    - max = 25
  if (container[field])
    dt
      b-dropdown.clickable-label(text=title, size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('" + expr  + "', 0)")
          | Export Unique #{title}
        b-dropdown-item(@click="exportUnique('" + expr  + "', 1)")
          | Export Unique #{title} with counts
        b-dropdown-item(@click="openSpiGraph('" + expr  + "')")
          | Open #{title} SPI Graph
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('" + expr  + ".country', 0)")
          | Export Unique #{title} Country
        b-dropdown-item(@click="exportUnique('" + expr  + ".country', 1)")
          | Export Unique #{title} Country with counts
        b-dropdown-item(@click="openSpiGraph('" + expr  + ".country')")
          | Open #{title} Country SPI Graph
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('" + expr  + ".asn', 0)")
          | Export Unique #{title} ASN
        b-dropdown-item(@click="exportUnique('" + expr  + ".asn', 1)")
          | Export Unique #{title} ASN with counts
        b-dropdown-item(@click="openSpiGraph('" + expr  + ".asn')")
          | Open #{title} ASN SPI Graph
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('" + expr  + ".rir', 0)")
          | Export Unique #{title} RIR
        b-dropdown-item(@click="exportUnique('" + expr  + ".rir', 1)")
          | Export Unique #{title} RIR with counts
        b-dropdown-item(@click="openSpiGraph('" + expr  + ".rir')")
          | Open #{title} RIR SPI Graph
        b-dropdown-divider
        b-dropdown-item(@click="fieldExists('" + field  + "', '==')")
          | Add #{title} EXISTS! to query

    dd
      each ip,i in container[field]
        if (i == max)
          |<span style="display:none">
        if (field == "ip")
          +ipPrint(container, container.ip[i], undefined, (container.GEO?container.GEO[i]:null), (container.ASN?container.ASN[i]:null), (container.RIR?container.RIR[i]:null), expr, expr.endsWith(".ip"))
        else
          +ipPrint(container, container[field][i], undefined, (container[field + "GEO"]?container[field + "GEO"][i]:null), (container[field + "ASN"]?container[field + "ASN"][i]:null), (container[field + "RIR"]?container[field + "RIR"][i]:null), expr, true)
      if (container[field].length > max)
        |</span>
        a.cursor-pointer.show-more-items &hellip;

//- Method to print HTTP and SMTP headers
mixin printHeader(inHeaderNames, inHeaderValues, expr)
  - var headers = {};
  -
    function headerObject(names, values, headers) {
      for (i = 0; i < names.length; i++) {
        if(headers[names[i]])
          headers[names[i]].add(values[i]);
        else
          headers[names[i]] = new Set().add(values[i]);
      }
    }
  - headerObject(inHeaderNames, inHeaderValues, headers)
  - headerNames = Object.keys(headers)
  for headerName in headerNames
    dt
      b-dropdown.clickable-label(size="sm", text=headerName, variant="default")
        b-dropdown-item(@click="exportUnique('" + expr  + "', 1)")
          | Export Unique Header Value with counts
    - var headerValues = Array.from (headers[headerName]);
    dd
      each headerValue,i in headerValues
        +clickableValue(expr, headerValue)

mixin ipArrayListDNS(container, field, title, expr, max)
  if (!max)
    - max = 25
  if (container[field+ "Ip"])
    dt
      b-dropdown.clickable-label(text=title, size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('" + "ip." + expr  + "', 0)")
          | Export Unique #{title}
        b-dropdown-item(@click="exportUnique('" + "ip." + expr  + "', 1)")
          | Export Unique #{title} with counts
        b-dropdown-item(@click="openSpiGraph('" + "ip." + expr  + "')")
          | Open #{title} SPI Graph
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('" +   "country." + expr + "', 0)")
          | Export Unique #{title} Country
        b-dropdown-item(@click="exportUnique('" +  "country." + expr + "', 1)")
          | Export Unique #{title} Country with counts
        b-dropdown-item(@click="openSpiGraph('" + "country." + expr +"')")
          | Open #{title} Country SPI Graph
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('" + "asn." + expr + "', 0)")
          | Export Unique #{title} ASN
        b-dropdown-item(@click="exportUnique('" + "asn."+ expr + "', 1)")
          | Export Unique #{title} ASN with counts
        b-dropdown-item(@click="openSpiGraph('" + "asn." + expr + "')")
          | Open #{title} ASN SPI Graph
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('" + "rir." + expr + "', 0)")
          | Export Unique #{title} RIR
        b-dropdown-item(@click="exportUnique('" + "rir." + expr + "', 1)")
          | Export Unique #{title} RIR with counts
        b-dropdown-item(@click="openSpiGraph('" + "rir." + expr + "')")
          | Open #{title} RIR SPI Graph
    dd
      each ip,i in container[field + "Ip"]
        if (i == max)
          |<span style="display:none">
        +ipPrint(container, container[field + "Ip"][i], undefined, (container[field + "GEO"]?container[field + "GEO"][i]:null), (container[field+"ASN"]?container[field + "ASN"][i]:null), (container[field + "RIR"]?container[field + "RIR"][i]:null), expr)
      if (container[field + "Ip"].length > max)
        |</span>
        a.cursor-pointer.show-more-items &hellip
