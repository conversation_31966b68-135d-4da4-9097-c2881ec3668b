h4.card-title General
dl
  dt Time
  dd
    +clickableValue('starttime', session.firstPacket)
    strong -&nbsp;
    +clickableValue('stoptime', session.lastPacket)

  dt Id
  dd
    +clickableValue('id', Db.sid2Id(session.id))

  +arrayList(session, "rootId", "Root Id", "rootId")

  if (session.network && session.network.community_id)
    +arrayList(session.network, "community_id", "Community Id", "communityId")

  dt
    +clickableLabel('node', 'Node')
  dd
    +clickableValue('node', session.node)
    if (session.srcNode)
      +clickableValue('srcNode', session.srcNode)

  +arrayList(session, "protocol", "Protocols", "protocols")

  dt
    +clickableLabel('ethertype', 'Ethertype')
  dd
    +clickableValue('ethertype', session.ethertype)

  dt
    +clickableLabel('ip.protocol', 'IP Protocol')
  dd
    +clickableValue('ip.protocol', session.ipProtocol)

  +arrayList(session, "user", "Users", "user")

  +arrayList(session, "asset", "Assets", "asset")

  if (session.source.bytes || session.destination.bytes)
    dt
      b-dropdown.clickable-label(text="Src", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('packets.src', 0)")
          | Export Unique Src Packets
        b-dropdown-item(@click="exportUnique('packets.src', 1)")
          | Export Unique Src Packets with counts
        b-dropdown-item(@click="openSpiGraph('packets.src')")
          | Open Src Packets SPI Graph
        b-dropdown-item(@click="fieldExists('packets.src', '==')")
          | Add Src Packets EXISTS! to query
        +clickableFieldActions('packets.src', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('bytes.src', 0)")
          | Export Unique Src Bytes
        b-dropdown-item(@click="exportUnique('bytes.src', 1)")
          | Export Unique Src Bytes with counts
        b-dropdown-item(@click="openSpiGraph('bytes.src')")
          | Open Src Bytes SPI Graph
        b-dropdown-item(@click="fieldExists('bytes.src', '==')")
          | Add Src Bytes EXISTS! to query
        +clickableFieldActions('bytes.src', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('databytes.src', 0)")
          | Export Unique Src Databytes
        b-dropdown-item(@click="exportUnique('databytes.src', 1)")
          | Export Unique Src Databytes with counts
        b-dropdown-item(@click="openSpiGraph('databytes.src')")
          | Open Src Databytes SPI Graph
        b-dropdown-item(@click="fieldExists('databytes.src', '==')")
          | Add Src Databytes EXISTS! to query
        +clickableFieldActions('databytes.src', false)
    dd
      span.no-wrap
        strong.ml-1 Packets
        +clickableValue('packets.src', session.source.packets)
      span.no-wrap
        strong.ml-1 Bytes
        +clickableValue('bytes.src', session.source.bytes)
      span.no-wrap
        strong.ml-1 Databytes
        +clickableValue('databytes.src', session.client.bytes)

    dt
      b-dropdown.clickable-label(text="Dst", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('packets.dst', 0)")
          | Export Unique Dst Packets
        b-dropdown-item(@click="exportUnique('packets.dst', 1)")
          | Export Unique Dst Packets with counts
        b-dropdown-item(@click="openSpiGraph('packets.dst')")
          | Open Dst Packets SPI Graph
        b-dropdown-item(@click="fieldExists('packets.dst', '==')")
          | Add Dst Packets EXISTS! to query
        +clickableFieldActions('packets.dst', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('bytes.dst', 0)")
          | Export Unique Dst Bytes
        b-dropdown-item(@click="exportUnique('bytes.dst', 1)")
          | Export Unique Dst Bytes with counts
        b-dropdown-item(@click="openSpiGraph('bytes.dst')")
          | Open Dst Bytes SPI Graph
        b-dropdown-item(@click="fieldExists('bytes.dst', '==')")
          | Add Dst Bytes EXISTS! to query
        +clickableFieldActions('bytes.dst', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('databytes.dst', 0)")
          | Export Unique Dst Databytes
        b-dropdown-item(@click="exportUnique('databytes.dst', 1)")
          | Export Unique Dst Databytes with counts
        b-dropdown-item(@click="openSpiGraph('databytes.dst')")
          | Open Dst Databytes SPI Graph
        b-dropdown-item(@click="fieldExists('databytes.dst', '==')")
          | Add Dst Databytes EXISTS! to query
        +clickableFieldActions('databytes.dst', false)
    dd
      span.no-wrap
        strong.ml-1 Packets
        +clickableValue('packets.dst', session.destination.packets)
      span.no-wrap
        strong.ml-1 Bytes
        +clickableValue('bytes.dst', session.destination.bytes)
      span.no-wrap
        strong.ml-1 Databytes
        +clickableValue('databytes.dst', session.server.bytes)

  if (session.source.mac)
    dt
      b-dropdown.clickable-label(text="Src Ethernet", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('mac.src', 0)")
          | Export Unique Src Mac
        b-dropdown-item(@click="exportUnique('mac.src', 1)")
          | Export Unique Src Mac with counts
        b-dropdown-item(@click="openSpiGraph('mac.src')")
          | Open Src Mac SPI Graph
        b-dropdown-item(@click="fieldExists('mac.src', '==')")
          | Add Src Mac EXISTS! to query
        +clickableFieldActions('mac.src', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('oui.src', 0)")
          | Export Unique Src OUI
        b-dropdown-item(@click="exportUnique('oui.src', 1)")
          | Export Unique Src OUI with counts
        b-dropdown-item(@click="openSpiGraph('oui.src')")
          | Open Src OUI SPI Graph
        b-dropdown-item(@click="fieldExists('oui.src', '==')")
          | Add Src OUI EXISTS! to query
        +clickableFieldActions('oui.src', false)
    dd
      strong.ml-1 Mac
      +arrayPrint(session.source, "mac", "mac.src")

      if (session.srcOui)
        strong.ml-1 OUI
        +arrayPrint(session, "srcOui", "oui.src")

  if (session.destination.mac)
    dt
      b-dropdown.clickable-label(text="Dst Ethernet", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('mac.dst', 0)")
          | Export Unique Dst Mac
        b-dropdown-item(@click="exportUnique('mac.dst', 1)")
          | Export Unique Dst Mac with counts
        b-dropdown-item(@click="openSpiGraph('mac.dst')")
          | Open Dst Mac SPI Graph
        b-dropdown-item(@click="fieldExists('mac.dst', '==')")
          | Add Dst Mac EXISTS! to query
        +clickableFieldActions('mac.dst', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('oui.dst', 0)")
          | Export Unique Dst OUI
        b-dropdown-item(@click="exportUnique('oui.dst', 1)")
          | Export Unique Dst OUI with counts
        b-dropdown-item(@click="openSpiGraph('oui.dst')")
          | Open Dst OUI SPI Graph
        b-dropdown-item(@click="fieldExists('oui.dst', '==')")
          | Add Dst OUI EXISTS! to query
        +clickableFieldActions('oui.dst', false)
    dd
      strong.ml-1 Mac
      +arrayPrint(session.destination, "mac", "mac.dst")

      if (session.dstOui)
        strong.ml-1 OUI
        +arrayPrint(session, "dstOui", "oui.dst")

  if (session.network && session.network.vlan !== undefined)
    +arrayList(session.network.vlan, "id", "VLan", "vlan")

  +arrayList(session, "vni", "VNI", "vni")

  if (session.srcOuterMac || session.srcOuterIp )
    dt
      b-dropdown.clickable-label(text="Src Outer Layer", size="sm", variant="default")
        if (session.srcOuterMac)
          b-dropdown-item(@click="exportUnique('outermac.src', 0)")
            | Export outer Unique Src Mac
          b-dropdown-item(@click="exportUnique('outermac.src', 1)")
            | Export outer Unique Src Mac with counts
          b-dropdown-item(@click="openSpiGraph('outermac.src')")
            | Open outer Src Mac SPI Graph
          b-dropdown-item(@click="fieldExists('outermac.src', '==')")
            | Add outer Src Mac EXISTS! to query
          b-dropdown-divider
        if (session.srcOuterIp)
          b-dropdown-item(@click="exportUnique('outerip.src', 0)")
            | Export outer Unique Src IP
          b-dropdown-item(@click="exportUnique('outerip.src', 1)")
            | Export outer Unique Src IP with counts
          b-dropdown-item(@click="openSpiGraph('outerip.src')")
            | Open outer Src IP SPI Graph
          b-dropdown-item(@click="fieldExists('outerip.src', '==')")
            | Add outer Src IP EXISTS! to query
          b-dropdown-divider
        if (session.srcOuterOui)
          b-dropdown-item(@click="exportUnique('outeroui.src', 0)")
            | Export outer Unique Src Oui
          b-dropdown-item(@click="exportUnique('outeroui.src', 1)")
            | Export outer Unique Src Oui with counts
          b-dropdown-item(@click="openSpiGraph('outeroui.src')")
            | Open outer Src Oui SPI Graph
          b-dropdown-item(@click="fieldExists('outeroui.src', '==')")
            | Add outer Src Oui EXISTS! to query
          b-dropdown-divider
    dd
      if (session.srcOuterMac)
        strong.ml-1 Mac
        +arrayPrint(session, "srcOuterMac", "outermac.src")
      if (session.srcOuterOui)
        strong.ml-1 Oui
        +arrayPrint(session, "srcOuterOui", "outeroui.src")
      if (session.srcOuterIp)
        strong.ml-1 IP
        +arrayPrint(session, "srcOuterIp", "outerip.src")

  if (session.dstOuterMac || session.dstOuterIp )
    dt
      b-dropdown.clickable-label(text="Dst Outer Layer", size="sm", variant="default")
        if (session.dstOuterMac)
          b-dropdown-item(@click="exportUnique('outermac.dst', 0)")
            | Export outer Unique Dst Mac
          b-dropdown-item(@click="exportUnique('outermac.dst', 1)")
            | Export outer Unique Dst Mac with counts
          b-dropdown-item(@click="openSpiGraph('outermac.dst')")
            | Open outer Dst Mac SPI Graph
          b-dropdown-item(@click="fieldExists('outermac.dst', '==')")
            | Add outer Dst Mac EXISTS! to query
          b-dropdown-divider
        if (session.dstOuterIp)
          b-dropdown-item(@click="exportUnique('outerip.dst', 0)")
            | Export outer Unique Dst IP
          b-dropdown-item(@click="exportUnique('outerip.dst', 1)")
            | Export outer Unique Dst IP with counts
          b-dropdown-item(@click="openSpiGraph('outerip.dst')")
            | Open outer Dst IP SPI Graph
          b-dropdown-item(@click="fieldExists('outerip.dst', '==')")
            | Add outer Dst IP EXISTS! to query
          b-dropdown-divider
        if (session.dstOuterOui)
          b-dropdown-item(@click="exportUnique('outeroui.dst', 0)")
            | Export outer Unique Dst Oui
          b-dropdown-item(@click="exportUnique('outeroui.dst', 1)")
            | Export outer Unique Dst Oui with counts
          b-dropdown-item(@click="openSpiGraph('outeroui.dst')")
            | Open outer Dst Oui SPI Graph
          b-dropdown-item(@click="fieldExists('outeroui.dst', '==')")
            | Add outer Dst Oui EXISTS! to query
    dd
      if (session.dstOuterMac)
        strong.ml-1 Mac
        +arrayPrint(session, "dstOuterMac", "outermac.dst")
      if (session.dstOuterOui)
        strong.ml-1 Oui
        +arrayPrint(session, "dstOuterOui", "outeroui.dst")
      if (session.dstOuterIp)
        strong.ml-1 IP
        +arrayPrint(session, "dstOuterIp", "outerip.dst")

  dt
    b-dropdown.clickable-label(text="Src IP/Port", size="sm", variant="default")
      b-dropdown-item(@click="exportUnique('ip.src', 0)")
        | Export Unique Src IP
      b-dropdown-item(@click="exportUnique('ip.src', 1)")
        | Export Unique Src IP with counts
      b-dropdown-item(@click="exportUnique('ip.src:port.src', 0)")
        | Export Unique Src IP#{sep}Port
      b-dropdown-item(@click="exportUnique('ip.src:port.src', 1)")
        | Export Unique Src IP#{sep}Port with counts
      b-dropdown-item(@click="openSpiGraph('ip.src')")
        | Open Src IPv6 SPI Graph
      +clickableFieldActions('ip.src', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('port.src', 0)")
        | Export Unique Src Port
      b-dropdown-item(@click="exportUnique('port.src', 1)")
        | Export Unique Src Port with counts
      b-dropdown-item(@click="openSpiGraph('port.src')")
        | Open Src Port SPI Graph
      +clickableFieldActions('port.src', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('country.src', 0)")
        | Export Unique Src Country
      b-dropdown-item(@click="exportUnique('country.src', 1)")
        | Export Unique Src Country with counts
      b-dropdown-item(@click="openSpiGraph('country.src')")
        | Open Src Country SPI Graph
      b-dropdown-item(@click="fieldExists('country.src', '==')")
        | Add Src Country EXISTS! to query
      +clickableFieldActions('country.src', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('source.as.full', 0)")
        | Export Unique Src ASN
      b-dropdown-item(@click="exportUnique('source.as.full', 1)")
        | Export Unique Src ASN with counts
      b-dropdown-item(@click="openSpiGraph('source.as.full')")
        | Open Src ASN SPI Graph
      b-dropdown-item(@click="fieldExists('source.as.full', '==')")
        | Add Src ASN EXISTS! to query
      +clickableFieldActions('source.as.full', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('rir.src', 0)")
        | Export Unique Src RIR
      b-dropdown-item(@click="exportUnique('rir.src', 1)")
        | Export Unique Src RIR with counts
      b-dropdown-item(@click="openSpiGraph('rir.src')")
        | Open Src RIR SPI Graph
      b-dropdown-item(@click="fieldExists('rir.src', '==')")
        | Add Src RIR EXISTS! to query
      +clickableFieldActions('rir.src', false)

  dd
    +ipPrint(session, session.source.ip, session.source.port, session.source.geo.country_iso_code, session.source.as.full, session.srcRIR, "src")

  dt
    b-dropdown.clickable-label(text="Dst IP/Port", size="sm", variant="default")
      b-dropdown-item(@click="exportUnique('ip.dst', 0)")
        | Export Unique Dst IP
      b-dropdown-item(@click="exportUnique('ip.dst', 1)")
        | Export Unique Dst IP with counts
      b-dropdown-item(@click="exportUnique('ip.dst:port.dst', 0)")
        | Export Unique Dst IP#{sep}Port
      b-dropdown-item(@click="exportUnique('ip.dst:port.dst', 1)")
        | Export Unique Dst IP#{sep}Port with counts
      b-dropdown-item(@click="openSpiGraph('ip.dst')")
        | Open Dst IPv6 SPI Graph
      +clickableFieldActions('ip.dst', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('port.dst', 0)")
        | Export Unique Dst Port
      b-dropdown-item(@click="exportUnique('port.dst', 1)")
        | Export Unique Dst Port with counts
      b-dropdown-item(@click="openSpiGraph('port.dst')")
        | Open Dst Port SPI Graph
      +clickableFieldActions('port.dst', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('country.dst', 0)")
        | Export Unique Dst Country
      b-dropdown-item(@click="exportUnique('country.dst', 1)")
        | Export Unique Dst Country with counts
      b-dropdown-item(@click="openSpiGraph('country.dst')")
        | Open Dst Country SPI Graph
      b-dropdown-item(@click="fieldExists('country.dst', '==')")
        | Add Dst Country EXISTS! to query
      +clickableFieldActions('country.dst', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('destination.as.full', 0)")
        | Export Unique Dst ASN
      b-dropdown-item(@click="exportUnique('destination.as.full', 1)")
        | Export Unique Dst ASN with counts
      b-dropdown-item(@click="openSpiGraph('destination.as.full')")
        | Open Dst ASN SPI Graph
      b-dropdown-item(@click="fieldExists('destination.as.full', '==')")
        | Add Dst ASN EXISTS! to query
      +clickableFieldActions('destination.as.full', false)
      b-dropdown-divider
      b-dropdown-item(@click="exportUnique('rir.dst', 0)")
        | Export Unique Dst RIR
      b-dropdown-item(@click="exportUnique('rir.dst', 1)")
        | Export Unique Dst RIR with counts
      b-dropdown-item(@click="openSpiGraph('rir.dst')")
        | Open Dst RIR SPI Graph
      b-dropdown-item(@click="fieldExists('rir.dst', '==')")
        | Add Dst RIR EXISTS! to query
      +clickableFieldActions('rir.dst', false)

  dd
    +ipPrint(session, session.destination.ip, session.destination.port, session.destination.geo.country_iso_code, session.destination.as.full, session.dstRIR, "dst")

  if (session.srcPayload8 || session.dstPayload8)
    dt
      b-dropdown.clickable-label(text="Payload8", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('payload8.src.hex', 0)")
          | Export Unique Src Payload8
        b-dropdown-item(@click="exportUnique('payload8.src.hex', 1)")
          | Export Unique Src Payload8 with counts
        b-dropdown-item(@click="openSpiGraph('payload8.src.hex')")
          | Open Src Payload8 SPI Graph
        b-dropdown-item(@click="fieldExists('payload8.src.hex', '==')")
          | Add Src Payload8 EXISTS! to query
        +clickableFieldActions('payload8.src.hex', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('payload8.dst.hex', 0)")
          | Export Unique Dst Payload8
        b-dropdown-item(@click="exportUnique('payload8.dst.hex', 1)")
          | Export Unique Dst Payload8 with counts
        b-dropdown-item(@click="openSpiGraph('payload8.dst.hex')")
          | Open Dst Payload8 SPI Graph
        b-dropdown-item(@click="fieldExists('payload8.dst.hex', '==')")
          | Add Dst Payload8 EXISTS! to query
        +clickableFieldActions('payload8.dst.hex', false)

    dd
      - function isprint(char) { return !( /[\x00-\x08\x0E-\x1F\x80-\xFF]/.test(char)); }
      - function printstr(str) { for (let char of str) { if (!isprint(char)) { char = '�'; } } return str; }
      if (session.srcPayload8)
        span.no-wrap
          strong.ml-1 Src
          - var srcPayload8a = Buffer.from(session.srcPayload8, "hex").toString();
          +clickableValue('payload8.src.hex', session.srcPayload8)
        | ( #{printstr(srcPayload8a)} )
      if (session.dstPayload8)
        span.no-wrap
          if (session.srcPayload8)
            strong.ml-1 Dst
          else
            strong Dst
          - var dstPayload8a = Buffer.from(session.dstPayload8, "hex").toString();
          +clickableValue('payload8.dst.hex', session.dstPayload8)
        | ( #{printstr(dstPayload8a)} )

  div.tag-list
    +arrayList(session, "tags", "Tags", "tags", '<div @click="addTags()" v-b-tooltip title="Add a new tag to this session" class="btn btn-xs btn-theme-secondary ml-1"><span class="fa fa-plus-circle"></span></div>')

  if (session.fileId && session.fileId.length > 0)
    +arrayList(session, "fileId", "Files", "file")

  if (session.huntId && session.huntId.length > 0)
    +arrayList(session, "huntId", "Hunt Ids", "huntId")
  if (session.huntName && session.huntName.length > 0)
    +arrayList(session, "huntName", "Hunt Names", "huntName")

  if (session.scrubby)
    dt
      +clickableLabel('scrubbed.by', 'Scrubbed')
    dd
      div.mt-1.mb-1
        | By Moloch user #{session.scrubby} at #{new Date(session.scrubat)}

  if (session.socks)
    dt
      b-dropdown.clickable-label(text="Socks Dst", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('ip.socks', 0)")
          | Export Unique Socks Dst
        b-dropdown-item(@click="exportUnique('ip.socks', 1)")
          | Export Unique Socks Dst with counts
        b-dropdown-item(@click="openSpiGraph('ip.socks')")
          | Open Socks Dst SPI Graph
        b-dropdown-item(@click="fieldExists('ip.socks', '==')")
          | Add Socks Dst EXISTS! to query
        +clickableFieldActions('ip.socks', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('port.socks', 0)")
          | Export Unique Socks Port
        b-dropdown-item(@click="exportUnique('port.socks', 1)")
          | Export Unique Socks Port with counts
        b-dropdown-item(@click="openSpiGraph('port.socks')")
          | Open Socks Port SPI Graph
        b-dropdown-item(@click="fieldExists('port.socks', '==')")
          | Add Socks Port EXISTS! to query
        +clickableFieldActions('port.socks', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('country.socks', 0)")
          | Export Unique Socks Country
        b-dropdown-item(@click="exportUnique('country.socks', 1)")
          | Export Unique Socks Country with counts
        b-dropdown-item(@click="openSpiGraph('country.socks')")
          | Open Socks Country SPI Graph
        b-dropdown-item(@click="fieldExists('country.socks', '==')")
          | Add Socks Country EXISTS! to query
        +clickableFieldActions('country.socks', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('socks.ip', 0)")
          | Export Unique Socks ASN
        b-dropdown-item(@click="exportUnique('socks.ip', 1)")
          | Export Unique Socks ASN with counts
        b-dropdown-item(@click="openSpiGraph('socks.ip')")
          | Open Socks ASN SPI Graph
        b-dropdown-item(@click="fieldExists('socks.ip', '==')")
          | Add Socks ASN EXISTS! to query
        +clickableFieldActions('socks.ip', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('rir.socks', 0)")
          | Export Unique Socks RIR
        b-dropdown-item(@click="exportUnique('rir.socks', 1)")
          | Export Unique Socks RIR with counts
        b-dropdown-item(@click="openSpiGraph('rir.socks')")
          | Open Socks RIR SPI Graph
        b-dropdown-item(@click="fieldExists('rir.socks', '==')")
          | Add Socks RIR EXISTS! to query
        +clickableFieldActions('rir.socks', false)

    dd
      +ipPrint(session, session.socks.ip, session.socks.port, session.socks.GEO, session.socks.ASN, session.socks.RIR, "socks")

    dt
      b-dropdown.clickable-label(text="Socks Host", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('host.socks', 0)")
          | Export Unique Socks Host
        b-dropdown-item(@click="exportUnique('host.socks', 1)")
          | Export Unique Socks Host with counts
        b-dropdown-item(@click="openSpiGraph('host.socks')")
          | Open Socks Host SPI Graph
        b-dropdown-item(@click="fieldExists('host.socks', '==')")
          | Add Socks Host EXISTS! to query
        +clickableFieldActions('host.socks', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('port.socks', 0)")
          | Export Unique Socks Port
        b-dropdown-item(@click="exportUnique('port.socks', 1)")
          | Export Unique Socks Port with counts
        b-dropdown-item(@click="openSpiGraph('port.socks')")
          | Open Socks Port SPI Graph
        b-dropdown-item(@click="fieldExists('port.socks', '==')")
          | Add Socks Port EXISTS! to query
        +clickableFieldActions('port.socks', false)

    dd
      +clickableValue('host.socks', session.socks.host)
      | :
      +clickableValue('port.socks', session.socks.port)

    if (session.socks.user)
      dt
        +clickableLabel('socks.user', 'Socks User')
      dd
        +clickableValue('socks.user', session.socks.user)

  if (session.greIp)
    +ipArrayList(session, "greIp", "GRE IPs", "gre.ip")

  if (session.tcp)
    +arrayList(session.tcp, "ja4l", "JA4l", "tcp.ja4l")
    +arrayList(session.tcp, "ja4ls", "JA4ls", "tcp.ja4ls")
    +arrayList(session.tcp, "ja4t", "JA4t", "tcp.ja4t")
    +arrayList(session.tcp, "ja4ts", "JA4ts", "tcp.ja4ts")

  if (session.tcpflags)
    dt
      b-dropdown.clickable-label(text="TCP Flags", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('tcpflags.syn', 0)")
          | Export Unique SYN
        b-dropdown-item(@click="exportUnique('tcpflags.syn', 1)")
          | Export Unique SYN with counts
        b-dropdown-item(@click="openSpiGraph('tcpflags.syn')")
          | Open SYN SPI Graph
        b-dropdown-item(@click="fieldExists('tcpflags.syn', '==')")
          | Add SYN EXISTS! to query
        +clickableFieldActions('tcpflags.syn', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('tcpflags.syn-ack', 0)")
          | Export Unique SYN-ACK
        b-dropdown-item(@click="exportUnique('tcpflags.syn-ack', 1)")
          | Export Unique SYN-ACK with counts
        b-dropdown-item(@click="openSpiGraph('tcpflags.syn-ack')")
          | Open SYN-ACK SPI Graph
        b-dropdown-item(@click="fieldExists('tcpflags.syn-ack', '==')")
          | Add SYN-ACK EXISTS! to query
        +clickableFieldActions('tcpflags.syn-ack', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('tcpflags.ack', 0)")
          | Export Unique ACK
        b-dropdown-item(@click="exportUnique('tcpflags.ack', 1)")
          | Export Unique ACK with counts
        b-dropdown-item(@click="openSpiGraph('tcpflags.ack')")
          | Open ACK SPI Graph
        b-dropdown-item(@click="fieldExists('tcpflags.ack', '==')")
          | Add ACK EXISTS! to query
        +clickableFieldActions('tcpflags.ack', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('tcpflags.psh', 0)")
          | Export Unique PSH
        b-dropdown-item(@click="exportUnique('tcpflags.psh', 1)")
          | Export Unique PSH with counts
        b-dropdown-item(@click="openSpiGraph('tcpflags.psh')")
          | Open PSH SPI Graph
        b-dropdown-item(@click="fieldExists('tcpflags.psh', '==')")
          | Add PSH EXISTS! to query
        +clickableFieldActions('tcpflags.psh', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('tcpflags.rst', 0)")
          | Export Unique RST
        b-dropdown-item(@click="exportUnique('tcpflags.rst', 1)")
          | Export Unique RST with counts
        b-dropdown-item(@click="openSpiGraph('tcpflags.rst')")
          | Open RST SPI Graph
        b-dropdown-item(@click="fieldExists('tcpflags.rst', '==')")
          | Add RST EXISTS! to query
        +clickableFieldActions('tcpflags.rst', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('tcpflags.fin', 0)")
          | Export Unique FIN
        b-dropdown-item(@click="exportUnique('tcpflags.fin', 1)")
          | Export Unique FIN with counts
        b-dropdown-item(@click="openSpiGraph('tcpflags.fin')")
          | Open FIN SPI Graph
        b-dropdown-item(@click="fieldExists('tcpflags.fin', '==')")
          | Add FIN EXISTS! to query
        +clickableFieldActions('tcpflags.fin', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('tcpflags.urg', 0)")
          | Export Unique URG
        b-dropdown-item(@click="exportUnique('tcpflags.urg', 1)")
          | Export Unique URG with counts
        b-dropdown-item(@click="openSpiGraph('tcpflags.urg')")
          | Open URG SPI Graph
        b-dropdown-item(@click="fieldExists('tcpflags.urg', '==')")
          | Add URG EXISTS! to query
        +clickableFieldActions('tcpflags.urg', false)
    dd
      span.no-wrap
        strong.ml-1 SYN
        +clickableValue('tcpflags.syn', session.tcpflags.syn)
      span.no-wrap
        strong.ml-1 SYN-ACK
        +clickableValue('tcpflags.syn-ack', session.tcpflags['syn-ack'])
      span.no-wrap
        strong.ml-1 ACK
        +clickableValue('tcpflags.ack', session.tcpflags.ack)
      span.no-wrap
        strong.ml-1 PSH
        +clickableValue('tcpflags.psh', session.tcpflags.psh)
      span.no-wrap
        strong.ml-1 RST
        +clickableValue('tcpflags.rst', session.tcpflags.rst)
      span.no-wrap
        strong.ml-1 FIN
        +clickableValue('tcpflags.fin', session.tcpflags.fin)
      span.no-wrap
        strong.ml-1 URG
        +clickableValue('tcpflags.urg', session.tcpflags.urg)

  if (session.tcpseq)
    dt
      b-dropdown.clickable-label(text="TCP Initial Seq", size="sm", variant="default")
        if (session.tcpseq.src !== undefined)
          b-dropdown-item(@click="exportUnique('tcpseq.src', 0)")
            | Export Unique TCP Seq Src
          +clickableFieldActions('tcpseq.src', false)
        if (session.tcpseq.dst !== undefined)
          b-dropdown-item(@click="exportUnique('tcpseq.dst', 0)")
            | Export Unique TCP Seq Dst
          +clickableFieldActions('tcpseq.dst', false)
    dd
      if (session.tcpseq.src !== undefined)
        strong.ml-1 Src
        +arrayPrint(session.tcpseq, "src", "tcpseq.src")
      if (session.tcpseq.dst !== undefined)
        strong.ml-1 Dst
        +arrayPrint(session.tcpseq, "dst", "tcpseq.dst")

  if (session.icmp)
    dt
      b-dropdown.clickable-label(text="ICMP", size="sm", variant="default")
        b-dropdown-item(@click="exportUnique('icmp.type', 0)")
          | Export Unique ICMP Type
        b-dropdown-item(@click="exportUnique('icmp.type', 1)")
          | Export Unique ICMP Type with counts
        b-dropdown-item(@click="fieldExists('icmp.type', '==')")
          | Add ICMP Type EXISTS! to query
        +clickableFieldActions('icmp.type', false)
        b-dropdown-divider
        b-dropdown-item(@click="exportUnique('icmp.code', 0)")
          | Export Unique ICMP Code
        b-dropdown-item(@click="exportUnique('icmp.code', 1)")
          | Export Unique ICMP Code with counts
        b-dropdown-item(@click="fieldExists('icmp.code', '==')")
          | Add ICMP Code EXISTS! to query
        +clickableFieldActions('icmp.code', false)
    dd
      strong.ml-1 Type
      +arrayPrint(session.icmp, "type", "icmp.type")
      strong.ml-1 Code
      +arrayPrint(session.icmp, "code", "icmp.code")

  if (session.srcDscp || session.dstDscp)
    dt
      b-dropdown.clickable-label(text="DSCP", size="sm", variant="default")
        if (session.srcDscp)
          b-dropdown-item(@click="exportUnique('dscp.src', 0)")
            | Export Unique DSCP Src
          b-dropdown-item(@click="exportUnique('dscp.src', 1)")
            | Export Unique DSCP Src with counts
          +clickableFieldActions('dscp.src', false)
        if (session.dstDscp)
          b-dropdown-item(@click="exportUnique('dscp.dst', 0)")
            | Export Unique DSCP Dst
          b-dropdown-item(@click="exportUnique('dscp.dst', 1)")
            | Export Unique DSCP Dst with counts
          +clickableFieldActions('dscp.dst', false)
    dd
      if (session.srcDscp)
        strong.ml-1 Src
        +arrayPrint(session, "srcDscp", "dscp.src")
      if (session.dstDscp)
        strong.ml-1 Dst
        +arrayPrint(session, "dstDscp", "dscp.dst")

  if (session.srcTTL || session.dstTTL)
    dt
      b-dropdown.clickable-label(text="IP TTL", size="sm", variant="default")
        if (session.srcTTL)
          b-dropdown-item(@click="exportUnique('ttl.src', 0)")
            | Export Unique TTL Src
          b-dropdown-item(@click="exportUnique('ttl.src', 1)")
            | Export Unique TTL Src with counts
          +clickableFieldActions('ttl.src', false)
        if (session.dstTTL)
          b-dropdown-item(@click="exportUnique('ttl.dst', 0)")
            | Export Unique TTL Dst
          b-dropdown-item(@click="exportUnique('ttl.dst', 1)")
            | Export Unique TTL Dst with counts
          +clickableFieldActions('ttl.dst', false)
    dd
      if (session.srcTTL)
        strong.ml-1 Src
        +arrayPrint(session, "srcTTL", "ttl.src")
      if (session.dstTTL)
        strong.ml-1 Dst
        +arrayPrint(session, "dstTTL", "ttl.dst")
