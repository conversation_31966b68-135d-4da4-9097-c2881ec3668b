if (session._err)
  div.alert.alert-danger
    span.fa.fa-exclamation-triangle
    strong &nbsp; #{session._err}
else
  if (session.network.packets[0] > reqPackets)
    h5.text-theme-quaternary
      span.fa.fa-info-circle.fa-lg
      | &nbsp; Only showing first #{reqPackets} packets

  div.row#textpacket
    div.col-md-6
      h4
        span.srccol Source
          if (session.sourceKey)
            span.small
              | &nbsp;(#{session.sourceKey})
          span.src-col-tip
    div.col-md-6
      h4
        span.dstcol Destination
          if (session.destinationKey)
            span.small
              | &nbsp;(#{session.destinationKey})
          span.dst-col-tip

  if (showFrames)
    each item, i in data
      div.row
        if (item.src)
          div.col-md-6.sessionsrc
            if (item.ts)
              div.session-detail-ts(value=item.ts)
                em.ts-value #{item.ts}
                if (item.tcpflags)
                  | &nbsp;&nbsp;
                  each flag, key in item.tcpflags
                    if (flag > 0)
                     | &nbsp; #{key} &nbsp;
                span.pull-right #{item.bytes} bytes
            |!{item.html}
          div.col-md-6
        else
          div.col-md-6.offset-md-6.sessiondst
            if (item.ts)
              div.session-detail-ts(value=item.ts)
                em.ts-value #{item.ts}
                if (item.tcpflags)
                  | &nbsp;&nbsp;
                  each flag, key in item.tcpflags
                    if (flag > 0)
                     | &nbsp; #{key} &nbsp;
                span.pull-right #{item.bytes} bytes
            |!{item.html}

  else
    each item, i in data
      div.row
        if item.client == 0
          div.col-md-6.sessionsrc
            if (item.ts)
              div.session-detail-ts(value=item.ts)
                em.ts-value #{item.ts}
                span.pull-right #{item.bytes} bytes
            |!{item.html}
          div.col-md-6
        else
          div.col-md-6.offset-md-6.sessiondst
            if (item.ts)
              div.session-detail-ts(value=item.ts)
                em.ts-value #{item.ts}
                span.pull-right #{item.bytes} bytes
            |!{item.html}
