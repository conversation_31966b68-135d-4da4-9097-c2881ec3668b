if (session.http)
  div.sessionDetailMeta.bold HTTP
  dl.sessionDetailMeta(suffix="http")
    if (session.http.method)
      dt
        +clickableLabel("http.method", "Method")
      dd
        each method in session.http.method
          +clickableValue("http.method", method, null, session)
          if (session.http["method-" + method])
              | (
              +clickableValue("http.method." + method, session.http["method-" + method][0], null, session)
              |)
        div(style="display:inline-block;") &nbsp;
    +arrayList(session.http, "statuscode", "Status code", "http.statuscode")
    +arrayList(session.http, "host", "Hosts", "host.http")
    +arrayList(session.http, "useragent", "User Agents", "http.user-agent")
    +ipArrayList(session.http, "xffIp", "XFFs", "ip.xff")
    +arrayList(session.http, "requestHeader", "Request Headers", "http.hasheader.src")
    +arrayList(session.http, "clientVersion", "Client Versions", "http.version.src")
    if (session.http.requestHeader)
      each value,i in session.http.requestHeader
        if (session.http['request-' + value])
          +arrayList(session.http, "request-" + value, value + " Header", "http.request." + value)
    +arrayList(session.http, "responseHeader", "Response Headers", "http.hasheader.dst")
    +arrayList(session.http, "serverVersion", "Server Versions", "http.version.dst")
    if (session.http.responseHeader)
      each value,i in session.http.responseHeader
        if (session.http['response-' + value])
          +arrayList(session.http, "response-" + value, value + " Header", "http.response." + value)
    +arrayList(session.http, "md5", "Body MD5s", "http.md5", null, null, session)
    +arrayList(session.http, "sha256", "Body SHA256s", "http.sha256", null, null, session)
    +arrayList(session.http, "key", "QS Keys", "http.uri.key")
    +arrayList(session.http, "cookieKey", "Cookie Keys", "http.cookie.key")
    +arrayList(session.http, "user", "User", "http.user")
    +arrayList(session.http, "authType", "Auth Type", "http.authtype")
    +arrayList(session.http, "bodyMagic", "libfile content type", "http.bodymagic")
    +arrayList(session.http, "ja4h", "JA4h", "http.ja4h")
    +arrayList(session.http, "ja4h_r", "JA4h_r", "http.ja4h_r")
    if (session.http.requestHeaderField && session.http.requestHeaderValue)
      div.sessionDetailMeta.bold HTTP Request Header Detail
      dl.sessionDetailMeta(suffix="http-request-header-detail")
        +printHeader(session.http.requestHeaderField, session.http.requestHeaderValue, 'http.hasheader.src.value')
    if (session.http.responseHeaderField && session.http.responseHeaderValue)
      div.sessionDetailMeta.bold HTTP Response Header Detail
      dl.sessionDetailMeta(suffix="http-response-header-detail")
        +printHeader(session.http.responseHeaderField, session.http.responseHeaderValue, 'http.hasheader.dst.value')
