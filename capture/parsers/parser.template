#include <string.h>
#include <ctype.h>
#include "arkime.h"

typedef struct {
} CHANGEMEInfo_t;

/******************************************************************************/
void changeme_parser(ArkimeSession_t *session, void *uw, const unsigned char *data, int remaining)
{
    CHANGEMEInfo_t            *changeme          = uw;
}
/******************************************************************************/
void changeme_free(ArkimeSession_t UNUSED(*session), void *uw)
{
    CHANGEMEInfo_t            *changeme          = uw;

    ARKIME_TYPE_FREE(CHANGEMEInfo_t, changeme);
}
/******************************************************************************/
void changeme_classify(ArkimeSession_t *session, const unsigned char *data, int UNUSED(len))
{
    if (arkime_nids_has_tag(session, ARKIME_FIELD_TAGS, "protocol:changeme"))
        return;

    arkime_nids_add_tag(session, ARKIME_FIELD_TAGS, "protocol:changeme");

    CHANGEMEInfo_t            *changeme          = ARKIME_TYPE_ALLOC0(CHANGEMEInfo_t);

    arkime_parsers_register(session, changeme_parser, changeme, changeme_free);
}
/******************************************************************************/
void arkime_parser_init()
{
    arkime_parsers_classifier_register_tcp("changeme", 0, "\x12", 1, changeme_classify);
}

