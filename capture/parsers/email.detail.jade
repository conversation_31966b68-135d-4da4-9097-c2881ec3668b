if (user.emailSearch && session.email)
  div.sessionDetailMeta.bold Email
  dl.sessionDetailMeta(suffix="email")
    +ipArrayList(session.email, "ip", "IPs", "email")
    +arrayList(session.email, 'host', "Hosts", "host.email")
    +arrayList(session.email, 'header', "Header", "email.has-header")
    +arrayList(session.email, 'subject', "Subjects", "email.subject")
    +arrayList(session.email, 'src', "Senders", "email.src")
    +arrayList(session.email, 'dst', "Destinations", "email.dst")
    +arrayList(session.email, 'useragent', "User Agents", "email.x-mailer")
    +arrayList(session.email, 'id', "Message Ids", "email.message-id")
    +arrayList(session.email, 'mimeVersion', "Mime Versions", "email.mime-version")
    +arrayList(session.email, 'contentType', "Content Types", "email.content-type")
    +arrayList(session.email, 'filename', "Filenames", "email.fn")
    +arrayList(session.email, 'md5', "Attachment MD5s", "email.md5", null, null, session)
    +arrayList(session.email, 'sha256', "Attachment SHA256s", "email.sha256", null, null, session)
    +arrayList(session.email, "bodyMagic", "Attachment libfile", "email.bodymagic")
    +arrayList(session.email, 'smtpHello', "SMTP Hello", "email.smtp-hello")
    if (session.email.header)
      each value,i in session.email.header
        +arrayList(session.email, "header-" + value, value + " Header", "email." + value)
    if (session.email.headerField && session.email.headerValue)
      div.sessionDetailMeta.bold Email Header Detail
      dl.sessionDetailMeta(suffix="email-header-detail")
        +printHeader(session.email.headerField, session.email.headerValue, 'email.has-header.value')
