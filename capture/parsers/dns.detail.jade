if (session.dns)
  div.sessionDetailMeta.bold DNS
  dl.sessionDetailMeta(suffix="dns")
    +ipArrayList(session.dns, "ip", "IPs", "dns")
    +arrayList(session.dns, 'host', "Hosts", "host.dns")
    +arrayList(session.dns, 'puny', "Puny", "dns.puny")
    +arrayList(session.dns, "nameserverHost","Name Servers","host.dns.nameserver")
    +ipArrayListDNS(session.dns, "nameserver","Name Server IPs","dns.nameserver")
    +arrayList(session.dns, "mailserverHost","Mail Servers","host.dns.mailserver")
    +ipArrayListDNS(session.dns, "mailserver","Mail Server IPs","dns.mailserver")
    +arrayList(session.dns, 'opcode', "Op Code", "dns.opcode")
    +arrayList(session.dns, 'status', "Status Code", "dns.status")
    +arrayList(session.dns, 'qt', "Query Type", "dns.query.type")
    +arrayList(session.dns, 'qc', "Query Class", "dns.query.class")
    if (Array.isArray(session.dns))
      each dns,i in session.dns
        div.sessionDetailMeta.bold Query #{dns.qt} - #{dns.queryHost}
        dl.sessionDetailMeta(suffix=dns.queryHost)
          dt Query:
          dd
            +clickableLabel("dns.query.type", "Type")
            +clickableValue("dns.query.type", dns.qt)
            +clickableLabel("dns.query.class", "Class")
            +clickableValue("dns.query.class", dns.qc)
            +clickableLabel("dns.query.host", "Name")
            +clickableValue("dns.query.host", dns.queryHost)
            +clickableLabel("dns.opcode", "Op Code")
            +clickableValue("dns.opcode", dns.opcode)
            if (dns.status)
              +clickableLabel("dns.status", "Status Code")
              +clickableValue("dns.status", dns.status)
            if (dns.headerFlags)
              +clickableLabel("dns.header_flags", "Flags")
              +arrayPrint(dns, "headerFlags", "dns.header_flags")
          if (dns.answers)
            each answer,j in dns.answers
              dt Answer:
              dd
                +clickableLabel("dns.answer.type", "Type")
                +clickableValue("dns.answer.type", answer.type)
                +clickableLabel("dns.answer.class", "Class")
                +clickableValue("dns.answer.class", answer.class)
                +clickableLabel("dns.answer.ttl", "TTL")
                +clickableValue("dns.answer.ttl", answer.ttl)
                +clickableLabel("dns.answer.name", "Name")
                +clickableValue("dns.answer.name", answer.name)
                if (answer.ip)
                  +clickableLabel("dns.answer.ip", "IP")
                  +clickableValue("dns.answer.ip", answer.ip)
                if (answer.cname)
                  +clickableLabel("dns.answer.cname", "CNAME")
                  +clickableValue("dns.answer.cname", answer.cname)
                if (answer.nameserver)
                  +clickableLabel("dns.answer.ns", "NS")
                  +clickableValue("dns.answer.ns", answer.nameserver)
                if (answer.mx)
                  +clickableLabel("dns.answer.mx", "Priority")
                  +clickableValue("dns.answer.mx", answer.priority)
                  +clickableLabel("dns.answer.mx", "MX")
                  +clickableValue("dns.answer.mx", answer.mx)
                if (answer.txt)
                  +clickableLabel("dns.answer.txt", "TXT")
                  +clickableValue("dns.answer.txt", answer.txt)
                if (answer.https)
                  +clickableLabel("dns.answer.https", "HTTPS")
                  +clickableValue("dns.answer.https", answer.https)
                if (answer.caa)
                  +clickableLabel("dns.answer.caa", "CAA")
                  +clickableValue("dns.answer.caa", answer.caa)
