if (session.ssh)
  div.sessionDetailMeta.bold SSH
  dl.sessionDetailMeta
    +arrayList(session.ssh, "key", "Host Keys", "ssh.key")
    +arrayList(session.ssh, "version", "Versions", "ssh.ver")
    +arrayList(session.ssh, "hassh", "Hassh", "ssh.hassh")
    +arrayList(session.ssh, "hasshServer", "Hassh Server", "ssh.hasshServer")
    +arrayList(session.ssh, "ja4ssh", "JA4ssh", "ssh.ja4ssh")
    +arrayList(session.ssh, "ja4ssh_r", "JA4ssh_r", "ssh.ja4ssh_r")
