endpointMap = {
    ["************************************"] = "svcctl",
    ["86d35949-83c9-4044-b424-db363231fd0c"] = "ITaskSchedulerService",
    ["378e52b0-c0a9-11cf-822d-00aa0051e40f"] = "sasec",
    ["1ff70682-0a51-30e8-076d-740be8cee98b"] = "atsvc",
    ["0a74ef1c-41a4-4e06-83ae-dc74fb1cdd53"] = "idletask",
    ["906b0ce0-c70b-1067-b317-00dd010662da"] = "IXnRemote",
    ["ae33069b-a2a8-46ee-a235-ddfd339be281"] = "IRPCRemoteObject",
    ["0b6edbfa-4a24-4fc6-8a23-942b1eca65d1"] = "IRPCAsyncNotify",
    ["afa8bd80-7d8a-11c9-bef4-08002b102989"] = "mgmt",
    ["f5cc59b4-4264-101a-8c59-08002b2f8426"] = "FrsRpc",
    ["000001a0-0000-0000-c000-000000000046"] = "IRemoteSCMActivator",
    ["00000143-0000-0000-c000-000000000046"] = "IRemUnknown2",
    ["********-1234-abcd-ef00-0********9ab"] = "lsarpc",
    ["76f03f96-cdfd-44fc-a22c-64950a001209"] = "IRemoteWinspool",
    ["********-1234-abcd-ef00-01234567cffb"] = "netlogon",
    ["e3514235-4b06-11d1-ab04-00c04fc2dcd2"] = "drsuapi",
    ["5261574a-4572-206e-b268-6b199213b4e4"] = "AsyncEMSMDB",
    ["4d9f4ab8-7d1c-11cf-861e-0020af6e7c57"] = "IActivation",
    ["99fcfec4-5260-101b-bbcb-00aa0021347a"] = "IObjectExporter",
    ["e1af8308-5d1f-11c9-91a4-08002b14a0fa"] = "epmapper",
    ["********-1234-abcd-ef00-0********9ac"] = "samr",
    ["4b324fc8-1670-01d3-1278-5a47bf6ee188"] = "srvsvc",
    ["45f52c28-7f9f-101a-b52b-08002b2efabe"] = "winspipe",
    ["6bffd098-a112-3610-9833-46c3f87e345a"] = "wkssvc",
    ["3919286a-b10c-11d0-9ba8-00c04fd92ef5"] = "dssetup",
    ["********-1234-abcd-ef00-0********9ab"] = "spoolss",
    ["1544f5e0-613c-11d1-93df-00c04fd7bd09"] = "exchange_rfr",
    ["f5cc5a18-4264-101a-8c59-08002b2f8426"] = "nspi",
    ["a4f1db00-ca47-1067-b31f-00dd010662da"] = "exchange_mapi",
    ["9556dc99-828c-11cf-a37e-00aa003240c7"] = "IWbemServices",
    ["f309ad18-d86a-11d0-a075-00c04fb68820"] = "IWbemLevel1Login",
    ["d4781cd6-e5d3-44df-ad94-930efe48a887"] = "IWbemLoginClientID",
    ["44aca674-e8fc-11d0-a07c-00c04fb68820"] = "IWbemContext interface",
    ["674b6698-ee92-11d0-ad71-00c04fd8fdff"] = "IWbemContext unmarshaler",
    ["dc12a681-737f-11cf-884d-00aa004b2e24"] = "IWbemClassObject interface",
    ["4590f812-1d3a-11d0-891f-00aa004b2e24"] = "IWbemClassObject unmarshaler",
    ["9a653086-174f-11d2-b5f9-00104b703efd"] = "IWbemClassObject interface",
    ["c49e32c6-bc8b-11d2-85d4-00105a1f8304"] = "IWbemBackupRestoreEx interface",
    ["7c857801-7381-11cf-884d-00aa004b2e24"] = "IWbemObjectSink interface",
    ["027947e1-d731-11ce-a357-000000000001"] = "IEnumWbemClassObject interface",
    ["44aca675-e8fc-11d0-a07c-00c04fb68820"] = "IWbemCallResult interface",
    ["c49e32c7-bc8b-11d2-85d4-00105a1f8304"] = "IWbemBackupRestore interface",
    ["a359dec5-e813-4834-8a2a-ba7f1d777d76"] = "IWbemBackupRestoreEx interface",
    ["f1e9c5b2-f59b-11d2-b362-00105a1f8177"] = "IWbemRemoteRefresher interface",
    ["2c9273e0-1dc3-11d3-b364-00105a1f8177"] = "IWbemRefreshingServices interface",
    ["423ec01e-2e35-11d2-b604-00104b703efd"] = "IWbemWCOSmartEnum interface",
    ["1c1c45ee-4395-11d2-b60b-00104b703efd"] = "IWbemFetchSmartEnum interface",
    ["541679AB-2E5F-11d3-B34E-00104BCC4B4A"] = "IWbemLoginHelper interface",
    ["51c82175-844e-4750-b0d8-ec255555bc06"] = "KMS",
    ["50abc2a4-574d-40b3-9d66-ee4fd5fba076"] = "dnsserver",
    ["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"] = "AudioSrv",
    ["c386ca3e-9061-4a72-821e-498d83be188f"] = "AudioRpc",
    ["6bffd098-a112-3610-9833-012892020162"] = "browser",
    ["91ae6020-9e3c-11cf-8d7c-00aa00c091be"] = "ICertPassage",
    ["c8cb7687-e6d3-11d2-a958-00c04f682e16"] = "DAV RPC SERVICE",
    ["82273fdc-e32a-18c3-3f78-827929dc23ea"] = "eventlog",
    ["3d267954-eeb7-11d1-b94e-00c04fa3080d"] = "HydraLsPipe",
    ["894de0c0-0d55-11d3-a322-00c04fa321a1"] = "InitShutdown",
    ["d95afe70-a6d5-4259-822e-2c84da1ddb0d"] = "WindowsShutdown",
    ["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"] = "IKeySvc",
    ["68b58241-c259-4f03-a2e5-a2651dcbc930"] = "IKeySvc2",
    ["0d72a7d4-6148-11d1-b4aa-00c04fb66ea0"] = "ICertProtect",
    ["f50aac00-c7f3-428e-a022-a6b71bfb9d43"] = "ICatDBSvc",
    ["338cd001-2244-31f1-aaaa-************"] = "winreg",
    ["3dde7c30-165d-11d1-ab8f-00805f14db40"] = "BackupKey",
    ["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"] = "RpcSrvDHCPC",
    ["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d6"] = "dhcpcsvc6",
    ["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"] = "lcrpc",
    ["5ca4a760-ebb1-11cf-8611-00a0245420ed"] = "winstation_rpc",
    ["12b81e99-f207-4a4c-85d3-77b42f76fd14"] = "ISeclogon",
    ["d6d70ef0-0e3b-11cb-acc3-08002b1d29c3"] = "NsiS",
    ["d3fbb514-0e3b-11cb-8fad-08002b1d29c3"] = "NsiC",
    ["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"] = "NsiM",
    ["17fdd703-1827-4e34-79d4-24a55c53bb37"] = "msgsvc",
    ["5a7b91f8-ff00-11d0-a9b2-00c04fb6e6fc"] = "msgsvcsend",
    ["8d9f4e40-a03d-11ce-8f69-08003e30051b"] = "pnp",
    ["57674cd0-5200-11ce-a897-08002b2e9c6d"] = "lls_license",
    ["342cfd40-3c6c-11ce-a893-08002b2e9c6d"] = "llsrpc",
    ["4fc742e0-4a10-11cf-8273-00aa004ae673"] = "netdfs",
    ["83da7c00-e84f-11d2-9807-00c04f8ec850"] = "sfcapi",
    ["2f5f3220-c126-1076-b549-074d078619da"] = "nddeapi"
}

opCodeMap = {}

for k,v in pairs(endpointMap) do opCodeMap[k] = {} end

opCodeMap["1ff70682-0a51-30e8-076d-740be8cee98b"][0x00] = "NetrJobAdd"
opCodeMap["1ff70682-0a51-30e8-076d-740be8cee98b"][0x01] = "NetrJobDel"
opCodeMap["1ff70682-0a51-30e8-076d-740be8cee98b"][0x02] = "NetrJobEnum"
opCodeMap["1ff70682-0a51-30e8-076d-740be8cee98b"][0x03] = "NetrJobGetInfo"
opCodeMap["378e52b0-c0a9-11cf-822d-00aa0051e40f"][0x00] = "SASetAccountInformation"
opCodeMap["378e52b0-c0a9-11cf-822d-00aa0051e40f"][0x01] = "SASetNSAccountInformation"
opCodeMap["378e52b0-c0a9-11cf-822d-00aa0051e40f"][0x02] = "SAGetNSAccountInformation"
opCodeMap["378e52b0-c0a9-11cf-822d-00aa0051e40f"][0x03] = "SAGetAccountInformation"
opCodeMap["0a74ef1c-41a4-4e06-83ae-dc74fb1cdd53"][0x00] = "ItSrvRegisterIdleTask"
opCodeMap["0a74ef1c-41a4-4e06-83ae-dc74fb1cdd53"][0x01] = "ItSrvUnregisterIdleTask"
opCodeMap["0a74ef1c-41a4-4e06-83ae-dc74fb1cdd53"][0x02] = "ItSrvProcessIdleTasks"
opCodeMap["0a74ef1c-41a4-4e06-83ae-dc74fb1cdd53"][0x03] = "ItSrvSetDetectionParameters"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x00] = "SchRpcHighestVersion"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x01] = "SchRpcRegisterTask"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x02] = "SchRpcRetrieveTask"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x03] = "SchRpcCreateFolder"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x04] = "SchRpcSetSecurity"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x05] = "SchRpcGetSecurity"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x06] = "SchRpcEnumFolder"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x07] = "SchRpcEnumTasks"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x08] = "SchRpcEnumInstances"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x09] = "SchRpcGetInstanceInfo"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x0a] = "SchRpcStopInstance"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x0b] = "SchRpcStop"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x0c] = "SchRpcRun"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x0d] = "SchRpcDelete"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x0e] = "SchRpcRename"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x0f] = "SchRpcScheduledRuntimes"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x10] = "SchRpcGetLastRunInfo"
opCodeMap["86d35949-83c9-4044-b424-db363231fd0c"][0x11] = "SchRpcGetTaskInfo"
opCodeMap["99fcfec4-5260-101b-bbcb-00aa0021347a"][0x00] = "ResolveOxid"
opCodeMap["99fcfec4-5260-101b-bbcb-00aa0021347a"][0x01] = "SimplePing"
opCodeMap["99fcfec4-5260-101b-bbcb-00aa0021347a"][0x02] = "ComplexPing"
opCodeMap["99fcfec4-5260-101b-bbcb-00aa0021347a"][0x03] = "ServerAlive"
opCodeMap["99fcfec4-5260-101b-bbcb-00aa0021347a"][0x04] = "ResolveOxid2"
opCodeMap["99fcfec4-5260-101b-bbcb-00aa0021347a"][0x05] = "ServerAlive2"
opCodeMap["4d9f4ab8-7d1c-11cf-861e-0020af6e7c57"][0x00] = "RemoteActivation"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x00] = "NspiBind"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x01] = "NspiUnbind"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x02] = "NspiUpdateStat"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x03] = "NspiQueryRows"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x04] = "NspiSeekEntries"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x05] = "NspiGetMatches"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x06] = "NspiResortRestriction"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x07] = "NspiDNToEph"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x08] = "NspiGetPropList"
opCodeMap["f5cc5a18-4264-101a-8c59-08002b2f8426"][0x09] = "NspiGetProps"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x03] = "OpenNamespace"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x04] = "CancelAsyncCall"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x05] = "QueryObjectSink"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x06] = "GetObject"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x07] = "GetObjectAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x08] = "PutClass"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x09] = "PutClassAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x0a] = "DeleteClass"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x0b] = "DeleteClassAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x0c] = "CreateClassEnum"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x0d] = "CreateClassEnumAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x0e] = "PutInstance"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x0f] = "PutInstanceAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x10] = "DeleteClass"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x11] = "DeleteClassAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x12] = "CreateInstanceEnum"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x13] = "CreateInstanceEnumAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x14] = "ExecQuery"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x15] = "ExecQueryAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x16] = "ExecNotificationQuery"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x17] = "ExecNotificationQueryAsync"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x18] = "ExecMethod"
opCodeMap["9556dc99-828c-11cf-a37e-00aa003240c7"][0x19] = "ExecMethodAsync"
opCodeMap["f309ad18-d86a-11d0-a075-00c04fb68820"][0x03] = "EstablishPosition"
opCodeMap["f309ad18-d86a-11d0-a075-00c04fb68820"][0x04] = "RequestChallenge"
opCodeMap["f309ad18-d86a-11d0-a075-00c04fb68820"][0x05] = "WBEMLogin"
opCodeMap["f309ad18-d86a-11d0-a075-00c04fb68820"][0x06] = "NTLMLogin"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x00] = "FrsRpcSendCommPkt"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x01] = "FrsRpcVerifyPromotionParent"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x02] = "FrsRpcStartPromotionParent"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x03] = "FrsNOP"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x04] = "FrsBackupComplete"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x05] = "FrsBackupComplete"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x06] = "FrsBackupComplete"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x07] = "FrsBackupComplete"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x08] = "FrsBackupComplete"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x09] = "FrsBackupComplete"
opCodeMap["f5cc59b4-4264-101a-8c59-08002b2f8426"][0x0a] = "FrsRpcVerifyPromotionParentEx"
opCodeMap["00000143-0000-0000-c000-000000000046"][0x00] = "QueryInterface"
opCodeMap["00000143-0000-0000-c000-000000000046"][0x01] = "AddRef"
opCodeMap["00000143-0000-0000-c000-000000000046"][0x02] = "Release"
opCodeMap["00000143-0000-0000-c000-000000000046"][0x03] = "RemQueryInterface"
opCodeMap["00000143-0000-0000-c000-000000000046"][0x04] = "RemAddRef"
opCodeMap["00000143-0000-0000-c000-000000000046"][0x05] = "RemRelease"
opCodeMap["00000143-0000-0000-c000-000000000046"][0x06] = "RemQueryInterface2"
opCodeMap["000001a0-0000-0000-c000-000000000046"][0x00] = "QueryInterfaceIRemoteSCMActivator"
opCodeMap["000001a0-0000-0000-c000-000000000046"][0x01] = "AddRefIRemoteISCMActivator"
opCodeMap["000001a0-0000-0000-c000-000000000046"][0x02] = "ReleaseIRemoteISCMActivator"
opCodeMap["000001a0-0000-0000-c000-000000000046"][0x03] = "RemoteGetClassObject"
opCodeMap["000001a0-0000-0000-c000-000000000046"][0x04] = "RemoteCreateInstance"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x00] = "NetrLogonUasLogon"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x01] = "NetrLogonUasLogoff"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x02] = "NetrLogonSamLogon"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x03] = "NetrLogonSamLogoff"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x04] = "NetrServerReqChallenge"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x05] = "NetrServerAuthenticate"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x06] = "NetrServerPasswordSet"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x07] = "NetrDatabaseDeltas"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x08] = "NetrDatabaseSync"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x09] = "NetrAccountDeltas"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x0a] = "NetrAccountSync"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x0b] = "NetrGetDCName"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x0c] = "NetrLogonControl"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x0d] = "NetrGetAnyDCName"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x0e] = "NetrLogonControl2"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x0f] = "NetrServerAuthenticate2"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x10] = "NetrDatabaseSync2"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x11] = "NetrDatabaseRedo"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x12] = "NetrLogonControl2Ex"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x13] = "NetrEnumerateTrustedDomains"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x14] = "DsrGetDcName"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x15] = "NetrLogonGetCapabilities"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x16] = "NetrLogonSetServiceBits"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x17] = "NetrLogonGetTrustRid"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x18] = "NetrLogonComputeServerDigest"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x19] = "NetrLogonComputeClientDigest"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x1a] = "NetrServerAuthenticate3"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x1b] = "DsrGetDcNameEx"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x1c] = "DsrGetSiteName"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x1d] = "NetrLogonGetDomainInfo"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x1e] = "NetrServerPasswordSet2"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x1f] = "NetrServerPasswordGet"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x20] = "NetrLogonSendToSam"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x21] = "DsrAddressToSiteNamesW"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x22] = "DsrGetDcNameEx2"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x23] = "NetrLogonGetTimeServiceParentDomain"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x24] = "NetrEnumerateTrustedDomainsEx"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x25] = "DsrAddressToSiteNamesExW"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x26] = "DsrGetDcSiteCoverageW"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x27] = "NetrLogonSamLogonEx"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x28] = "DsrEnumerateDomainTrusts"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x29] = "DsrDeregisterDnsHostRecords"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x2a] = "NetrServerTrustPasswordsGet"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x2b] = "DsrGetForestTrustInformation"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x2c] = "NetrGetForestTrustInformation"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x2d] = "NetrLogonSamLogonWithFlags"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x2e] = "NetrServerGetTrustInfo"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x2f] = "unused"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x30] = "DsrUpdateReadOnlyServerDnsRecords"
opCodeMap["********-1234-abcd-ef00-01234567cffb"][0x31] = "NetrChainSetClientAttributes"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x00] = "RpcAsyncOpenPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x01] = "RpcAsyncAddPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x02] = "RpcAsyncSetJob"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x03] = "RpcAsyncGetJob"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x04] = "RpcAsyncEnumJobs"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x05] = "RpcAsyncAddJob"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x06] = "RpcAsyncScheduleJob"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x07] = "RpcAsyncDeletePrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x08] = "RpcAsyncSetPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x09] = "RpcAsyncGetPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x0a] = "RpcAsyncStartDocPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x0b] = "RpcAsyncStartPagePrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x0c] = "RpcAsyncWritePrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x0d] = "RpcAsyncEndPagePrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x0e] = "RpcAsyncEndDocPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x0f] = "RpcAsyncAbortPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x10] = "RpcAsyncGetPrinterData"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x11] = "RpcAsyncGetPrinterDataEx"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x12] = "RpcAsyncSetPrinterData"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x13] = "RpcAsyncSetPrinterDataEx"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x14] = "RpcAsyncClosePrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x15] = "RpcAsyncAddForm"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x16] = "RpcAsyncDeleteForm"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x17] = "RpcAsyncGetForm"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x18] = "RpcAsyncSetForm"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x19] = "RpcAsyncEnumForms"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x1a] = "RpcAsyncGetPrinterDriver"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x1b] = "RpcAsyncEnumPrinterData"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x1c] = "RpcAsyncEnumPrinterDataEx"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x1d] = "RpcAsyncEnumPrinterKey"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x1e] = "RpcAsyncDeletePrinterData"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x1f] = "RpcAsyncDeletePrinterDataEx"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x20] = "RpcAsyncDeletePrinterKey"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x21] = "RpcAsyncXcvData"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x22] = "RpcAsyncSendRecvBidiData"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x23] = "RpcAsyncCreatePrinterIC"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x24] = "RpcAsyncPlayGdiScriptOnPrinterIC"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x25] = "RpcAsyncDeletePrinterIC"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x26] = "RpcAsyncEnumPrinters"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x27] = "RpcAsyncAddPrinterDriver"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x28] = "RpcAsyncEnumPrinterDrivers"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x29] = "RpcAsyncGetPrinterDriverDirectory"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x2a] = "RpcAsyncDeletePrinterDriver"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x2b] = "RpcAsyncDeletePrinterDriverEx"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x2c] = "RpcAsyncAddPrintProcessor"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x2d] = "RpcAsyncEnumPrintProcessors"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x2e] = "RpcAsyncGetPrintProcessorDirectory"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x2f] = "RpcAsyncEnumPorts"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x30] = "RpcAsyncEnumMonitors"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x31] = "RpcAsyncAddPort"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x32] = "RpcAsyncSetPort"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x33] = "RpcAsyncAddMonitor"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x34] = "RpcAsyncDeleteMonitor"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x35] = "RpcAsyncDeletePrintProcessor"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x36] = "RpcAsyncEnumPrintProcessorDatatypes"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x37] = "RpcAsyncAddPerMachineConnection"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x38] = "RpcAsyncDeletePerMachineConnection"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x39] = "RpcAsyncEnumPerMachineConnections"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x3a] = "RpcSyncRegisterForRemoteNotifications"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x3b] = "RpcSyncUnRegisterForRemoteNotifications"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x3c] = "RpcSyncRefreshRemoteNotifications"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x3d] = "RpcAsyncGetRemoteNotifications"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x3e] = "RpcAsyncInstallPrinterDriverFromPackage"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x3f] = "RpcAsyncUploadPrinterDriverPackage"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x40] = "RpcAsyncGetCorePrinterDrivers"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x41] = "RpcAsyncCorePrinterDriverInstalled"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x42] = "RpcAsyncGetPrinterDriverPackagePath"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x43] = "RpcAsyncDeletePrinterDriverPackage"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x44] = "RpcAsyncReadPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x45] = "RpcAsyncResetPrinter"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x46] = "RpcAsyncGetJobNamedPropertyValue"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x47] = "RpcAsyncSetJobNamedProperty"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x48] = "RpcAsyncDeleteJobNamedProperty"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x49] = "RpcAsyncEnumJobNamedProperties"
opCodeMap["76f03f96-cdfd-44fc-a22c-64950a001209"][0x4a] = "RpcAsyncLogJobInfoForBranchOffice"
opCodeMap["894de0c0-0d55-11d3-a322-00c04fa321a1"][0x00]  = "BaseInitiateShutdown"
opCodeMap["894de0c0-0d55-11d3-a322-00c04fa321a1"][0x01]  = "BaseAbortShutdown"
opCodeMap["894de0c0-0d55-11d3-a322-00c04fa321a1"][0x02]  = "BaseInitiateShutdownEx"
opCodeMap["d95afe70-a6d5-4259-822e-2c84da1ddb0d"][0x00] = "WsdrInitiateShutdown"
opCodeMap["d95afe70-a6d5-4259-822e-2c84da1ddb0d"][0x01] = "WsdrAbortShutdown"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x00] = "RpcEnumPrinters"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x01] = "RpcOpenPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x02] = "RpcSetJob"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x03] = "RpcGetJob"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x04] = "RpcEnumJobs"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x05] = "RpcAddPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x06] = "RpcDeletePrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x07] = "RpcSetPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x08] = "RpcGetPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x09] = "RpcAddPrinterDriver"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0a] = "RpcEnumPrinterDrivers"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0b] = "RpcGetPrinterDriver"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0c] = "RpcGetPrinterDriverDirectory"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0d] = "RpcDeletePrinterDriver"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0e] = "RpcAddPrintProcessor"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0f] = "RpcEnumPrintProcessors"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x10] = "RpcGetPrintProcessorDirectory"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x11] = "RpcStartDocPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x12] = "RpcStartPagePrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x13] = "RpcWritePrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x14] = "RpcEndPagePrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x15] = "RpcAbortPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x16] = "RpcReadPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x17] = "RpcEndDocPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x18] = "RpcAddJob"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x19] = "RpcScheduleJob"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1a] = "RpcGetPrinterData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1b] = "RpcSetPrinterData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1c] = "RpcWaitForPrinterChange"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1d] = "RpcClosePrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1e] = "RpcAddForm"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1f] = "RpcDeleteForm"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x20] = "RpcGetForm"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x21] = "RpcSetForm"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x22] = "RpcEnumForms"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x23] = "RpcEnumPorts"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x24] = "RpcEnumMonitors"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x25] = "RpcAddPort"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x26] = "RpcConfigurePort"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x27] = "RpcDeletePort"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x28] = "RpcCreatePrinterIC"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x29] = "RpcPlayGdiScriptOnPrinterIC"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2a] = "RpcDeletePrinterIC"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2b] = "RpcAddPrinterConnection"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2c] = "RpcDeletePrinterConnection"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2d] = "RpcPrinterMessageBox"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2e] = "RpcAddMonitor"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2f] = "RpcDeleteMonitor"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x30] = "RpcDeletePrintProcessor"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x31] = "RpcAddPrintProvidor"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x32] = "RpcDeletePrintProvidor"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x33] = "RpcEnumPrintProcessorDatatypes"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x34] = "RpcResetPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x35] = "RpcGetPrinterDriver2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x36] = "RpcClientFindFirstPrinterChangeNotification"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x37] = "RpcFindNextPrinterChangeNotification"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x38] = "RpcFindClosePrinterChangeNotification"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x39] = "RpcRouterFindFirstPrinterChangeNotificationOld"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3a] = "RpcReplyOpenPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3b] = "RpcRouterReplyPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3c] = "RpcReplyClosePrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3d] = "RpcAddPortEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3e] = "RpcRemoteFindFirstPrinterChangeNotification"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3f] = "RpcSpoolerInit"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x40] = "RpcResetPrinterEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x41] = "RpcRemoteFindFirstPrinterChangeNotificationEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x42] = "RpcRouterReplyPrinterEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x43] = "RpcRouterRefreshPrinterChangeNotification"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x44] = "RpcSetAllocFailCount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x45] = "RpcSplOpenPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x46] = "RpcAddPrinterEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x47] = "RpcSetPort"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x48] = "RpcEnumPrinterData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x49] = "RpcDeletePrinterData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4a] = "RpcClusterSplOpen"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4b] = "RpcClusterSplClose"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4c] = "RpcClusterSplIsAlive"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4d] = "RpcSetPrinterDataEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4e] = "RpcGetPrinterDataEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4f] = "RpcEnumPrinterDataEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x50] = "RpcEnumPrinterKey"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x51] = "RpcDeletePrinterDataEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x52] = "RpcDeletePrinterKey"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x53] = "RpcSeekPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x54] = "RpcDeletePrinterDriverEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x55] = "RpcAddPerMachineConnection"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x56] = "RpcDeletePerMachineConnection"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x57] = "RpcEnumPerMachineConnections"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x58] = "RpcXcvData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x59] = "RpcAddPrinterDriverEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5a] = "RpcSplOpenPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5b] = "RpcGetSpoolFileInfo"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5c] = "RpcCommitSpoolData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5d] = "RpcCloseSpoolFileHandle"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5e] = "RpcFlushPrinter"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5f] = "RpcSendRecvBidiData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x60] = "RpcAddDriverCatalog"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x61] = "RpcAddPrinterConnection2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x62] = "RpcDeletePrinterConnection2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x63] = "RpcInstallPrinterDriverFromPackage"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x64] = "RpcUploadPrinterDriverPackage"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x65] = "RpcGetCorePrinterDrivers"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x66] = "RpcCorePrinterDriverInstalled"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x67] = "RpcGetPrinterDriverPackagePath"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x68] = "RpcReportJobProcessingProgress"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x00] = "NetrCharDevEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x01] = "NetrCharDevGetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x02] = "NetrCharDevControl"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x03] = "NetrCharDevQEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x04] = "NetrCharDevQGetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x05] = "NetrCharDevQSetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x06] = "NetrCharDevQPurge"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x07] = "NetrCharDevQPurgeSelf"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x08] = "NetrConnectionEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x09] = "NetrFileEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x0a] = "NetrFileGetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x0b] = "NetrFileClose"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x0c] = "NetrSessionEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x0d] = "NetrSessionDel"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x0e] = "NetrShareAdd"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x0f] = "NetrShareEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x10] = "NetrShareGetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x11] = "NetrShareSetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x12] = "NetrShareDel"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x13] = "NetrShareDelSticky"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x14] = "NetrShareCheck"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x15] = "NetrServerGetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x16] = "NetrServerSetInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x17] = "NetrServerDiskEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x18] = "NetrServerStatisticsGet"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x19] = "NetrServerTransportAdd"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x1a] = "NetrServerTransportEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x1b] = "NetrServerTransportDel"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x1c] = "NetrRemoteTOD"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x1d] = "NetrServerSetServiceBits"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x1e] = "NetprPathType"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x1f] = "NetprPathCanonicalize"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x20] = "NetprPathCompare"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x21] = "NetprNameValidate"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x22] = "NetprNameCanonicalize"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x23] = "NetprNameCompare"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x24] = "NetrShareEnumSticky"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x25] = "NetrShareDelStart"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x26] = "NetrShareDelCommit"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x27] = "NetrpGetFileSecurity"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x28] = "NetrpSetFileSecurity"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x29] = "NetrServerTransportAddEx"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x2a] = "NetrServerSetServiceBitsEx"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x2b] = "NetrDfsGetVersion"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x2c] = "NetrDfsCreateLocalPartition"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x2d] = "NetrDfsDeleteLocalPartition"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x2e] = "NetrDfsSetLocalVolumeState"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x2f] = "NetrDfsSetServerInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x30] = "NetrDfsCreateExitPoint"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x31] = "NetrDfsDeleteExitPoint"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x32] = "NetrDfsModifyPrefix"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x33] = "NetrDfsFixLocalVolume"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x34] = "NetrDfsManagerReportSiteInfo"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x35] = "NetrServerTransportDelEx"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x37] = "NetrServerAliasEnum"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x38] = "NetrServerAliasDel"
opCodeMap["4b324fc8-1670-01d3-1278-5a47bf6ee188"][0x39] = "NetrShareDelEx"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x00] = "SamrConnect"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x01] = "SamrCloseHandle"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x02] = "SamrSetSecurityObject"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x03] = "SamrQuerySecurityObject"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x04] = "SamrShutdownSamServer"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x05] = "SamrLookupDomainInSamServer"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x06] = "SamrEnumerateDomainsInSamServer"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x07] = "SamrOpenDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x08] = "SamrQueryInformationDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x09] = "SamrSetInformationDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x0a] = "SamrCreateGroupInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x0b] = "SamrEnumerateGroupsInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x0c] = "SamrCreateUserInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x0d] = "SamrEnumerateUsersInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x0e] = "SamrCreateAliasInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x0f] = "SamrEnumerateAliasesInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x10] = "SamrGetAliasMembership"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x11] = "SamrLookupNamesInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x12] = "SamrLookupIdsInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x13] = "SamrOpenGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x14] = "SamrQueryInformationGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x15] = "SamrSetInformationGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x16] = "SamrAddMemberToGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x17] = "SamrDeleteGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x18] = "SamrRemoveMemberFromGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x19] = "SamrGetMembersInGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x1a] = "SamrSetMemberAttributesOfGroup"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x1b] = "SamrOpenAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x1c] = "SamrQueryInformationAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x1d] = "SamrSetInformationAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x1e] = "SamrDeleteAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x1f] = "SamrAddMemberToAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x20] = "SamrRemoveMemberFromAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x21] = "SamrGetMembersInAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x22] = "SamrOpenUser"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x23] = "SamrDeleteUser"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x24] = "SamrQueryInformationUser"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x25] = "SamrSetInformationUser"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x26] = "SamrChangePasswordUser"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x27] = "SamrGetGroupsForUser"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x28] = "SamrQueryDisplayInformation"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x29] = "SamrGetDisplayEnumerationIndex"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x2a] = "SamrTestPrivateFunctionsDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x2b] = "SamrTestPrivateFunctionsUser"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x2c] = "SamrGetUserDomainPasswordInformation"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x2d] = "SamrRemoveMemberFromForeignDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x2e] = "SamrQueryInformationDomain2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x2f] = "SamrQueryInformationUser2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x30] = "SamrQueryDisplayInformation2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x31] = "SamrGetDisplayEnumerationIndex2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x32] = "SamrCreateUser2InDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x33] = "SamrQueryDisplayInformation3"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x34] = "SamrAddMultipleMembersToAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x35] = "SamrRemoveMultipleMembersFromAlias"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x36] = "SamrOemChangePasswordUser2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x37] = "SamrUnicodeChangePasswordUser2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x38] = "SamrGetDomainPasswordInformation"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x39] = "SamrConnect2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x3a] = "SamrSetInformationUser2"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x3b] = "SamrSetBootKeyInformation"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x3c] = "SamrGetBootKeyInformation"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x3d] = "SamrConnect3"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x3e] = "SamrConnect4"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x3f] = "SamrUnicodeChangePasswordUser3"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x40] = "SamrConnect5"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x41] = "SamrRidToSid"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x42] = "SamrSetDSRMPassword"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x43] = "SamrValidatePassword"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x44] = "SamrQueryLocalizableAccountsInDomain"
opCodeMap["********-1234-abcd-ef00-0********9ac"][0x45] = "SamrPerformGenericOperation"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x00] = "OpenClassesRoot"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x01] = "OpenCurrentUser"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x02] = "OpenLocalMachine"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x03] = "OpenPerformanceData"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x04] = "OpenUsers"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x05] = "BaseRegCloseKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x06] = "BaseRegCreateKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x07] = "BaseRegDeleteKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x08] = "BaseRegDeleteValue"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x09] = "BaseRegEnumKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x0a] = "BaseRegEnumValue"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x0b] = "BaseRegFlushKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x0c] = "BaseRegGetKeySecurity"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x0d] = "BaseRegLoadKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x0e] = "BaseRegNotifyChangeKeyValue"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x0f] = "BaseRegOpenKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x10] = "BaseRegQueryInfoKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x11] = "BaseRegQueryValue"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x12] = "BaseRegReplaceKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x13] = "BaseRegRestoreKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x14] = "BaseRegSaveKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x15] = "BaseRegSetKeySecurity"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x16] = "BaseRegSetValue"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x17] = "BaseRegUnLoadKey"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x18] = "BaseInitiateSystemShutdown"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x19] = "BaseAbortSystemShutdown"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x1a] = "BaseRegGetVersion"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x1b] = "OpenCurrentConfig"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x1c] = "OpenDynData"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x1d] = "BaseRegQueryMultipleValues"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x1e] = "BaseInitiateSystemShutdownEx"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x1f] = "BaseRegSaveKeyEx"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x20] = "OpenPerformanceText"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x21] = "OpenPerformanceNlsText"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x22] = "BaseRegQueryMultipleValues2"
opCodeMap["338cd001-2244-31f1-aaaa-************"][0x23] = "BaseRegDeleteKeyEx"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x00] = "DsRolerGetPrimaryDomainInformation"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x01] = "DsRolerDnsNameToFlatName"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x02] = "DsRolerDcAsDc"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x03] = "DsRolerDcAsReplica"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x04] = "DsRolerDemoteDc"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x05] = "DsRolerGetDcOperationProgress"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x06] = "DsRolerGetDcOperationResults"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x07] = "DsRolerCancel"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x08] = "DsRolerServerSaveStateForUpgrade"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x09] = "DsRolerUpgradeDownlevelServer"
opCodeMap["3919286a-b10c-11d0-9ba8-00c04fd92ef5"][0x0a] = "DsRolerAbortDownlevelServerUpgrade"
opCodeMap["************************************"][0x00] = "CloseServiceHandle"
opCodeMap["************************************"][0x01] = "ControlService"
opCodeMap["************************************"][0x02] = "DeleteService"
opCodeMap["************************************"][0x03] = "LockServiceDatabase"
opCodeMap["************************************"][0x04] = "QueryServiceObjectSecurity"
opCodeMap["************************************"][0x05] = "SetServiceObjectSecurity"
opCodeMap["************************************"][0x06] = "QueryServiceStatus"
opCodeMap["************************************"][0x07] = "SetServiceStatus"
opCodeMap["************************************"][0x08] = "UnlockServiceDatabase"
opCodeMap["************************************"][0x09] = "NotifyBootConfigStatus"
opCodeMap["************************************"][0x0a] = "ScSetServiceBitsW"
opCodeMap["************************************"][0x0b] = "ChangeServiceConfigW"
opCodeMap["************************************"][0x0c] = "CreateServiceW"
opCodeMap["************************************"][0x0d] = "EnumDependentServicesW"
opCodeMap["************************************"][0x0e] = "EnumServicesStatusW"
opCodeMap["************************************"][0x0f] = "OpenSCManagerW"
opCodeMap["************************************"][0x10] = "OpenServiceW"
opCodeMap["************************************"][0x11] = "QueryServiceConfigW"
opCodeMap["************************************"][0x12] = "QueryServiceLockStatusW"
opCodeMap["************************************"][0x13] = "StartServiceW"
opCodeMap["************************************"][0x14] = "GetServiceDisplayNameW"
opCodeMap["************************************"][0x15] = "GetServiceKeyNameW"
opCodeMap["************************************"][0x16] = "ScSetServiceBitsA"
opCodeMap["************************************"][0x17] = "ChangeServiceConfigA"
opCodeMap["************************************"][0x18] = "CreateServiceA"
opCodeMap["************************************"][0x19] = "EnumDependentServicesA"
opCodeMap["************************************"][0x1a] = "EnumServicesStatusA"
opCodeMap["************************************"][0x1b] = "OpenSCManagerA"
opCodeMap["************************************"][0x1c] = "OpenServiceA"
opCodeMap["************************************"][0x1d] = "QueryServiceConfigA"
opCodeMap["************************************"][0x1e] = "QueryServiceLockStatusA"
opCodeMap["************************************"][0x1f] = "StartServiceA"
opCodeMap["************************************"][0x20] = "GetServiceDisplayNameA"
opCodeMap["************************************"][0x21] = "GetServiceKeyNameA"
opCodeMap["************************************"][0x22] = "ScGetCurrentGroupStateW"
opCodeMap["************************************"][0x23] = "EnumServiceGroupW"
opCodeMap["************************************"][0x24] = "ChangeServiceConfig2A"
opCodeMap["************************************"][0x25] = "ChangeServiceConfig2W"
opCodeMap["************************************"][0x26] = "QueryServiceConfig2A"
opCodeMap["************************************"][0x27] = "QueryServiceConfig2W"
opCodeMap["************************************"][0x28] = "QueryServiceStatusEx"
opCodeMap["************************************"][0x29] = "EnumServicesStatusExA"
opCodeMap["************************************"][0x2a] = "EnumServicesStatusExW"
opCodeMap["************************************"][0x2b] = "ScSendTSMessage"
opCodeMap["************************************"][0x2c] = "CreateServiceWOW64A"
opCodeMap["************************************"][0x2d] = "CreateServiceWOW64W"
opCodeMap["************************************"][0x2e] = "ScQueryServiceTagInfo"
opCodeMap["************************************"][0x2f] = "NotifyServiceStatusChange"
opCodeMap["************************************"][0x30] = "GetNotifyResult"
opCodeMap["************************************"][0x31] = "CloseNotifyHandle"
opCodeMap["************************************"][0x32] = "ControlServiceExA"
opCodeMap["************************************"][0x33] = "ControlServiceExW"
opCodeMap["************************************"][0x34] = "ScSendPnPMessage"
opCodeMap["************************************"][0x35] = "ScValidatePnPService"
opCodeMap["************************************"][0x36] = "ScOpenServiceStatusHandle"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x00] = "BrowserrServerEnum"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x01] = "BrowserrDebugCall"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x02] = "BrowserrQueryOtherDomains"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x03] = "BrowserrResetNetlogonState"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x04] = "BrowserrDebugTrace"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x05] = "BrowserrQueryStatistics"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x06] = "BrowserrResetStatistics"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x07] = "NetrBrowserStatisticsClear"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x08] = "NetrBrowserStatisticsGet"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x09] = "BrowserrSetNetlogonState"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x0a] = "BrowserrQueryEmulatedDomains"
opCodeMap["6bffd098-a112-3610-9833-012892020162"][0x0b] = "BrowserrServerEnumEx"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x00] = "gfxCreateZoneFactoriesList"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x01] = "gfxCreateGfxFactoriesList"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x02] = "gfxCreateGfxList"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x03] = "gfxRemoveGfx"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x04] = "gfxAddGfx"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x05] = "gfxModifyGx"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x06] = "gfxOpenGfx"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x07] = "gfxLogon"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x08] = "gfxLogoff"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x09] = "winmmRegisterSessionNotificationEvent"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x0a] = "winmmUnregisterSessionNotification"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x0b] = "winmmSessionConnectState"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x0c] = "wdmDriverOpenDrvRegKey"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x0d] = "winmmAdvisePreferredDeviceChange"
opCodeMap["3faf4738-3a21-4307-b46c-fdda9bb8c0d5"][0x0e] = "winmmGetPnpInfo"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x00] = "AudioServerConnect"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x01] = "AudioServerDisconnect"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x02] = "AudioServerInitialize"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x03] = "AudioServerGetAudioSession"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x04] = "AudioServerCreateStream"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x05] = "AudioServerDestroyStream"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x06] = "AudioServerGetStreamLatency"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x07] = "AudioServerGetMixFormat"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x08] = "AudioServerIsFormatSupported"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x09] = "AudioServerGetDevicePeriod"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x0a] = "AudioVolumeGetMasterVolumeLevelScalar"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x0b] = "AudioSessionGetProcessId"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x0c] = "AudioSessionGetState"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x0d] = "AudioSessionGetLastActivation"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x0e] = "AudioSessionGetLastInactivation"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x0f] = "AudioSessionIsSystemSoundsSession"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x10] = "AudioSessionGetDisplayName"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x11] = "AudioSessionSetDisplayName"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x12] = "AudioSessionGetSessionClass"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x13] = "AudioSessionSetSessionClass"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x14] = "AudioSessionGetVolume"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x15] = "AudioSessionSetVolume"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x16] = "AudioSessionGetMute"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x17] = "AudioSessionSetMute"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x18] = "AudioSessionGetChannelCount"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x19] = "AudioSessionSetChannelVolume"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x1a] = "AudioSessionGetChannelVolume"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x1b] = "AudioSessionSetAllVolumes"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x1c] = "AudioSessionGetAllVolumes"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x1d] = "AudioServerDisconnect"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x1e] = "AudioServerGetMixFormat"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x1f] = "PolicyConfigGetDeviceFormat"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x20] = "PolicyConfigSetDeviceFormat"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x21] = "AudioServerGetDevicePeriod"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x22] = "PolicyConfigSetProcessingPeriod"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x23] = "PolicyConfigGetShareMode"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x24] = "PolicyConfigSetShareMode"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x25] = "GetAudioSessionManager"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x26] = "AudioSessionManagerDestroy"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x27] = "AudioSessionManagerGetAudioSession"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x28] = "AudioSessionManagerGetCurrentSession"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x29] = "AudioSessionManagerGetExistingSession"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x2a] = "AudioSessionManagerAddAudioSessionClientNotification"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x2b] = "AudioSessionManagerDeleteAudioSessionClientNotification"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x2c] = "AudioSessionManagerAddAudioSessionClientNotification"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x2d] = "AudioVolumeConnect"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x2e] = "AudioVolumeDisconnect"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x2f] = "AudioVolumeGetChannelCount"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x30] = "AudioVolumeSetMasterVolumeLevel"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x31] = "AudioVolumeSetMasterVolumeLevelScalar"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x32] = "AudioVolumeGetMasterVolumeLevel"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x33] = "AudioVolumeGetMasterVolumeLevelScalar"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x34] = "AudioVolumeSetChannelVolumeLevel"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x35] = "AudioVolumeSetChannelVolumeLevelScalar"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x36] = "AudioVolumeGetChannelVolumeLevel"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x37] = "AudioVolumeGetChannelVolumeLevelScalar"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x38] = "AudioVolumeSetMute"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x39] = "AudioSessionGetDisplayName"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x3a] = "AudioVolumeAddMasterVolumeNotification"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x3b] = "AudioVolumeDeleteMasterVolumeNotification"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x3c] = "AudioMeterGetAverageRMS"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x3d] = "AudioMeterGetChannelsRMS"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x3e] = "AudioMeterGetPeakValue"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x3f] = "AudioMeterGetChannelsPeakValues"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x40] = "AudioVolumeGetStepInfo"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x41] = "AudioVolumeStepUp"
opCodeMap["c386ca3e-9061-4a72-821e-498d83be188f"][0x42] = "AudioVolumeStepDown"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d6"][0x00] = "RpcSrvRequestPrefix"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d6"][0x01] = "RpcSrvRenewPrefix"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d6"][0x02] = "RpcSrvReleasePrefix"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d6"][0x03] = "RpcSrvRequestParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x00] = "RpcSrvEnableDhcp"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x01] = "RpcSrvRenewLease"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x02] = "RpcSrvRenewLeaseByBroadcast"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x03] = "RpcSrvReleaseLease"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x04] = "RpcSrvSetFallbackParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x05] = "RpcSrvGetFallbackParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x06] = "RpcSrvFallbackRefreshParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x07] = "RpcSrvStaticRefreshParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x08] = "RpcSrvRemoveDnsRegistrations"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x09] = "RpcSrvRequestParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x0a] = "RpcSrvPersistentRequestParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x0b] = "RpcSrvRegisterParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x0c] = "RpcSrvDeRegisterParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x0d] = "RpcSrvEnumInterfaces"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x0e] = "RpcSrvQueryLeaseInfo"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x0f] = "RpcSrvSetClassId"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x10] = "RpcSrvGetClassId"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x11] = "RpcSrvSetClientId"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x12] = "RpcSrvGetClientId"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x13] = "RpcSrvNotifyMediaReconnected"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x14] = "RpcSrvGetOriginalSubnetMask"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x15] = "RpcSrvSetMSFTVendorSpecificOptions"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x16] = "RpcSrvRequestCachedParams"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x17] = "RpcSrvRegisterConnectionStateNotification"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x18] = "RpcSrvDeRegisterConnectionStateNotification"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x19] = "RpcSrvGetNotificationStatus"
opCodeMap["3c4728c5-f0ab-448b-bda1-6ce01eb0a6d5"][0x1a] = "RpcSrvGetDhcpServicedConnections"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x00] = "RpcLicensingOpenServer"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x01] = "RpcLicensingCloseServer"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x02] = "RpcLicensingLoadPolicy"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x03] = "RpcLicensingUnloadPolicy"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x04] = "RpcLicensingSetPolicy"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x05] = "RpcLicensingGetAvailablePolicyIds"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x06] = "RpcLicensingGetPolicy"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x07] = "RpcLicensingGetPolicyInformation"
opCodeMap["2f59a331-bf7d-48cb-9ec5-7c090d76e8b8"][0x08] = "RpcLicensingDeactivateCurrentPolicy"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x00] = "RpcWinStationOpenServer"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x01] = "RpcWinStationCloseServer"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x02] = "RpcIcaServerPing"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x03] = "RpcWinStationEnumerate"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x04] = "RpcWinStationRename"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x05] = "RpcWinStationQueryInformation"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x06] = "RpcWinStationSetInformation"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x07] = "RpcWinStationSendMessage"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x08] = "RpcLogonIdFromWinStationName"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x09] = "RpcWinStationNameFromLogonId"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x0a] = "RpcWinStationConnect"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x0b] = "RpcWinStationVirtualOpen"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x0c] = "RpcWinStationBeepOpen"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x0d] = "RpcWinStationDisconnect"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x0e] = "RpcWinStationReset"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x0f] = "RpcWinStationShutdownSystem"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x10] = "RpcWinStationWaitSystemEvent"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x11] = "RpcWinStationShadow"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x12] = "RpcWinStationShadowTargetSetup"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x13] = "RpcWinStationShadowTarget"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x14] = "RpcWinStationGenerateLicense"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x15] = "RpcWinStationInstallLicense"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x16] = "RpcWinStationEnumerateLicenses"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x17] = "RpcWinStationActivateLicense"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x18] = "RpcWinStationRemoveLicense"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x19] = "RpcWinStationQueryLicense"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x1a] = "RpcWinStationSetPoolCount"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x1b] = "RpcWinStationQueryUpdateRequired"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x1c] = "RpcWinStationCallback"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x1d] = "RpcWinStationGetApplicationInfo"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x1e] = "RpcWinStationReadRegistry"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x1f] = "RpcWinStationWaitForConnect"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x20] = "RpcWinStationNotifyLogon"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x21] = "RpcWinStationNotifyLogoff"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x22] = "RpcWinStationEnumerateProcesses"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x23] = "RpcWinStationAnnoyancePopup"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x24] = "RpcWinStationEnumerateProcesses"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x25] = "RpcWinStationTerminateProcess"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x26] = "RpcServerNWLogonSetAdmin"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x27] = "RpcServerNWLogonQueryAdmin"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x28] = "RpcWinStationNtsdDebug"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x29] = "RpcWinStationBreakPoint"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x2a] = "RpcWinStationCheckForApplicationName"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x2b] = "RpcWinStationGetAllProcesses"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x2c] = "RpcWinStationGetProcessSid"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x2d] = "RpcWinStationGetTermSrvCountersValue"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x2e] = "RpcWinStationReInitializeSecurity"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x2f] = "RpcWinStationBroadcastSystemMessage"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x30] = "RpcWinStationSendWindowMessage"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x31] = "RpcWinStationNotifyNewSession"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x32] = "RpcServerGetInternetConnectorStatus"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x33] = "RpcServerSetInternetConnectorStatus"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x34] = "RpcServerQueryInetConnectorInformation"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x35] = "RpcWinStationGetLanAdapterName"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x36] = "RpcWinStationUpdateUserConfig"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x37] = "RpcWinStationQueryLogonCredentials"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x38] = "RpcWinStationRegisterConsoleNotification"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x39] = "RpcWinStationUnRegisterConsoleNotification"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x3a] = "RpcWinStationUpdateSettings"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x3b] = "RpcWinStationShadowStop"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x3c] = "RpcWinStationCloseServerEx"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x3d] = "RpcWinStationIsHelpAssistantSession"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x3e] = "RpcWinStationGetMachinePolicy"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x3f] = "RpcWinStationUpdateClientCachedCredentials"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x40] = "RpcWinStationFUSCanRemoteUserDisconnect"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x41] = "RpcWinStationCheckLoopBack"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x42] = "RpcConnectCallback"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x43] = "RpcWinStationNotifyDisconnectPipe"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x44] = "RpcWinStationSessionInitialized"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x45] = "RpcRemoteAssistancePrepareSystemRestore"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x46] = "RpcWinStationGetAllProcesses_NT6"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x47] = "RpcWinStationRegisterNotificationEvent"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x48] = "RpcWinStationUnRegisterNotificationEvent"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x49] = "RpcWinStationAutoReconnect"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x4a] = "RpcWinStationCheckAccess"
opCodeMap["5ca4a760-ebb1-11cf-8611-00a0245420ed"][0x4b] = "RpcWinStationOpenSessionDirectory"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c3"][0x00] = "nsi_binding_export"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c3"][0x01] = "nsi_binding_unexport"
opCodeMap["d3fbb514-0e3b-11cb-8fad-08002b1d29c3"][0x00] = "nsi_binding_lookup_begin"
opCodeMap["d3fbb514-0e3b-11cb-8fad-08002b1d29c3"][0x01] = "nsi_binding_lookup_done"
opCodeMap["d3fbb514-0e3b-11cb-8fad-08002b1d29c3"][0x02] = "nsi_binding_lookup_next"
opCodeMap["d3fbb514-0e3b-11cb-8fad-08002b1d29c3"][0x03] = "nsi_mgmt_handle_set_exp_age"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x00] = "nsi_group_delete"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x01] = "nsi_group_mbr_add"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x02] = "nsi_group_mbr_remove"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x03] = "nsi_group_mbr_inq_begin"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x04] = "nsi_group_mbr_inq_next"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x05] = "nsi_group_mbr_inq_done"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x06] = "nsi_profile_delete"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x07] = "nsi_profile_elt_add"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x08] = "nsi_profile_elt_remove"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x09] = "nsi_profile_elt_inq_begin"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x0a] = "nsi_profile_elt_inq_next"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x0b] = "nsi_profile_elt_inq_done"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x0c] = "nsi_entry_object_inq_begin"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x0d] = "nsi_entry_object_inq_next"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x0e] = "nsi_entry_object_inq_done"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x0f] = "nsi_entry_expand_name"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x10] = "nsi_mgmt_binding_unexport"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x11] = "nsi_mgmt_entry_delete"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x12] = "nsi_mgmt_entry_create"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x13] = "nsi_mgmt_entry_inq_if_ids"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x14] = "nsi_mgmt_inq_exp_age"
opCodeMap["d6d70ef0-0e3b-11cb-acc3-08002b1d29c4"][0x15] = "nsi_mgmt_inq_set_age"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x00] = "ElfrClearELFW"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x01] = "ElfrBackupELFW"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x02] = "ElfrCloseEL"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x03] = "ElfrDeregisterEventSource"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x04] = "ElfrNumberOfRecords"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x05] = "ElfrOldestRecord"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x06] = "ElfrChangeNotify"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x07] = "ElfrOpenELW"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x08] = "ElfrRegisterEventSourceW"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x09] = "ElfrOpenBELW"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x0a] = "ElfrReadELW"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x0b] = "ElfrReportEventW"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x0c] = "ElfrClearELFA"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x0d] = "ElfrBackupELFA"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x0e] = "ElfrOpenELA"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x0f] = "ElfrRegisterEventSourceA"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x10] = "ElfrOpenBELA"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x11] = "ElfrReadELA"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x12] = "ElfrReportEventA"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x13] = "ElfrRegisterClusterSvc"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x14] = "ElfrDeregisterClusterSvc"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x15] = "ElfrWriteClusterEvents"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x16] = "ElfrGetLogInformation"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x17] = "ElfrFlushEL"
opCodeMap["82273fdc-e32a-18c3-3f78-827929dc23ea"][0x18] = "ElfrReportEventAndSourceW"
opCodeMap["12b81e99-f207-4a4c-85d3-77b42f76fd14"][0x00] = "SeclCreateProcessWithLogonW"
opCodeMap["12b81e99-f207-4a4c-85d3-77b42f76fd14"][0x01] = "SeclCreateProcessWithLogonExW"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x00] = "KeyrOpenKeyService"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x01] = "KeyrEnumerateProviders"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x02] = "KeyrEnumerateProviderTypes"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x03] = "KeyrEnumerateProvContainers"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x04] = "KeyrCloseKeyService"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x05] = "KeyrGetDefaultProvider"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x06] = "KeyrSetDefaultProvider"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x07] = "KeyrEnroll"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x08] = "KeyrExportCert"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x09] = "KeyrImportCert"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x0a] = "KeyrEnumerateAvailableCertTypes"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x0b] = "KeyrEnumerateCAs"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x0c] = "KeyrEnroll_V2"
opCodeMap["8d0ffe72-d252-11d0-bf8f-00c04fd9126b"][0x0d] = "KeyrQueryRequestStatus"
opCodeMap["68b58241-c259-4f03-a2e5-a2651dcbc930"][0x00] = "KSrSubmitRequest"
opCodeMap["68b58241-c259-4f03-a2e5-a2651dcbc930"][0x01] = "KSrGetTemplates"
opCodeMap["68b58241-c259-4f03-a2e5-a2651dcbc930"][0x02] = "KSrGetCAs"
opCodeMap["0d72a7d4-6148-11d1-b4aa-00c04fb66ea0"][0x00] = "SSCertProtectFunction"
opCodeMap["f50aac00-c7f3-428e-a022-a6b71bfb9d43"][0x00] = "SSCatDBAddCatalog"
opCodeMap["f50aac00-c7f3-428e-a022-a6b71bfb9d43"][0x01] = "SSCatDBDeleteCatalog"
opCodeMap["f50aac00-c7f3-428e-a022-a6b71bfb9d43"][0x02] = "SSCatDBEnumCatalogs"
opCodeMap["f50aac00-c7f3-428e-a022-a6b71bfb9d43"][0x03] = "SSCatDBRegisterForChangeNotification"
opCodeMap["f50aac00-c7f3-428e-a022-a6b71bfb9d43"][0x04] = "KeyrCloseKeyService"
opCodeMap["f50aac00-c7f3-428e-a022-a6b71bfb9d43"][0x05] = "SSCatDBRebuildDatabase"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x00] = "LsarClose"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x01] = "LsarDelete"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x02] = "LsarEnumeratePrivileges"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x03] = "LsarQuerySecurityObject"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x04] = "LsarSetSecurityObject"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x05] = "LsarChangePassword"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x06] = "LsarOpenPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x07] = "LsarQueryInformationPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x08] = "LsarSetInformationPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x09] = "LsarClearAuditLog"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0a] = "LsarCreateAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0b] = "LsarEnumerateAccounts"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0c] = "LsarCreateTrustedDomain"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0d] = "LsarEnumerateTrustedDomains"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0e] = "LsarLookupNames"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x0f] = "LsarLookupSids"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x10] = "LsarCreateSecret"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x11] = "LsarOpenAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x12] = "LsarEnumeratePrivilegesAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x13] = "LsarAddPrivilegesToAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x14] = "LsarRemovePrivilegesFromAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x15] = "LsarGetQuotasForAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x16] = "LsarSetQuotasForAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x17] = "LsarGetSystemAccessAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x18] = "LsarSetSystemAccessAccount"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x19] = "LsarOpenTrustedDomain"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1a] = "LsarQueryInfoTrustedDomain"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1b] = "LsarSetInformationTrustedDomain"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1c] = "LsarOpenSecret"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1d] = "LsarSetSecret"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1e] = "LsarQuerySecret"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x1f] = "LsarLookupPrivilegeValue"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x20] = "LsarLookupPrivilegeName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x21] = "LsarLookupPrivilegeDisplayName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x22] = "LsarDeleteObject"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x23] = "LsarEnumerateAccountsWithUserRight"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x24] = "LsarEnumerateAccountRights"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x25] = "LsarAddAccountRights"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x26] = "LsarRemoveAccountRights"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x27] = "LsarQueryTrustedDomainInfo"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x28] = "LsarSetTrustedDomainInfo"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x29] = "LsarDeleteTrustedDomain"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2a] = "LsarStorePrivateData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2b] = "LsarRetrievePrivateData"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2c] = "LsarOpenPolicy2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2d] = "LsarGetUserName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2e] = "LsarQueryInformationPolicy2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x2f] = "LsarSetInformationPolicy2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x30] = "LsarQueryTrustedDomainInfoByName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x31] = "LsarSetTrustedDomainInfoByName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x32] = "LsarEnumerateTrustedDomainsEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x33] = "LsarCreateTrustedDomainEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x34] = "LsarCloseTrustedDomainEx"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x35] = "LsarQueryDomainInformationPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x36] = "LsarSetDomainInformationPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x37] = "LsarOpenTrustedDomainByName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x38] = "LsarTestCall"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x39] = "LsarLookupSids2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3a] = "LsarLookupNames2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3b] = "LsarCreateTrustedDomainEx2"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3c] = "CredrWrite"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3d] = "CredrRead"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3e] = "CredrEnumerate"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x3f] = "CredrWriteDomainCredentials"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x40] = "CredrReadDomainCredentials"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x41] = "CredrDelete"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x42] = "CredrGetTargetInfo"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x43] = "CredrProfileLoaded"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x44] = "LsarLookupNames3"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x45] = "CredrGetSessionTypes"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x46] = "LsarRegisterAuditEvent"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x47] = "LsarGenAuditEvent"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x48] = "LsarUnregisterAuditEvent"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x49] = "LsarQueryForestTrustInformation"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4a] = "LsarSetForestTrustInformation"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4b] = "CredrRename"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4c] = "LsarLookupSids3"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4d] = "LsarLookupNames4"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4e] = "LsarOpenPolicySce"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x4f] = "LsarAdtRegisterSecurityEventSource"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x50] = "LsarAdtUnregisterSecurityEventSource"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x51] = "LsarAdtReportSecurityEvent"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x52] = "CredrFindBestCredential"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x53] = "LsarSetAuditPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x54] = "LsarQueryAuditPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x55] = "LsarEnumerateAuditPolicy"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x56] = "LsarEnumerateAuditCategories"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x57] = "LsarEnumerateAuditSubCategories"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x58] = "LsarLookupAuditCategoryName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x59] = "LsarLookupAuditSubCategoryName"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5a] = "LsarSetAuditSecurity"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5b] = "LsarQueryAuditSecurity"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5c] = "CredReadByTokenHandle"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5d] = "CredrRestoreCredentials"
opCodeMap["********-1234-abcd-ef00-0********9ab"][0x5e] = "CredrBackupCredentials"
opCodeMap["17fdd703-1827-4e34-79d4-24a55c53bb37"][0x00] = "NetrMessageNameAdd"
opCodeMap["17fdd703-1827-4e34-79d4-24a55c53bb37"][0x01] = "NetrMessageNameEnum"
opCodeMap["17fdd703-1827-4e34-79d4-24a55c53bb37"][0x02] = "NetrMessageNameGetInfo"
opCodeMap["17fdd703-1827-4e34-79d4-24a55c53bb37"][0x03] = "NetrMessageNameDel"
opCodeMap["5a7b91f8-ff00-11d0-a9b2-00c04fb6e6fc"][0x00] = "NetrSendMessage"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x00] = "PNP_Disconnect"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x01] = "PNP_Connect"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x02] = "PNP_GetVersion"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x03] = "PNP_GetGlobalState"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x04] = "PNP_InitDetection"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x05] = "PNP_ReportLogOn"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x06] = "PNP_ValidateDeviceInstance"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x07] = "PNP_GetRootDeviceInstance"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x08] = "PNP_GetRelatedDeviceInstance"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x09] = "PNP_EnumerateSubKeys"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x0a] = "PNP_GetDeviceList"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x0b] = "PNP_GetDeviceListSize"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x0c] = "PNP_GetDepth"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x0d] = "PNP_GetDeviceRegProp"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x0e] = "PNP_SetDeviceRegProp"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x0f] = "PNP_GetClassInstance"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x10] = "PNP_CreateKey"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x11] = "PNP_DeleteRegistryKey"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x12] = "PNP_GetClassCount"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x13] = "PNP_GetClassName"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x14] = "PNP_DeleteClassKey"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x15] = "PNP_GetInterfaceDeviceAlias"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x16] = "PNP_GetInterfaceDeviceList"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x17] = "PNP_GetInterfaceDeviceListSize"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x18] = "PNP_RegisterDeviceClassAssociation"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x19] = "PNP_UnregisterDeviceClassAssociation"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x1a] = "PNP_GetClassRegProp"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x1b] = "PNP_SetClassRegProp"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x1c] = "PNP_CreateDevInst"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x1d] = "PNP_DeviceInstanceAction"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x1e] = "PNP_GetDeviceStatus"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x1f] = "PNP_SetDeviceProblem"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x20] = "PNP_DisableDevInst"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x21] = "PNP_UninstallDevInst"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x22] = "PNP_AddID"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x23] = "PNP_RegisterDriver"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x24] = "PNP_QueryRemove"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x25] = "PNP_RequestDeviceEject"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x26] = "PNP_IsDockStationPresent"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x27] = "PNP_RequestEjectPC"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x28] = "PNP_HwProfFlags"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x29] = "PNP_GetHwProfInfo"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x2a] = "PNP_AddEmptyLogConf"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x2b] = "PNP_FreeLogConf"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x2c] = "PNP_GetFirstLogConf"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x2d] = "PNP_GetNextLogConf"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x2e] = "PNP_GetLogConfPriority"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x2f] = "PNP_AddResDes"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x30] = "PNP_FreeResDes"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x31] = "PNP_GetNextResDes"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x32] = "PNP_GetResDesData"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x33] = "PNP_GetResDesDataSize"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x34] = "PNP_ModifyResDes"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x35] = "PNP_DetectResourceConflict"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x36] = "PNP_QueryResConfList"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x37] = "PNP_SetHwProf"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x38] = "PNP_QueryArbitratorFreeData"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x39] = "PNP_QueryArbitratorFreeSize"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x3a] = "PNP_RunDetection"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x3b] = "PNP_RegisterNotification"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x3c] = "PNP_UnregisterNotification"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x3d] = "PNP_GetCustomDevProp"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x3e] = "PNP_GetVersionInternal"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x3f] = "PNP_GetBlockedDriverInfo"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x40] = "PNP_GetServerSideDeviceInstallFlags"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x41] = "PNP_GetObjectPropKeys"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x42] = "PNP_GetObjectProp"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x43] = "PNP_SetObjectProp"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x44] = "PNP_InstallDevInst"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x45] = "PNP_ApplyPowerSettings"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x46] = "PNP_DriverStoreAddDriverPackage"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x47] = "PNP_DriverStoreDeleteDriverPackage"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x48] = "PNP_RegisterServiceNotification"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x49] = "PNP_SetActiveService"
opCodeMap["8d9f4e40-a03d-11ce-8f69-08003e30051b"][0x4a] = "PNP_DeleteServiceDevices"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x00] = "DnssrvOperation"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x01] = "DnssrvQuery"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x02] = "DnssrvComplexOperation"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x03] = "DnssrvEnumRecords"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x04] = "DnssrvUpdateRecord"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x05] = "DnssrvOperation2"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x06] = "DnssrvQuery2"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x07] = "DnssrvComplexOperation2"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x08] = "DnssrvEnumRecords2"
opCodeMap["50abc2a4-574d-40b3-9d66-ee4fd5fba076"][0x09] = "DnssrvUpdateRecord2"
opCodeMap["57674cd0-5200-11ce-a897-08002b2e9c6d"][0x00] = "LlsrLicenseRequestW"
opCodeMap["57674cd0-5200-11ce-a897-08002b2e9c6d"][0x01] = "LlsrLicenseFree"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x00] = "LlsrConnect"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x01] = "LlsrClose"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x02] = "LlsrLicenseEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x03] = "LlsrLicenseEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x04] = "LlsrLicenseAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x05] = "LlsrLicenseAddA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x06] = "LlsrProductEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x07] = "LlsrProductEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x08] = "LlsrProductAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x09] = "LlsrProductAddA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x0a] = "LlsrProductUserEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x0b] = "LlsrProductUserEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x0c] = "LlsrProductServerEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x0d] = "LlsrProductServerEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x0e] = "LlsrProductLicenseEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x0f] = "LlsrProductLicenseEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x10] = "LlsrUserEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x11] = "LlsrUserEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x12] = "LlsrUserInfoGetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x13] = "LlsrUserInfoGetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x14] = "LlsrUserInfoSetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x15] = "LlsrUserInfoSetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x16] = "LlsrUserDeleteW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x17] = "LlsrUserDeleteA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x18] = "LlsrUserProductEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x19] = "LlsrUserProductEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x1a] = "LlsrUserProductDeleteW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x1b] = "LlsrUserProductDeleteA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x1c] = "LlsrMappingEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x1d] = "LlsrMappingEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x1e] = "LlsrMappingInfoGetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x1f] = "LlsrMappingInfoGetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x20] = "LlsrMappingInfoSetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x21] = "LlsrMappingInfoSetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x22] = "LlsrMappingUserEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x23] = "LlsrMappingUserEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x24] = "LlsrMappingUserAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x25] = "LlsrMappingUserAddA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x26] = "LlsrMappingUserDeleteW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x27] = "LlsrMappingUserDeleteA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x28] = "LlsrMappingAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x29] = "LlsrMappingAddA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x2a] = "LlsrMappingDeleteW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x2b] = "LlsrMappingDeleteA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x2c] = "LlsrServerEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x2d] = "LlsrServerEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x2e] = "LlsrServerProductEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x2f] = "LlsrServerProductEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x30] = "LlsrLocalProductEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x31] = "LlsrLocalProductEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x32] = "LlsrLocalProductInfoGetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x33] = "LlsrLocalProductInfoGetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x34] = "LlsrLocalProductInfoSetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x35] = "LlsrLocalProductInfoSetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x36] = "LlsrServiceInfoGetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x37] = "LlsrServiceInfoGetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x38] = "LlsrServiceInfoSetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x39] = "LlsrServiceInfoSetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x3a] = "LlsrReplConnect"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x3b] = "LlsrReplClose"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x3c] = "LlsrReplicationRequestW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x3d] = "LlsrReplicationServerAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x3e] = "LlsrReplicationServerServiceAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x3f] = "LlsrReplicationServiceAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x40] = "LlsrReplicationUserAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x41] = "LlsrProductSecurityGetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x42] = "LlsrProductSecurityGetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x43] = "LlsrProductSecuritySetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x44] = "LlsrProductSecuritySetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x45] = "LlsrProductLicensesGetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x46] = "LlsrProductLicensesGetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x47] = "LlsrCertificateClaimEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x48] = "LlsrCertificateClaimEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x49] = "LlsrCertificateClaimAddCheckA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x4a] = "LlsrCertificateClaimAddCheckW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x4b] = "LlsrCertificateClaimAddA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x4c] = "LlsrCertificateClaimAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x4d] = "LlsrReplicationCertDbAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x4e] = "LlsrReplicationProductSecurityAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x4f] = "LlsrReplicationUserAddExW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x50] = "LlsrCapabilityGet"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x51] = "LlsrLocalServiceEnumW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x52] = "LlsrLocalServiceEnumA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x53] = "LlsrLocalServiceAddA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x54] = "LlsrLocalServiceAddW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x55] = "LlsrLocalServiceInfoSetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x56] = "LlsrLocalServiceInfoSetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x57] = "LlsrLocalServiceInfoGetW"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x58] = "LlsrLocalServiceInfoGetA"
opCodeMap["342cfd40-3c6c-11ce-a893-08002b2e9c6d"][0x59] = "LlsrCloseEx"
opCodeMap["91ae6020-9e3c-11cf-8d7c-00aa00c091be"][0x00] = "CertServerRequest"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x00] = "NetrDfsManagerGetVersion"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x01] = "NetrDfsAdd"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x02] = "NetrDfsRemove"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x03] = "NetrDfsSetInfo"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x04] = "NetrDfsGetInfo"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x05] = "NetrDfsEnum"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x06] = "NetrDfsRename"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x07] = "NetrDfsMove"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x08] = "NetrDfsManagerGetConfigInfo"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x09] = "NetrDfsManagerSendSiteInfo"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x0a] = "NetrDfsAddFtRoot"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x0b] = "NetrDfsRemoveFtRoot"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x0c] = "NetrDfsAddStdRoot"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x0d] = "NetrDfsRemoveStdRoot"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x0e] = "NetrDfsManagerInitialize"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x0f] = "NetrDfsAddStdRootForced"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x10] = "NetrDfsGetDcAddress"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x11] = "NetrDfsSetDcAddress"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x12] = "NetrDfsFlushFtTable"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x13] = "NetrDfsAdd2"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x14] = "NetrDfsRemove2"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x15] = "NetrDfsEnumEx"
opCodeMap["4fc742e0-4a10-11cf-8273-00aa004ae673"][0x16] = "NetrDfsSetInfo2"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x00] = "SfcSrv_GetNextProtectedFile"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x01] = "SfcSrv_IsFileProtected"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x02] = "SfcSrv_FileException"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x03] = "SfcSrv_InitiateScan"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x04] = "SfcSrv_PurgeCache"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x05] = "SfcSrv_SetCacheSize"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x06] = "SfcSrv_SetDisable"
opCodeMap["83da7c00-e84f-11d2-9807-00c04f8ec850"][0x07] = "SfcSrv_InstallProtectedFiles"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x00] = "NDdeShareAddW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x01] = "NDdeShareDelA"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x02] = "NDdeShareDelW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x03] = "NDdeGetShareSecurityA"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x04] = "NDdeGetShareSecurityW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x05] = "NDdeSetShareSecurityA"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x06] = "NDdeSetShareSecurityW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x07] = "NDdeShareEnumA"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x08] = "NDdeShareEnumW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x09] = "NDdeShareGetInfoW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x0a] = "NDdeShareSetInfoW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x0b] = "NDdeSetTrustedShareA"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x0c] = "NDdeSetTrustedShareW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x0d] = "NDdeGetTrustedShareA"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x0e] = "NDdeGetTrustedShareW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x0f] = "NDdeTrustedShareEnumA"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x10] = "NDdeTrustedShareEnumW"
opCodeMap["2f5f3220-c126-1076-b549-074d078619da"][0x12] = "NDdeSpecialCommand"
opCodeMap["3dde7c30-165d-11d1-ab8f-00805f14db40"][0x00] = "bkrp_BackupKey"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x00] = "NetrWkstaGetInfo"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x01] = "NetrWkstaSetInfo"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x02] = "NetrWkstaUserEnum"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x03] = "NetrWkstaUserGetInfo"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x04] = "NetrWkstaUserSetInfo"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x05] = "NetrWkstaTransportEnum"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x06] = "NetrWkstaTransportAdd"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x07] = "NetrWkstaTransportDel"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x08] = "NetrUseAdd"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x09] = "NetrUseGetInfo"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x0a] = "NetrUseDel"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x0b] = "NetrUseEnum"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x0c] = "NetrMessageBufferSend"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x0d] = "NetrWorkstationStatisticsGet"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x0e] = "NetrLogonDomainNameAdd"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x0f] = "NetrLogonDomainNameDel"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x10] = "NetrJoinDomain"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x11] = "NetrUnjoinDomain"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x12] = "NetrValidateName"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x13] = "NetrRenameMachineInDomain"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x14] = "NetrGetJoinInformation"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x15] = "NetrGetJoinableOUs"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x16] = "NetrJoinDomain2"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x17] = "NetrUnjoinDomain2"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x18] = "NetrRenameMachineInDomain2"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x19] = "NetrValidateName2"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x1a] = "NetrGetJoinableOUs2"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x1b] = "NetrAddAlternateComputerName"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x1c] = "NetrRemoveAlternateComputerName"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x1d] = "NetrSetPrimaryComputerName"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x1e] = "NetrEnumerateComputerNames"
opCodeMap["6bffd098-a112-3610-9833-46c3f87e345a"][0x1f] = "NetrWorkstationResetDfsCache"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x00] = "ept_insert"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x01] = "ept_delete"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x02] = "ept_lookup"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x03] = "ept_map"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x04] = "ept_lookup_handle_free"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x05] = "ept_inq_object"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x06] = "ept_mgmt_delete"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x07] = "ept_map_auth"
opCodeMap["e1af8308-5d1f-11c9-91a4-08002b14a0fa"][0x08] = "ept_map_auth_async"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x00] = "EcDoConnect"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x01] = "EcDoDisconnect"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x02] = "EcDoRpc"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x03] = "EcGetMoreRpc"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x04] = "EcRRegisterPushNotification"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x05] = "EcRUnregisterPushNotification"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x06] = "EcDummyRpc"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x07] = "EcRGetDCName"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x08] = "EcRNetGetDCName"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x09] = "EcDoRpcExt"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x0a] = "EcDoConnectEx"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x0b] = "EcDoRpcExt2"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x0c] = "EcUnknown0xC"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x0d] = "EcUnknown0xD"
opCodeMap["a4f1db00-ca47-1067-b31f-00dd010662da"][0x0e] = "EcDoAsyncConnectEx"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x00] = "DRSBind"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x01] = "DRSUnbind"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x02] = "DRSReplicaSync"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x03] = "DRSGetNCChanges"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x04] = "DRSUpdateRefs"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x05] = "DRSReplicaAdd"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x06] = "DRSReplicaDel"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x07] = "DRSReplicaModify"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x08] = "DRSVerifyNames"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x09] = "DRSGetMemberships"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x0a] = "DRSInterDomainMove"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x0b] = "DRSGetNT4ChangeLog"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x0c] = "DRSCrackNames"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x0d] = "DRSWriteSPN"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x0e] = "DRSRemoveDsServer"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x0f] = "DRSRemoveDsDomain"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x10] = "DRSDomainControllerInfo"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x11] = "DRSAddEntry"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x12] = "DRSExecuteKCC"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x13] = "DRSGetReplInfo"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x14] = "DRSAddSidHistory"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x15] = "DRSGetMemberships2"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x16] = "DRSReplicaVerifyObjects"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x17] = "DRSGetObjectExistence"
opCodeMap["e3514235-4b06-11d1-ab04-00c04fc2dcd2"][0x18] = "DRSQuerySitesByCost"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x00] = "R_WinsRecordAction"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x01] = "R_WinsStatus"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x02] = "R_WinsTrigger"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x03] = "R_WinsDoStaticInit"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x04] = "R_WinsDoScavenging"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x05] = "R_WinsGetDbRecs"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x06] = "R_WinsTerm"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x07] = "R_WinsBackup"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x08] = "R_WinsDelDbRecs"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x09] = "R_WinsPullRange"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x0a] = "R_WinsSetPriorityClass"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x0b] = "R_WinsResetCounters"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x0c] = "R_WinsWorkerThdUpd"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x0d] = "R_WinsGetNameAndAdd"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x0e] = "R_WinsGetBrowserNames_Old"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x0f] = "R_WinsDeleteWins"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x10] = "R_WinsSetFlags"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x11] = "R_WinsGetDbRecsByName"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x12] = "R_WinsStatusWHdl"
opCodeMap["45f52c28-7f9f-101a-b52b-08002b2efabe"][0x13] = "R_WinsDoScavengingNew"
opCodeMap["afa8bd80-7d8a-11c9-bef4-08002b102989"][0x00] = "inq_if_ids"
opCodeMap["afa8bd80-7d8a-11c9-bef4-08002b102989"][0x01] = "inq_stats"
opCodeMap["afa8bd80-7d8a-11c9-bef4-08002b102989"][0x02] = "is_server_listening"
opCodeMap["afa8bd80-7d8a-11c9-bef4-08002b102989"][0x03] = "stop_server_listening"
opCodeMap["afa8bd80-7d8a-11c9-bef4-08002b102989"][0x04] = "inq_princ_name"

function dcerpcClassify(session, str, direction)
    if str:sub(1,3) == string.char(0x05, 0x00, 0x0b) then
    ctx = (string.format('%02X%02X%02X%02X', str:sub(33,36):reverse():byte(1,4)) .. "-" ..
            string.format('%02X%02X', str:sub(37,38):reverse():byte(1,2)) .. "-" ..
            string.format('%02X%02X', str:sub(39,40):reverse():byte(1,2)) .. "-" ..
            string.format('%02X%02X', str:sub(41,42):byte(1,2)) .. "-" ..
            string.format('%02X%02X%02X%02X%02X%02X', str:sub(43,48):byte(1,6))):lower()
    if endpointMap[ctx] ~= nil then
        session:add_string("dcerpc.api",endpointMap[ctx])
        tbl = session:table()
        tbl['ctx'] = ctx
        session:register_parser(parseDCERPC)
    end
    end
end

function parseDCERPC(session, str, direction)
    if str:sub(1,3) ~= string.char(0x05, 0x00, 0x00) then
      return 0
    end
    tbl = session:table()
    if tbl['ctx'] == nil then
      return 0
    end
    if opCodeMap[tbl['ctx']] == nil then
      return 0
    end
    if opCodeMap[tbl['ctx']][str:byte(23)] == nil then
      return 0
    end
    session:add_string("dcerpc.cmd",opCodeMap[tbl['ctx']][str:byte(23)])
    return 0
end

ArkimeSession.register_tcp_classifier("dcerpc", 0, string.char(0x05, 0x00), "dcerpcClassify")
