/* Copyright 2019 AOL Inc. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#include "arkime.h"

//#define UNKIPPROTODEBUG 1

extern ArkimeConfig_t        config;

LOCAL int unkIpProtocolMProtocol;

/******************************************************************************/
LOCAL void unkIpProtocol_create_sessionid(uint8_t *sessionId, ArkimePacket_t *const UNUSED (packet))
{
    // uint8_t *data = packet->pkt + packet->payloadOffset;

    sessionId[0] = 2;
    sessionId[1] = 0x9a;
    sessionId[2] = 0x9a;

    // for now, lump all unkIpProtocol into the same session
}
/******************************************************************************/
LOCAL int unkIpProtocol_pre_process(ArkimeSession_t *session, ArkimePacket_t *const UNUSED(packet), int isNewSession)
{
    if (isNewSession)
        arkime_session_add_protocol(session, "unkIpProtocol");

    return 0;
}
/******************************************************************************/
LOCAL int unkIpProtocol_process(ArkimeSession_t *UNUSED(session), ArkimePacket_t *const UNUSED(packet))
{
    return 1;
}
/******************************************************************************/
LOCAL ArkimePacketRC unkIpProtocol_packet_enqueue(ArkimePacketBatch_t *UNUSED(batch), ArkimePacket_t *const packet, const uint8_t *data, int len)
{
    uint8_t sessionId[ARKIME_SESSIONID_LEN];

    // no sanity checks

    packet->payloadOffset = data - packet->pkt;
    packet->payloadLen = len;

    unkIpProtocol_create_sessionid(sessionId, packet);

    packet->hash = arkime_session_hash(sessionId);
    packet->mProtocol = unkIpProtocolMProtocol;

    return ARKIME_PACKET_DO_PROCESS;
}
/******************************************************************************/
void arkime_plugin_init()
{
    arkime_packet_set_ip_cb(ARKIME_IPPROTO_UNKNOWN, unkIpProtocol_packet_enqueue);
    unkIpProtocolMProtocol = arkime_mprotocol_register("unkIpProtocol",
                                                       SESSION_OTHER,
                                                       unkIpProtocol_create_sessionid,
                                                       unkIpProtocol_pre_process,
                                                       unkIpProtocol_process,
                                                       NULL);
}
