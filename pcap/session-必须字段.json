// session 必须字段
{
  "index": {
    "_index": "arkime_sessions3-021231"
  }
} //arkime_sessions3-{YYMMDD}
{
  "@timestamp": 1752149929057, //毫秒时间戳，处理时间
  "firstPacket": 1041342931300, // session第一包时间戳
  "lastPacket": 1041342932300, // session最后一包包时间戳
  "length": 1000, // 会话持续时间(毫秒)
  "ipProtocol": 17, // 6=TCP
  "node": "localhost", // 捕获节点名称
  //源和目标信息
  "source": {
    "ip": "*******",
    "port": 3267,
    "bytes": 207,
    "packets": 1,
    "mac": [
      "00:09:6b:88:f5:c9"
    ]
  },
  "destination": {
    "ip": "*******",
    "port": 2000,
    "bytes": 0,
    "packets": 0,
    "mac-cnt": 1,
    "mac": [
      "00:e0:81:00:b0:28"
    ]
  },
  //网络统计
  "network": {
    "packets": 1,
    "bytes": 207
  },
  "packetPos": [
    -1, // -{fileId}
    24 // 数据包在PCAP文件中的位置
  ],
  "fileId": [
    1 //对应在arkime_files_v30索引中的id
  ],
  "protocols": [
    "udp"
  ]
  "http":[
    
  ]
}
//file必须字段

PUT http: //localhost:9200/arkime_files_v30/_doc/localhost-1    //{nodename}-{fileid}
{
  "num": 1, //file_id
  "name": "/opt/arkime/raw/test-http.pcap", //在/opt/arkime目录下的，能够访问到的文件路径
  "first": 1752150698995, //首包时间戳
  "node": "localhost", // 捕获节点名称
  "filesize": 247, //文件大小名称
  "locked": 0 //文件处理中为1 处理完成为0
}
// 添加字段
{
  "index": {
    "_index": "arkime_fields_v30",
    "_id": "http.method"  //过滤器写法
  }
}
{
  "friendlyName": "Method",      //显示名称
  "group": "http",               //组信息，其中每个组会将字段显示到一个框体内
  "help": "HTTP request method", //帮助信息
  "dbField2": "http.method",     //
  "type": "termfield",           //类型
}