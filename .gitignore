# OS & Editor directories and files
*.so
*.o
*.dSYM
*.val
.*.swp
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
.DS_Store

# dependencies
node_modules

# npm
npm-debug.log*

# capture
capture/molochconfig.h
capture/molochconfig.h.in
capture/arkimeconfig.h
capture/arkimeconfig.h.in
capture/moloch-capture
capture/capture


# tests
tests/GeoLite2*
tests/oui.txt
tests/lmdbCache

# Require force update
tests/cont3xt.ini
tests/test.config.ini
tests/test.config.json

# viewer
viewer/public/CyberChef*.zip
viewer/vueapp/dist

# parliament
parliament/issues.json
parliament/parliament.json
parliament/parliament.dev.json
parliament/parliament.issues.json
parliament/parliament.dev.issues.json
parliament/vueapp/dist
tests/parliament.dev.issues.json

# wiseservice
wiseService/vueapp/dist

# cont3xt
cont3xt/vueapp/dist

# common
common/version.js

# Makefiles
Makefile
Makefile.in

# configs
config.guess
config.sub
config.log
config.status
config.dev.ini

# misc
aclocal.m4
missing
install-sh
compile
configure
GeoIP.dat
GeoIPASNum.dat
GeoIPASNumv6.dat
GeoIPv6.dat
ipv4-address-space.csv
autom4te.cache
stamp-h1

thirdparty/*
npm_offline_packages