#!/bin/bash
# npm离线安装测试脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_DIR="/tmp/arkime_npm_test_$(date +%s)"
NPM_OFFLINE_DIR="${SCRIPT_DIR}/npm_offline_packages"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 显示测试横幅
show_banner() {
    cat << 'EOF'
    
    ╔═══════════════════════════════════════════════════════════╗
    ║                                                           ║
    ║              npm离线安装测试工具                          ║
    ║                                                           ║
    ║         验证npm离线包的完整性和安装功能                   ║
    ║                                                           ║
    ╚═══════════════════════════════════════════════════════════╝
    
EOF
}

# 清理测试环境
cleanup() {
    if [[ -d "$TEST_DIR" ]]; then
        log_info "清理测试环境: $TEST_DIR"
        rm -rf "$TEST_DIR"
    fi
}

# 设置清理陷阱
trap cleanup EXIT

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        exit 1
    fi
    
    local node_version=$(node --version)
    log_info "Node.js版本: $node_version"
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        exit 1
    fi
    
    local npm_version=$(npm --version)
    log_info "npm版本: $npm_version"
    
    # 检查离线包目录
    if [[ ! -d "$NPM_OFFLINE_DIR" ]]; then
        log_error "npm离线包目录不存在: $NPM_OFFLINE_DIR"
        log_info "请先运行: ./download_npm_packages_offline.sh"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 测试离线包完整性
test_offline_package_integrity() {
    log_info "测试离线包完整性..."
    
    # 检查必要文件
    local required_files=(
        "install_npm_offline.sh"
        "node_modules_production.tar.gz"
        "node_modules_full.tar.gz"
        "package.json"
        "package-lock.json"
        ".npmrc"
        "README.md"
    )
    
    for file in "${required_files[@]}"; do
        if [[ -f "${NPM_OFFLINE_DIR}/$file" ]]; then
            log_success "文件存在: $file"
        else
            log_error "文件缺失: $file"
            return 1
        fi
    done
    
    # 检查目录
    local required_dirs=(
        ".npm_cache"
        "tarballs"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ -d "${NPM_OFFLINE_DIR}/$dir" ]]; then
            log_success "目录存在: $dir"
        else
            log_error "目录缺失: $dir"
            return 1
        fi
    done
    
    # 检查tarball数量
    local tarball_count=$(find "${NPM_OFFLINE_DIR}/tarballs" -name "*.tgz" | wc -l)
    log_info "tarball文件数量: $tarball_count"
    
    if [[ $tarball_count -gt 0 ]]; then
        log_success "tarball文件检查通过"
    else
        log_error "没有找到tarball文件"
        return 1
    fi
    
    log_success "离线包完整性检查通过"
}

# 测试生产环境安装
test_production_install() {
    log_info "测试生产环境npm包安装..."
    
    local test_prod_dir="${TEST_DIR}/production"
    mkdir -p "$test_prod_dir"
    
    # 使用安装脚本
    if "${NPM_OFFLINE_DIR}/install_npm_offline.sh" "$test_prod_dir" "production"; then
        log_success "生产环境安装脚本执行成功"
    else
        log_error "生产环境安装脚本执行失败"
        return 1
    fi
    
    # 检查node_modules目录
    if [[ -d "${test_prod_dir}/node_modules" ]]; then
        local package_count=$(find "${test_prod_dir}/node_modules" -maxdepth 1 -type d | wc -l)
        log_info "安装的包数量: $((package_count - 1))"  # 减去node_modules本身
        log_success "node_modules目录创建成功"
    else
        log_error "node_modules目录未创建"
        return 1
    fi
    
    # 检查关键依赖包
    local key_packages=("express" "vue" "axios" "moment")
    for package in "${key_packages[@]}"; do
        if [[ -d "${test_prod_dir}/node_modules/$package" ]]; then
            log_success "关键包存在: $package"
        else
            log_warning "关键包缺失: $package"
        fi
    done
    
    log_success "生产环境安装测试通过"
}

# 测试完整安装
test_full_install() {
    log_info "测试完整npm包安装..."
    
    local test_full_dir="${TEST_DIR}/full"
    mkdir -p "$test_full_dir"
    
    # 使用安装脚本
    if "${NPM_OFFLINE_DIR}/install_npm_offline.sh" "$test_full_dir" "full"; then
        log_success "完整安装脚本执行成功"
    else
        log_error "完整安装脚本执行失败"
        return 1
    fi
    
    # 检查node_modules目录
    if [[ -d "${test_full_dir}/node_modules" ]]; then
        local package_count=$(find "${test_full_dir}/node_modules" -maxdepth 1 -type d | wc -l)
        log_info "安装的包数量: $((package_count - 1))"
        log_success "node_modules目录创建成功"
    else
        log_error "node_modules目录未创建"
        return 1
    fi
    
    # 检查开发依赖包
    local dev_packages=("eslint" "jest" "webpack")
    for package in "${dev_packages[@]}"; do
        if [[ -d "${test_full_dir}/node_modules/$package" ]]; then
            log_success "开发包存在: $package"
        else
            log_warning "开发包缺失: $package"
        fi
    done
    
    log_success "完整安装测试通过"
}

# 测试手动解压安装
test_manual_extract() {
    log_info "测试手动解压安装..."
    
    local test_extract_dir="${TEST_DIR}/extract"
    mkdir -p "$test_extract_dir"
    
    # 解压生产环境包
    if tar -xzf "${NPM_OFFLINE_DIR}/node_modules_production.tar.gz" -C "$test_extract_dir"; then
        log_success "生产环境包解压成功"
    else
        log_error "生产环境包解压失败"
        return 1
    fi
    
    # 检查解压结果
    if [[ -d "${test_extract_dir}/node_modules" ]]; then
        local package_count=$(find "${test_extract_dir}/node_modules" -maxdepth 1 -type d | wc -l)
        log_info "解压的包数量: $((package_count - 1))"
        log_success "手动解压测试通过"
    else
        log_error "解压后node_modules目录不存在"
        return 1
    fi
}

# 测试Makefile集成
test_makefile_integration() {
    log_info "测试Makefile集成..."
    
    # 检查Makefile是否存在
    if [[ ! -f "${SCRIPT_DIR}/Makefile" ]]; then
        log_warning "Makefile不存在，跳过集成测试"
        return 0
    fi
    
    # 检查Makefile中的离线安装逻辑
    if grep -q "npm_offline_packages" "${SCRIPT_DIR}/Makefile"; then
        log_success "Makefile包含离线安装逻辑"
    else
        log_error "Makefile未包含离线安装逻辑"
        return 1
    fi
    
    # 模拟make check-local
    local test_make_dir="${TEST_DIR}/make_test"
    mkdir -p "$test_make_dir"
    
    # 复制必要文件
    cp "${SCRIPT_DIR}/package.json" "$test_make_dir/" 2>/dev/null || true
    cp "${SCRIPT_DIR}/package-lock.json" "$test_make_dir/" 2>/dev/null || true
    cp -r "${NPM_OFFLINE_DIR}" "$test_make_dir/" 2>/dev/null || true
    
    cd "$test_make_dir"
    
    # 测试离线安装逻辑
    if [[ -d "npm_offline_packages" ]] && [[ -f "npm_offline_packages/install_npm_offline.sh" ]]; then
        if npm_offline_packages/install_npm_offline.sh . full; then
            log_success "Makefile集成测试通过"
        else
            log_error "Makefile集成测试失败"
            return 1
        fi
    else
        log_warning "离线包不完整，跳过Makefile集成测试"
    fi
    
    cd "$SCRIPT_DIR"
}

# 性能测试
test_performance() {
    log_info "进行性能测试..."
    
    local test_perf_dir="${TEST_DIR}/performance"
    mkdir -p "$test_perf_dir"
    
    # 测试安装时间
    log_info "测试离线安装时间..."
    local start_time=$(date +%s)
    
    "${NPM_OFFLINE_DIR}/install_npm_offline.sh" "$test_perf_dir" "production" >/dev/null 2>&1
    
    local end_time=$(date +%s)
    local install_time=$((end_time - start_time))
    
    log_info "离线安装耗时: ${install_time}秒"
    
    # 检查安装大小
    if [[ -d "${test_perf_dir}/node_modules" ]]; then
        local install_size=$(du -sh "${test_perf_dir}/node_modules" | cut -f1)
        log_info "安装后大小: $install_size"
    fi
    
    log_success "性能测试完成"
}

# 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    local report_file="${SCRIPT_DIR}/npm_offline_test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
npm离线安装测试报告
生成时间: $(date)
测试环境: $(uname -a)
Node.js版本: $(node --version)
npm版本: $(npm --version)

=== 测试结果摘要 ===
✓ 离线包完整性检查
✓ 生产环境安装测试
✓ 完整安装测试
✓ 手动解压测试
✓ Makefile集成测试
✓ 性能测试

=== 离线包信息 ===
位置: $NPM_OFFLINE_DIR
大小: $(du -sh "$NPM_OFFLINE_DIR" | cut -f1)
tarball数量: $(find "${NPM_OFFLINE_DIR}/tarballs" -name "*.tgz" | wc -l)

=== 建议 ===
1. 离线包功能正常，可以用于生产部署
2. 确保目标环境Node.js版本匹配
3. 定期更新离线包以获取最新依赖

EOF
    
    echo "测试报告已生成: $report_file"
    cat "$report_file"
}

# 主测试流程
main() {
    show_banner
    
    log_info "开始npm离线安装测试..."
    
    # 创建测试目录
    mkdir -p "$TEST_DIR"
    log_info "测试目录: $TEST_DIR"
    
    # 执行测试
    check_prerequisites
    test_offline_package_integrity
    test_production_install
    test_full_install
    test_manual_extract
    test_makefile_integration
    test_performance
    
    # 生成报告
    generate_test_report
    
    log_success "npm离线安装测试完成!"
    
    echo ""
    echo "=== 测试总结 ==="
    echo "✓ 所有测试项目均通过"
    echo "✓ npm离线安装功能正常"
    echo "✓ 可以用于生产环境部署"
    echo ""
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
